import React from "react";
import { Redirect } from "react-router";
import { useAppSelector } from "../../../redux/hooks";
import ObjectGeometryForm from "./ObjectGeometryForm";
import Loading from "../../Loading";
import { FormProvider } from "../../../pages/Api/usage-pages/components/FormContext";
const ObjectGeometry = () => {
  const { loading, result } = useAppSelector((state) => state.objectGeometry);

  if (loading) {
    return <Loading />;
  }
  return (
    <FormProvider>
      {Object.keys(result).length > 0 ? (
        <Redirect
          to={{
            pathname: "/object-geometry-result",
          }}
        />
      ) : (
        <ObjectGeometryForm />
      )}
    </FormProvider>
  );
};

export default ObjectGeometry;
