/**
 * Admin module
 * @module adminController
 */
import { NextFunction, Request, Response } from "express";
import AppError from "../utils/appError";
import { AdminService } from "../services/admin/adminService";
import { AnalyticsService } from "../services/admin/analyticsService";
import { AnalyticsLoggingService } from "../services/admin/analyticsLoggingService";
import type Strip<PERSON> from "stripe";
import { stripe } from "./stripeController";
import { deleteContact } from "../utils/sendgridContactHandler";
import { User } from "../@types";
import pool from "../db";
import AWS from "aws-sdk";
import { AuditService } from "../services/admin/auditService";
import fetchApiUsage from "../utils/fetchApiUsage";
import { fetchUsageByTime } from "../utils/fetchUsageTimeRange";
import getTimeBoundaries from "../utils/timeBoundries";
import { BlockedUserService } from "../services/admin/blockedUserService";
import { fetchAllAnalytics } from "../utils/fetchAnalytics";
import { PaginatedResponse } from "../utils/pagination";

if (!process.env.AWS_ACCESS_KEY || !process.env.AWS_SECRET_ACCESS_KEY) {
  console.error("AWS credentials are not set in environment variables");
  throw new Error("AWS credentials are required");
}

AWS.config.update({
  region: "eu-west-1",
  credentials: new AWS.Credentials(
    process.env.AWS_ACCESS_KEY,
    process.env.AWS_SECRET_ACCESS_KEY,
  ),
});

const apiGateway = new AWS.APIGateway({
  accessKeyId: process.env.AWS_ACCESS_KEY,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  endpoint: "https://apigateway.eu-west-1.amazonaws.com",
  apiVersion: "2015-07-09",
});

export const getAllApiUsageAnalytics = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { startDate, endDate } = req.query;

    if (!startDate || !endDate) {
      return next(new AppError("Start and end dates are required", 400));
    }

    const usage = await fetchAllAnalytics(
      startDate as string,
      endDate as string,
    );

    res.status(200).json({
      status: "success",
      data: usage,
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const getUsers = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { search, active, page, limit, sortBy, sortOrder } = req.query;
    const activeFilter = active ? active === "true" : undefined;

    const users = await AdminService.getUsers(
      {
        page: page ? parseInt(page as string) : undefined,
        limit: limit ? parseInt(limit as string) : undefined,
        sortBy: sortBy as string,
        sortOrder: sortOrder as "ASC" | "DESC",
      },
      search as string,
      activeFilter,
    );

    const response = {
      status: "success",
      data: users,
    };

    res.status(200).json(response);
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const addTestCredit = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { userId } = req.params;
    const { testCredit } = req.body;

    await AdminService.addTestCredit(userId, testCredit);

    res.status(200).json({
      status: "success",
      message: `Successfully added ${testCredit} test credits to user ${userId}`,
    });
  } catch (error: any) {
    next(new AppError(error.message, error.statusCode || 500, false));
  }
};

export const getUserById = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { userId } = req.params;
    const user = await AdminService.getUserById(userId);

    if (!user) {
      next(new AppError("User not found", 404, true));
      return;
    }

    res.status(200).json({
      status: "success",
      user: user,
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const getMopsusAnalytics = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { timeframe = "30d" } = req.query;
    const results = await AnalyticsService.getMopsusAnalytics(
      timeframe as string,
    );
    return res.status(200).json(results);
  } catch (err: any) {
    next(new AppError(err.message, 500, false));
  }
};

export const editUser = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { userId } = req.params;
    const updates = req.body;

    const existingUser = await AdminService.getUserById(userId);
    if (!existingUser) {
      next(new AppError("User not found", 404));
      return;
    }

    if (updates.email) {
      const emailExists = await AdminService.getUserByEmail(updates.email);
      if (emailExists && emailExists.user_id !== userId) {
        next(new AppError("Email already exists", 400));
        return;
      }
    }

    const user = await AdminService.editUser(userId, updates);

    res.status(200).json({
      status: "success",
      user,
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const getMopsusActiveUsersChart = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { start, end } = req.query;
    const results = await AnalyticsService.getActiveUsersLineChart(
      start as string,
      end as string,
    );
    return res.status(200).json(results);
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const getMopsusNewUsersChart = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { start, end } = req.query;
    const results = await AnalyticsService.getNewUsersBarChart(
      start as string,
      end as string,
    );
    return res.status(200).json(results);
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const getMopsusPropertyGenerationsChart = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { start, end } = req.query;
    const results = await AnalyticsService.getPropertyGenerationsBarChart(
      start as string,
      end as string,
    );
    return res.status(200).json(results);
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const getUserApiUsage = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { userId } = req.params;

    const user = await AdminService.getUserById(userId);
    if (!user) {
      return next(new AppError("User not found", 404));
    }

    if (!user.api_key) {
      return next(new AppError("User does not have an API key", 400));
    }

    const planChangedAt = new Date(user.plan_changed_at);
    const now = new Date();
    const msSincePlanChange = now.getTime() - planChangedAt.getTime();
    const minutesSincePlanChange = Math.round(msSincePlanChange / 60000);

    const {
      lastWeekBeginning,
      thisWeekBeginning,
      yesterdayBeginning,
      todayBeginning,
      rightNow,
    } = getTimeBoundaries();

    const [
      usagePlan,
      usageData,
      usageYesterday,
      usageToday,
      usageLastWeek,
      usageThisWeek,
    ] = await Promise.all([
      AdminService.getUsagePlan(user.current_usage_plan),
      fetchApiUsage(user.api_key, minutesSincePlanChange),
      fetchUsageByTime(user.api_key, yesterdayBeginning, todayBeginning),
      fetchUsageByTime(user.api_key, todayBeginning, rightNow),
      fetchUsageByTime(user.api_key, lastWeekBeginning, thisWeekBeginning),
      fetchUsageByTime(user.api_key, thisWeekBeginning, rightNow),
    ]);

    const apiDetails = await Promise.all(
      usagePlan.apiStages?.map(async (stage: any) => {
        try {
          const apiDetails = await apiGateway
            .getRestApi({
              restApiId: stage.apiId,
            })
            .promise();

          return {
            id: stage.apiId,
            name: apiDetails.name,
            stage: stage.stage,
          };
        } catch (error) {
          console.error(`Failed to get API ${stage.apiId}:`, error);
          return {
            id: stage.apiId,
            name: "Unknown API",
            stage: stage.stage,
          };
        }
      }) || [],
    );

    const allowedApiNames = apiDetails.map((d) => d.name);

    res.status(200).json({
      status: "success",
      subscription: user.current_usage_plan !== "812sc7",
      usage: usageData,
      usageToday,
      usageYesterday,
      usageLastWeek,
      usageThisWeek,
      usagePlan: {
        name: usagePlan.name,
        quota: usagePlan.quota != null ? usagePlan.quota.limit : null,
      },
      allowedApiNames,
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const createUser = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = await AdminService.createUser(req.body);
    res.status(201).json({
      status: "success",
      user: user.user,
      message: user.message,
    });
  } catch (error: any) {
    next(error);
  }
};

export const getUsagePlans = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { page, limit, sortBy, sortOrder } = req.query;
    const plans = await AdminService.getAllUsagePlans({
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined,
      sortBy: sortBy as string,
      sortOrder: sortOrder as "ASC" | "DESC",
    });
    res.status(200).json({
      status: "success",
      data: plans,
    });
  } catch (error: any) {
    next(error);
  }
};

export const getUsagePlan = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    let plan = await AdminService.getUsagePlan(req.params.planId);

    if (plan && plan.apiStages) {
      const apiPromises = plan.apiStages.map(async (stage: any) => {
        try {
          const apiDetails = await apiGateway
            .getRestApi({
              restApiId: stage.apiId,
            })
            .promise();

          return {
            ...stage,
            apiName: apiDetails.name,
          };
        } catch (error) {
          console.error(
            `Failed to fetch API details for ${stage.apiId}:`,
            error,
          );
          return {
            ...stage,
            apiName: "Unknown API",
          };
        }
      });

      const apiStagesWithNames = await Promise.all(apiPromises);
      plan = {
        ...plan,
        apiStages: apiStagesWithNames,
      };
    }

    res.status(200).json({
      status: "success",
      data: plan,
    });
  } catch (error: any) {
    next(error);
  }
};

export const createUsagePlan = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const plan = await AdminService.createUsagePlan(req.body);
    res.status(201).json({
      status: "success",
      data: plan,
    });
  } catch (error: any) {
    next(error);
  }
};

export const updateUsagePlan = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const plan = await AdminService.updateUsagePlan(
      req.params.planId,
      req.body,
    );
    res.status(200).json({
      status: "success",
      data: plan,
    });
  } catch (error: any) {
    next(error);
  }
};

export const deleteUsagePlan = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    await AdminService.deleteUsagePlan(req.params.planId);
    res.status(204).json({
      status: "success",
      data: null,
    });
  } catch (error: any) {
    next(error);
  }
};

export const getApiUsageOverview = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { startDate, endDate } = req.query;
    const apiKey = req.headers["x-api-key"] as string;

    if (!apiKey) {
      return next(new AppError("API key is required", 400));
    }

    if (!startDate || !endDate) {
      return next(new AppError("Start and end dates are required", 400));
    }

    const usage = await AdminService.getApiUsageOverview(
      apiKey,
      startDate as string,
      endDate as string,
    );
    res.status(200).json({
      status: "success",
      usage,
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const blockEmail = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { email, reason } = req.body;

    if (!email) {
      return next(new AppError("Email is required", 400));
    }

    await BlockedUserService.blockEmail(email, reason);

    res.status(200).json({
      status: "success",
      message: `Email ${email} has been blocked`,
    });
  } catch (error: any) {
    next(new AppError(error.message, error.statusCode || 500, false));
  }
};

export const unblockEmail = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { email } = req.params;

    if (!email) {
      return next(new AppError("Email is required", 400));
    }

    await BlockedUserService.unblockEmail(email);

    res.status(200).json({
      status: "success",
      message: `Email ${email} has been unblocked`,
    });
  } catch (error: any) {
    next(new AppError(error.message, error.statusCode || 500, false));
  }
};

export const getBlockedEmails = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { page, limit } = req.query;
    const blockedEmails = await BlockedUserService.getBlockedEmails(
      page ? parseInt(page as string) : undefined,
      limit ? parseInt(limit as string) : undefined,
    );

    res.status(200).json({
      status: "success",
      data: blockedEmails,
    });
  } catch (error: any) {
    next(new AppError(error.message, error.statusCode || 500, false));
  }
};

export const deleteUser = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { userId } = req.params;
    const user = await AdminService.getUserById(userId);
    if (!user) {
      next(new AppError("User not found", 404));
      return;
    }

    if (user.stripe_customer_id) {
      try {
        await stripe.customers.del(user.stripe_customer_id);
      } catch (err) {
        console.error("Error deleting Stripe customer:", err);
      }
    }
    await deleteContact(user);
    await AdminService.deleteUser({
      userId: userId,
      email: user.email,
    });

    res.status(204).json({
      status: "success",
    });
  } catch (error: any) {
    console.log(error);
    next(new AppError(error.message, error.statusCode || 500, false));
  }
};

export const migrateLegacyUser = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = await AdminService.migrateLegacyUser(req.body);
    res.status(201).json({
      status: "success",
      user: user.user,
      message: user.message,
    });
  } catch (error: any) {
    next(error);
  }
};

export const getDetailedAnalytics = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { timeframe = "30d", type } = req.query;

    let results;
    switch (type) {
      case "active-users":
        results = await AnalyticsLoggingService.getActiveUsersMetrics(
          timeframe as string,
        );
        break;
      case "new-users":
        results = await AnalyticsLoggingService.getNewUsersMetrics(
          timeframe as string,
        );
        break;
      case "property-generations":
        results = await AnalyticsLoggingService.getPropertyGenerationMetrics(
          timeframe as string,
        );
        break;
      case "onboarding-funnel":
        results = await AnalyticsLoggingService.getOnboardingFunnelMetrics(
          timeframe as string,
        );
        break;
      default:
        return next(new AppError("Invalid analytics type specified", 400));
    }

    res.status(200).json({
      status: "success",
      data: results,
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const logAnalyticsEvent = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { event_type, metadata } = req.body;
    const user = req.user as User;

    const event = await AnalyticsLoggingService.logEvent({
      event_type,
      user_id: user?.user_id,
      metadata,
    });

    res.status(200).json({
      status: "success",
      data: event,
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};
