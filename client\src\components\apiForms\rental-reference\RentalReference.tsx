import React from "react";
import { Redirect } from "react-router";
import { useAppSelector } from "../../../redux/hooks";
import Loading from "../../Loading";
import RentalReferenceForm from "./RentalReferenceForm";
import { FormProvider } from "../../../pages/Api/usage-pages/components/FormContext";

const RentalReference = () => {
  const { loading, result } = useAppSelector((state) => state.rentalreference);
  if (loading) {
    return <Loading />;
  }
  return (
    <FormProvider>
      {Object.keys(result).length > 0 ? (
        <Redirect
          to={{
            pathname: "/rentalreference-result",
          }}
        />
      ) : (
        <RentalReferenceForm />
      )}
    </FormProvider>
  );
};

export default RentalReference;
