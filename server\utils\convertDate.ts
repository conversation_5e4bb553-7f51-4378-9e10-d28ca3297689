export function convertDate(dateStr: string): string {
  // Split the input date string
  const [day, month, year] = dateStr.split("-").map(Number);

  // Create a Date object
  const date = new Date(year, month - 1, day);

  // Options for formatting the date
  const options: Intl.DateTimeFormatOptions = {
    month: "short",
    day: "numeric",
  };

  // Format the date to "Month day"
  const formattedDate = date.toLocaleDateString("en-US", options);

  return formattedDate;
}
