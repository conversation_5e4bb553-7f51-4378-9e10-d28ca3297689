import { body, validationResult } from "express-validator";
import { Request, Response, NextFunction } from "express";

export const validateGenerateDescription = [
  body("propertyDetails")
    .optional()
    .isObject()
    .withMessage("Property details must be an object"),
  body("propertyDetails.yearOfConstruction")
    .optional()
    .isString()
    .withMessage("Year of construction must be a string"),
  body("propertyDetails.location")
    .optional()
    .isString()
    .withMessage("Location must be a string"),
  body("propertyDetails.area")
    .optional()
    .isString()
    .withMessage("Area must be a string"),
  body("propertyDetails.propertyType")
    .optional()
    .isString()
    .withMessage("Property type must be a string"),
  body("propertyDetails.facilities")
    .optional()
    .isString()
    .withMessage("Facilities must be a string"),

  body("configureOutput")
    .optional()
    .isObject()
    .withMessage("Configure output must be an object"),
  body("configureOutput.tone")
    .optional()
    .isString()
    .withMessage("Tone must be a string"),
  body("configureOutput.targetAudience")
    .optional()
    .isString()
    .withMessage("Target audience must be a string"),
  body("configureOutput.languagePreference")
    .optional()
    .isString()
    .withMessage("Language preference must be a string"),
  body("configureOutput.descriptionLength")
    .optional()
    .isString()
    .withMessage("Description length must be a string"),

  body("additionalNotes")
    .optional()
    .isString()
    .withMessage("Additional notes must be a string"),

  body("images")
    .optional()
    .custom((value, { req }) => {
      if (req.files && req.files.length > 0) {
        const allowedMimeTypes = ["image/jpeg", "image/png", "image/gif"];
        req.files.forEach((file: Express.Multer.File) => {
          if (!allowedMimeTypes.includes(file.mimetype)) {
            throw new Error(
              "Invalid file type. Only JPEG, PNG, and GIF are allowed",
            );
          }
          if (file.size > 5 * 1024 * 1024) {
            // 5MB limit
            throw new Error("File size exceeds 5MB limit");
          }
        });
      }
      return true;
    }),

  (req: Request, res: Response, next: NextFunction) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    next();
  },
];
