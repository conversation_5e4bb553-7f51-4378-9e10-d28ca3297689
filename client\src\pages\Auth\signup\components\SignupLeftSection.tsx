import {
  ChangeEvent,
  FormEvent,
  useCallback,
  useEffect,
  useState,
} from "react";
import { CiLock, CiMail, CiUser } from "react-icons/ci";
import TextInput from "../../../../components/TextInput";
import DividerWithText from "../../../../components/DividerWithText";
import Social from "../../components/Social";
import { Link, useHistory } from "react-router-dom";
import AuthForm from "../../components/AuthForm";
import { useAppDispatch, useAppSelector } from "../../../../redux/hooks";
import { signUp } from "../../../../redux/actions/authActions";
import { useValidation } from "../../../../hooks/useValidator";
import ReviewBadge from "./ReviewBadge";
import Text from "../../../../components/Text";
import AltumLogo from "../../../../assets/images/Logo-AltumAI.png";

interface AuthFormData {
  fullName: string;
  email: string;
  password: string;
}

const SignupLeftSection = () => {
  const history = useHistory();
  const dispatch = useAppDispatch();
  const { isAuthenticated, id, loading } = useAppSelector(
    (state) => state.auth,
  );
  const [formData, setFormData] = useState<AuthFormData>({
    fullName: "",
    email: "",
    password: "",
  });

  const [touchedFields, setTouchedFields] = useState<{
    [key: string]: boolean;
  }>({});
  const { errors, validate } = useValidation();

  useEffect(() => {
    if (isAuthenticated) {
      history.push("/dashboard/startpagina");
    }
  }, [isAuthenticated, history]);

  useEffect(() => {
    if (id.length > 0) {
      dispatch({ type: "auth/clearResults" });
      history.push(`/verify-email?email=${formData.email}`);
    }
  }, [dispatch, formData.email, history, id.length]);

  const handleBlur = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      const { name } = e.target;
      setTouchedFields((prev) => ({ ...prev, [name]: true }));
      validate(formData);
    },
    [formData, validate],
  );

  const handleChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setFormData((prevData) => ({ ...prevData, [name]: value }));
      if (touchedFields[name]) {
        validate({ ...formData, [name]: value });
      }
    },
    [touchedFields, formData, validate],
  );

  const handleSubmit = useCallback(
    (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      const errors = validate(formData);
      if (!errors["email"] && !errors["password"] && !errors["fullName"]) {
        dispatch(signUp(formData));
      }
    },
    [dispatch, formData, validate],
  );

  return (
    <div className="w-full md:w-3/5 p-5 flex flex-col justify-between">
      <div className="max-w-md mx-auto w-full flex flex-col items-center">
        <img src={AltumLogo} alt="Altum AI" className="w-48 mt-8 mb-4" />

        <div className="text-center mb-4">
          <h1 className="text-2xl font-semibold text-gray-800">
            Maak een gratis account aan
          </h1>
          <p className="text-gray-600 mt-2 text-sm">
            Ga probleemloos aan de slag met 15 gratis credits—geen
            betalingsgegevens nodig! Plus, geniet van gratis ondersteuning voor
            al je vragen
          </p>
        </div>

        <Social />

        <DividerWithText
          className="md:max-w-[424px] w-full my-4"
          text="of ga verder met e-mail"
        />

        <AuthForm
          cta="Doorgaan"
          onSubmit={handleSubmit}
          isLoading={loading}
          isError={
            !!errors["email"] || !!errors["password"] || !!errors["fullName"]
          }
        >
          <TextInput
            type="text"
            name="fullName"
            placeholder="Volledige naam"
            className="mt-3"
            icon={CiUser}
            message={touchedFields.fullName ? errors["fullName"] : ""}
            onChange={handleChange}
            onBlur={handleBlur}
          />
          <TextInput
            type="email"
            name="email"
            placeholder="E-mail"
            className="mt-3"
            icon={CiMail}
            message={touchedFields.email ? errors["email"] : ""}
            onChange={handleChange}
            onBlur={handleBlur}
          />
          <TextInput
            type="password"
            name="password"
            placeholder="Wachtwoord (8+ tekens)"
            className="mt-3"
            icon={CiLock}
            message={touchedFields.password ? errors["password"] : ""}
            onChange={handleChange}
            onBlur={handleBlur}
          />
        </AuthForm>

        <Text className="mt-3 text-center text-sm text-gray-600">
          Door verder te gaan, ga je akkoord met onze{" "}
          <a
            href="/terms-of-service"
            className="text-green-600 hover:underline"
            target="_blank"
          >
            Algemene voorwaarden
          </a>{" "}
          en{" "}
          <a
            href="/privacy-policy"
            className="text-green-600 hover:underline"
            target="_blank"
          >
            Privacybeleid
          </a>
        </Text>

        <Text className="text-center mt-4 text-gray-600">
          Gebruik je al het Altum AI platform?{" "}
          <Link to="/signin" className="text-green-600 hover:underline">
            Inloggen
          </Link>
        </Text>
      </div>
    </div>
  );
};

export default SignupLeftSection;
