import React from "react";
import { Redirect } from "react-router";
import { useAppSelector } from "../../../redux/hooks";
import ImageLabelForm from "./ImageLabelForm";
import Loading from "../../Loading";
import { FormProvider } from "../../../pages/Api/usage-pages/components/FormContext";
const ImageLabel = () => {
  const { result, loading } = useAppSelector((state) => state.imageLabel);

  if (loading) {
    return <Loading />;
  }
  return (
    <FormProvider>
      {Object.keys(result).length > 0 ? (
        <Redirect
          to={{
            pathname: "/foto-labelen-result",
          }}
        />
      ) : (
        <ImageLabelForm />
      )}
    </FormProvider>
  );
};

export default ImageLabel;
