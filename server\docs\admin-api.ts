/**
 * Admin API Documentation
 * This file contains TypeScript interfaces and documentation for the admin API endpoints.
 */

export interface PaginationQuery {
  /** Page number (0-based) */
  page?: number;
  /** Number of items per page */
  limit?: number;
  /** Field to sort by */
  sortBy?: string;
  /** Sort order ('asc' or 'desc') */
  sortOrder?: "asc" | "desc";
}

export interface AdminEndpoints {
  /**
   * Create a new user
   * POST /api/v1/admin/create-user
   * Creates a new user and sends their login credentials via email
   * @requires Authentication & Admin privileges
   */
  createUser: {
    body: {
      email: string;
      firstName?: string;
      lastName?: string;
      password?: string;
      company?: string;
      kvk?: string;
      usagePlanId: string;
    };
    response: {
      status: "success" | "error";
      /** Message indicating credentials have been sent */
      message?: string;
      user: {
        user_id: string;
        email: string;
        first_name: string;
        last_name: string;
        company: string;
        kvk: string;
        api_key: string;
        api_key_id: string;
        current_usage_plan: string;
        created_at: string;
      };
    };
  };

  /**
   * Delete a user
   * DELETE /api/v1/admin/delete-user/:userId
   * @requires Authentication & Admin privileges
   */
  deleteUser: {
    params: {
      userId: string;
    };
    response: {
      status: "success" | "error";
      message: string;
    };
  };

  /**
   * Get all users with pagination, search, and filtering
   * GET /api/v1/admin/get-users
   * @requires Authentication & Admin privileges
   */
  getUsers: {
    query: PaginationQuery & {
      /** Search term for name, email, or API key */
      search?: string;
      /** Filter by account status (true/false) */
      active?: boolean;
    };
    response: {
      status: "success" | "error";
      items: Array<{
        user_id: string;
        email: string;
        first_name: string;
        last_name: string;
        company: string;
        kvk: string;
        api_key: string;
        api_key_id: string;
        current_usage_plan: string;
        created_at: string;
        active: boolean;
      }>;
      totalItems: number;
      currentPage: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  };

  /**
   * Get user by ID
   * GET /api/v1/admin/get-user/:userId
   * @requires Authentication & Admin privileges
   */
  getUserById: {
    params: {
      userId: string;
    };
    response: {
      status: "success" | "error";
      user: {
        user_id: string;
        email: string;
        first_name: string;
        last_name: string;
        company: string;
        kvk: string;
        api_key: string;
        api_key_id: string;
        current_usage_plan: string;
        created_at: string;
        active: boolean;
      };
    };
  };

  /**
   * Get API usage for a specific user
   * GET /api/v1/admin/user/:userId/api-usage
   * Similar to getUsagePlanDetails but for admin viewing any user's detailed usage
   * @requires Authentication & Admin privileges
   */
  getUserApiUsage: {
    params: {
      /** User ID to fetch API usage for */
      userId: string;
    };
    response: {
      status: "success" | "error";
      subscription: boolean;
      usage: {
        total_calls: number;
        usage_by_endpoint: {
          [endpoint: string]: number;
        };
      };
      usageToday: {
        total_calls: number;
        usage_by_endpoint: {
          [endpoint: string]: number;
        };
      };
      usageYesterday: {
        total_calls: number;
        usage_by_endpoint: {
          [endpoint: string]: number;
        };
      };
      usageLastWeek: {
        total_calls: number;
        usage_by_endpoint: {
          [endpoint: string]: number;
        };
      };
      usageThisWeek: {
        total_calls: number;
        usage_by_endpoint: {
          [endpoint: string]: number;
        };
      };
      usagePlan: {
        name: string;
        quota: number | null;
      };
      allowedApiNames: string[];
    };
  };

  /**
   * Edit user by ID
   * PATCH /api/v1/admin/user/:userId
   * @requires Authentication & Admin privileges
   */
  editUser: {
    params: {
      /** User ID to edit */
      userId: string;
    };
    body: {
      /** User's first name */
      first_name?: string;
      /** User's last name */
      last_name?: string;
      /** User's email address */
      email?: string;
      /** Company name */
      company?: string;
      /** KVK number */
      kvk?: string;
      /** Account status */
      active?: boolean;
      /** Current usage plan ID */
      current_usage_plan?: string;
      /** Stripe customer ID */
      stripe_customer_id?: string;
      /** User role */
      role?: string;
      /** API key */
      api_key?: string;
      /** API key ID */
      api_key_id?: string;
    };
    response: {
      status: "success" | "error";
      user?: {
        user_id: string;
        email: string;
        first_name: string;
        last_name: string;
        company?: string;
        kvk?: string;
        api_key: string;
        api_key_id: string;
        current_usage_plan: string;
        stripe_customer_id: string;
        role: string;
        created_at: string;
        active: boolean;
      };
      message?: string;
    };
  };

  /**
   * Get all usage plans
   * GET /api/v1/admin/usage-plans
   * @requires Authentication & Admin privileges
   */
  getUsagePlans: {
    query: PaginationQuery;
    response: {
      status: "success" | "error";
      items: Array<{
        id: string;
        name: string;
        description?: string;
        quota?: {
          limit: number;
          period: string;
          offset: number;
        };
        throttle?: {
          burstLimit: number;
          rateLimit: number;
        };
      }>;
      totalItems: number;
      currentPage: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  };

  /**
   * Create a usage plan
   * POST /api/v1/admin/usage-plans
   * @requires Authentication & Admin privileges
   */
  createUsagePlan: {
    body: {
      name: string;
      description?: string;
      quota?: {
        limit: number;
        period: string;
        offset: number;
      };
      throttle?: {
        burstLimit: number;
        rateLimit: number;
      };
    };
    response: {
      status: "success" | "error";
      plan: {
        id: string;
        name: string;
        description?: string;
        quota?: {
          limit: number;
          period: string;
          offset: number;
        };
        throttle?: {
          burstLimit: number;
          rateLimit: number;
        };
      };
    };
  };

  /**
   * Update a usage plan
   * PATCH /api/v1/admin/usage-plans/:planId
   * @requires Authentication & Admin privileges
   */
  updateUsagePlan: {
    params: {
      planId: string;
    };
    body: {
      name?: string;
      description?: string;
      quota?: {
        limit: number;
        period: string;
        offset: number;
      };
      throttle?: {
        burstLimit: number;
        rateLimit: number;
      };
    };
    response: {
      status: "success" | "error";
      plan: {
        id: string;
        name: string;
        description?: string;
        quota?: {
          limit: number;
          period: string;
          offset: number;
        };
        throttle?: {
          burstLimit: number;
          rateLimit: number;
        };
      };
    };
  };

  /**
   * Delete a usage plan
   * DELETE /api/v1/admin/usage-plans/:planId
   * @requires Authentication & Admin privileges
   */
  deleteUsagePlan: {
    params: {
      planId: string;
    };
    response: {
      status: "success" | "error";
      message: string;
    };
  };

  /**
   * Get API usage overview
   * GET /api/v1/admin/api-usage/overview
   * @requires Authentication & Admin privileges
   */
  getApiUsageOverview: {
    response: {
      status: "success" | "error";
      data: {
        total_users: number;
        active_users: number;
        new_users: number;
        usage_by_endpoint: {
          [endpoint: string]: number;
        };
      };
    };
  };

  /**
   * Migrate a legacy user with an existing API key
   * POST /api/v1/admin/migrate-legacy-user
   * - If user exists, only updates API key related fields
   * - If user doesn't exist, creates a new user with AWS resources and sends login credentials via email
   * @requires Authentication & Admin privileges
   */
  migrateLegacyUser: {
    body: {
      /** External API key to migrate */
      externalApiKey: string;
      /** User's email address */
      email: string;
      /** Optional company name */
      company?: string;
      /** Optional KVK number */
      kvk?: string;
      /** Optional first name */
      firstName?: string;
      /** Optional last name */
      lastName?: string;
    };
    response: {
      status: "success" | "error";
      /** Message indicating credentials have been sent (for new users) */
      message?: string;
      user: {
        user_id: string;
        email: string;
        first_name: string;
        last_name: string;
        company: string;
        kvk: string;
        api_key: string;
        api_key_id: string;
        current_usage_plan: string;
        created_at: string;
        active: boolean;
      };
    };
  };

  /**
   * Get analytics
   * GET /api/v1/admin/analytics/:date
   * @requires Authentication & Admin privileges
   */
  getAnalytics: {
    params: {
      date: string; // Format: YYYY-MM-DD
    };
    response: {
      status: "success" | "error";
      data: {
        total_users: number;
        active_users: number;
        new_users: number;
        usage_by_endpoint: {
          [endpoint: string]: number;
        };
      };
    };
  };

  /**
   * Get line chart analytics
   * GET /api/v1/admin/analytics/line/:start/:end
   * @requires Authentication & Admin privileges
   */
  getLineChartAnalytics: {
    params: {
      start: string; // Format: YYYY-MM-DD
      end: string; // Format: YYYY-MM-DD
    };
    response: {
      status: "success" | "error";
      data: Array<{
        date: string;
        total_users: number;
        active_users: number;
        new_users: number;
        usage_by_endpoint: {
          [endpoint: string]: number;
        };
      }>;
    };
  };

  /**
   * Get user retention metrics
   * GET /api/v1/admin/retention
   * @requires Authentication & Admin privileges
   */
  getUserRetentionMetrics: {
    query: PaginationQuery & {
      months?: number; // Number of months to analyze
    };
    response: {
      items: Array<{
        month: string;
        total_users: number;
        retained_users: number;
        retention_rate: number;
      }>;
      totalItems: number;
      currentPage: number;
      totalPages: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  };
}
