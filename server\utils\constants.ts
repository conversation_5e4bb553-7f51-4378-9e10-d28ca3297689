export const STRIPE_MOPSUS_TAX_RATE = "txr_1HMYiDDFtlz8Oje6j6rq84vg";

// SENDGRID
export const SENDGRID_SUB_CANCELLED_ID = "d-b320e5f1a9e746c7a3b0a6d6b86fbab3";
export const SENDGRID_CONFIRM_SIGNUP_ID = "d-c6f30ed9bddc4143840fe074e2dd5f80";
export const SENDGRID_CHANGE_PASSWORD_ID = "d-1e5298221f8c45afbda7a731b126cbd7";
export const SENDGRID_LOGIN_CREDENTIALS_ID =
  "d-975a34e29e874895a5e2d2eab66f6a7c";
export const SENDGRID_API_NOT_USED_ID = "d-74b3c206f27e408ba5824f38fe89dcb2";
export const SENDGRID_SUB_BOUGHT_ID = "d-c1062049ba6547ffa35ca718d4790a0d";
export const SENDGRID_RENEW_PPU_ID = "d-65a33ad3b96f40a494cea7d83fe77146";
export const SENDGRID_RENEW_FREE_TIER_ID = "d-ba3a7d92cebe42fc9dd9cb3c9b686cf7";
export const SENDGRID_ACC_VERIFIED_ID = "d-7be9459a87ca4afb910ebc883e139a48";
export const SENDGRID_429_ID = "d-382f3dfe5ece412289a828417c5c5c1b";
export const SENDGRID_403_ID = "d-b0f63aa47b7d41c8b4e7e37382537a15";
export const SENDGRID_422_ID = "d-05d7430675ca431e8d31a8b5eb8e9a8d";
export const SENDGRID_ANALYTICS_ID = "d-f2659a4f48c8400b8d45f4359fd7ad95";
export const SENDGRID_OTP = "d-55c48471796e4a579a8768951b5c1933";
export const SENDGRID_2FA = "d-3b4364c78a9145f39c2eae18a55002c4";

export const SENDGRID_500_ID = "d-b55976fba33c4624a8dfaa673c663651";

export const SENDGRID_INVOICE_ID = "d-a284f3ce96e14151aae304658b653e35";
export const SENDGRID_FAILED_INVOICE_ID = "d-70985c6475d140d586bfa60f8e1b5fdc";

export const MOPSUS_PPU = "Altum AI - Pay per use";
export const MOPSUS_100 = "Altum AI - 100";
export const MOPSUS_250 = "Altum AI - 250";
export const MOPSUS_750 = "Altum AI - 750";
export const MOPSUS_2000 = "Altum AI - 2000";

export const MOPSUS_STARTUP_5000 = "Startup aanbod - 5.000 API calls per maand";
export const MOPSUS_STARTUP_200 = "Startup dagelijkse limiet";

export const MOPSUS_1200 = "Altum AI - 1200";
export const MOPSUS_3000 = "Altum AI - 3000";
export const MOPSUS_9000 = "Altum AI - 9000";
export const MOPSUS_24000 = "Altum AI - 24000";

export const MOPSUS_DAILY_20 = "Dagelijks limiet 20";
export const MOPSUS_DAILY_100 = "Dagelijks limiet 100";
export const MOPSUS_DAILY_250 = "Dagelijks limiet 250";

export const MOPSUS_WOZ_UNLIMITED = "WOZ API";
export const MOPSUS_OBJ_DATA_UNLIMITED = "Object data API";
export const MOPSUS_AVM_UNLIMITED = "Woningwaarde API";
export const MOPSUS_REFERENCE_UNLIMITED = "Woning Referenties API";
export const MOPSUS_ECO_UNLIMITED = "Verduurzaming API";
export const MOPSUS_ENERGY_UNLIMITED = "NTA 8800 Energielabel API";

export const TRANSACTION_PPU = "Kadaster transacties - Pay per use";
export const TRANSACTION_100 = "Kadaster transacties - 100";

// TEST PRICES
export const STRIPE_PPU_TEST = "price_1LkF7HDFtlz8Oje6XcerEoPQ";
export const STRIPE_100_TEST = "price_1Njm2WDFtlz8Oje645wtteYK";
export const STRIPE_250_TEST = "price_1LkFB5DFtlz8Oje65ECiACmi";
export const STRIPE_750_TEST = "price_1LkFGMDFtlz8Oje63BKtO2RQ";
export const STRIPE_2000_TEST = "price_1LkO73DFtlz8Oje6FTPMmX2U";

export const STRIPE_STARTUP_5000_TEST = "price_1Ok1cxDFtlz8Oje6v2ZnPZxh";
export const STRIPE_STARTUP_200_TEST = "price_1Ok22IDFtlz8Oje6zNyISf5S";

export const STRIPE_TRANSACTION_PPU_TEST = "price_1LkOYkDFtlz8Oje6lwysSJL6";
export const STRIPE_TRANSACTION_100_TEST = "price_1NjmHtDFtlz8Oje6OX1njkET";
// yearly Prices
export const STRIPE_1200_YEARLY_RECURRING_TEST =
  "price_1Njm7tDFtlz8Oje6WJ4Sgja4";
export const STRIPE_3000_YEARLY_RECURRING_TEST =
  "price_1MTmjWDFtlz8Oje6vj4udSJc";
export const STRIPE_9000_YEARLY_RECURRING_TEST =
  "price_1MTmooDFtlz8Oje69cRTFXIr";
export const STRIPE_24000_YEARLY_RECURRING_TEST =
  "price_1MTmqtDFtlz8Oje6oT995fZ1";

// Unlimited Prices
export const STRIPE_WOZ_UNLIMITED_TEST = "price_1O3CoTDFtlz8Oje66TpHMwYa";
export const STRIPE_OBJ_DATA_UNLIMITED_TEST = "price_1O3Ct9DFtlz8Oje65EO0E6Rx";
export const STRIPE_AVM_UNLIMITED_TEST = "price_1NyBLiDFtlz8Oje6IBi8Jg0a";
export const STRIPE_REFERENCE_UNLIMITED_TEST = "price_1NyBIUDFtlz8Oje6gzNMSY7Y";
export const STRIPE_ECO_UNLIMITED_TEST = "price_1O5bioDFtlz8Oje6CZ70bVFG";
export const STRIPE_ENERGY_UNLIMITED_TEST = "price_1O5aPbDFtlz8Oje6wGOIs5qS";

// Daily Quota Prices
export const STRIPE_20_DAILY_TEST = "price_1N5qZXDFtlz8Oje6s2dHevsW";
export const STRIPE_100_DAILY_TEST = "price_1N5qXvDFtlz8Oje6ht3Ed53E";
export const STRIPE_250_DAILY_TEST = "price_1N5qWBDFtlz8Oje6biNAGoMr";

export const STRIPE_MOPSUS_TAX_RATE_TEST = "txr_1HMYo4DFtlz8Oje6nj9chcN6";

// LIVE PRICES
export const STRIPE_PPU = "price_1LmdWyDFtlz8Oje6D6hHr1xU";
export const STRIPE_100 = "price_1NhcCjDFtlz8Oje6lPlH9bQy";
export const STRIPE_250 = "price_1LaKJqDFtlz8Oje6Ired8OWh";
export const STRIPE_750 = "price_1LaKNCDFtlz8Oje6HsitTbis";
export const STRIPE_2000 = "price_1LaKOIDFtlz8Oje6ag1JjPC5";

//STARTUP PLANS
export const STRIPE_STARTUP_5000 = "price_1MxoHJDFtlz8Oje6s2uILuAa";
export const STRIPE_STARTUP_200 = "price_1MxoH3DFtlz8Oje6sUoK1Cyy";

// Daily Quota Prices
export const STRIPE_20_DAILY = "price_1LmdWyDFtlz8Oje6D6hHr1xU";
export const STRIPE_100_DAILY = "price_1MxoBMDFtlz8Oje6pNxcMREK";
export const STRIPE_250_DAILY = "price_1MxoCTDFtlz8Oje6OjhMmvH0";

// Unlimited Price
export const STRIPE_WOZ_UNLIMITED = "price_1MmezXDFtlz8Oje6QvVHT5x2";
export const STRIPE_OBJ_DATA_UNLIMITED = "price_1MmeznDFtlz8Oje6TyMkdLJd";
export const STRIPE_AVM_UNLIMITED = "price_1MmdSYDFtlz8Oje6W6YwPqLH";
export const STRIPE_REFERENCE_UNLIMITED = "price_1MmdSiDFtlz8Oje6ypSnMG8Z";
export const STRIPE_ECO_UNLIMITED = "price_1MmdT3DFtlz8Oje656Wb0Cmj";
export const STRIPE_ENERGY_UNLIMITED = "price_1MmdTGDFtlz8Oje6y6qUohMs";

// yearly Prices
export const STRIPE_1200_YEARLY_RECURRING = "price_1Nn8ADDFtlz8Oje6SmQ2uaeM";
export const STRIPE_3000_YEARLY_RECURRING = "price_1MxoAqDFtlz8Oje6IHmhhMoM";
export const STRIPE_9000_YEARLY_RECURRING = "price_1MSdFyDFtlz8Oje6nC8TMCHq";
export const STRIPE_24000_YEARLY_RECURRING = "price_1MSdGWDFtlz8Oje6RyWGIXGZ";

export const STRIPE_TRANSACTION_PPU = "price_1NSz3nDFtlz8Oje6kXnrwWeH";
export const STRIPE_TRANSACTION_100 = "price_1NjmjPDFtlz8Oje6mh5MXfGc";

// SENDGRIDprice_1NSz3nDFtlz8Oje6kXnrwWeH

export const SENDGRID_CREDIT_DEPLETED = "d-dd72cac7e2cf4dd4834e1ee3910c71ec";
export const SENDGRID_CREDIT_DEPLETED_YEARLY =
  "d-a3f73901ee1544579193b171dca31d61";
// List
export const SENDGRID_LIST_UNLIMITED_MONTHLY_PAIDUSERS =
  "d7ca0e93-aa84-4df7-b1bb-c46adca8467a";

// USAGE PLAN ID
export const AWS_MOPSUS_USAGE_DAILY_20 = "9k1mqq";
export const AWS_MOPSUS_USAGE_DAILY_100 = "arl951";
export const AWS_MOPSUS_USAGE_DAILY_250 = "vrqz03";

//STARTUP PLAN ID
export const AWS_MOPSUS_STARTUP_5000 = "6yu6kt";
export const AWS_MOPSUS_STARTUP_200 = "2dorhm";

// UNLIMITED PLAN ID

export const AWS_MOPSUS_USAGE_WOZ_UNLIMITED = "6iyn0z";
export const AWS_MOPSUS_USAGE_OBJ_DATA_UNLIMITED = "mpiqlc";
export const AWS_MOPSUS_USAGE_AVM_UNLIMITED = "cxj905";
export const AWS_MOPSUS_USAGE_REFERENCE_UNLIMITED = "vvs8i5";
export const AWS_MOPSUS_USAGE_ECO_UNLIMITED = "n960l9";
export const AWS_MOPSUS_USAGE_ENERGY_UNLIMITED = "cybldv";
