import pool from "../db";

/**
 * Runs database migrations
 * @param options Configuration options for migrations
 * @returns Promise that resolves when migrations are complete
 */
export async function runMigrations(
  options: {
    requireConfirmation?: boolean;
    logLevel?: "quiet" | "normal" | "verbose";
  } = {},
) {
  const {
    requireConfirmation = process.env.NODE_ENV === "production",
    logLevel = "normal",
  } = options;

  const log = (
    message: string,
    level: "quiet" | "normal" | "verbose" = "normal",
  ) => {
    if (logLevel === "quiet") return;
    if (level === "verbose" && logLevel !== "verbose") return;
    console.log(message);
  };

  try {
    if (requireConfirmation) {
      log(
        `⚠️  About to run migrations on ${process.env.NODE_ENV} database at ${
          process.env.DB_HOST || process.env.POSTGRES_URI
        }`,
        "quiet",
      );
      log("Press Ctrl+C to cancel...", "quiet");
      await new Promise((resolve) => setTimeout(resolve, 5000));
    }

    // Start a transaction
    await pool.query("BEGIN");

    try {
      log("Running migrations...");

      // Create migrations table if it doesn't exist
      await pool.query(`
        CREATE TABLE IF NOT EXISTS migrations (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL UNIQUE,
          executed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
          environment VARCHAR(50) NOT NULL
        );
      `);

      // Run each migration in a transaction if it hasn't been run before
      for (const [name, sql] of Object.entries(migrations)) {
        const migrationExists = await pool.query(
          "SELECT 1 FROM migrations WHERE name = $1 AND environment = $2",
          [name, process.env.NODE_ENV],
        );

        if (migrationExists.rows.length === 0) {
          log(`Running migration: ${name}`);
          await pool.query(sql);
          await pool.query(
            "INSERT INTO migrations (name, environment) VALUES ($1, $2)",
            [name, process.env.NODE_ENV],
          );
          log(`✅ Completed migration: ${name}`);
        } else {
          log(`⏭️  Skipping migration: ${name} (already executed)`, "verbose");
        }
      }

      await pool.query("COMMIT");
      log("✨ All migrations completed successfully");
    } catch (error) {
      await pool.query("ROLLBACK");
      throw error;
    }
  } catch (error) {
    log("❌ Migration failed:", "quiet");
    console.error(error);
    throw error;
  }
}

const migrations = {
  createAuditAndUsageTables: `
    -- Create audit_logs table
    CREATE TABLE IF NOT EXISTS audit_logs (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        action VARCHAR(255) NOT NULL,
        entity_type VARCHAR(255) NOT NULL,
        entity_id VARCHAR(255) NOT NULL,
        changes JSONB NOT NULL,
        performed_by VARCHAR(255) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        ip_address VARCHAR(45)
    );

    -- Create indexes for efficient querying
    CREATE INDEX IF NOT EXISTS idx_audit_logs_entity ON audit_logs(entity_type, entity_id);
    CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at DESC);
    CREATE INDEX IF NOT EXISTS idx_audit_logs_performed_by ON audit_logs(performed_by);

    -- Create api_usage_logs table
    CREATE TABLE IF NOT EXISTS api_usage_logs (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
        endpoint VARCHAR(255) NOT NULL,
        method VARCHAR(10) NOT NULL,
        status_code INTEGER NOT NULL,
        response_time INTEGER NOT NULL,
        timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        request_body JSONB,
        response_body JSONB,
        ip_address VARCHAR(45),
        user_agent TEXT
    );

    -- Create indexes for efficient querying and analytics
    CREATE INDEX IF NOT EXISTS idx_api_usage_logs_user ON api_usage_logs(user_id);
    CREATE INDEX IF NOT EXISTS idx_api_usage_logs_timestamp ON api_usage_logs(timestamp DESC);
    CREATE INDEX IF NOT EXISTS idx_api_usage_logs_endpoint ON api_usage_logs(endpoint);
    CREATE INDEX IF NOT EXISTS idx_api_usage_logs_status ON api_usage_logs(status_code);

    -- Add last_login_at column to users table if not exists
    DO $$ 
    BEGIN
        IF NOT EXISTS (
            SELECT 1 
            FROM information_schema.columns 
            WHERE table_name = 'users' AND column_name = 'last_login_at'
        ) THEN
            ALTER TABLE users ADD COLUMN last_login_at TIMESTAMP WITH TIME ZONE;
        END IF;
    END $$;
  `,
};
