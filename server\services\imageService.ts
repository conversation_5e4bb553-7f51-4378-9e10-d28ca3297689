import * as fs from "fs/promises";
import * as path from "path";
import * as os from "os";
import { GoogleGenerativeAI, Part } from "@google/generative-ai";

if (!process.env.GOOGLE_API_KEY) {
  throw new Error("GOOGLE_API_KEY is not defined in the environment variables");
}

const genAI = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY);
const model = genAI.getGenerativeModel({ model: "gemini-1.5-flash" });

const UPLOAD_DIR = path.join(os.tmpdir(), "property-uploads");

async function processImage(fileId: string): Promise<Express.Multer.File> {
  const filePath = path.join(UPLOAD_DIR, fileId);
  const metaFilePath = `${filePath}.meta`;

  const [buffer, metadataStr] = await Promise.all([
    fs.readFile(filePath),
    fs.readFile(metaFilePath, "utf-8"),
  ]);

  const metadata = JSON.parse(metadataStr);

  return {
    buffer,
    originalname: metadata.originalName,
    mimetype: metadata.mimeType,
    path: filePath,
  } as Express.Multer.File;
}

export async function processImages(imageIds: string[]): Promise<string[]> {
  if (!imageIds || imageIds.length === 0) return [];

  try {
    const images = await Promise.all(imageIds.map(processImage));

    const imageParts: Part[] = images.map((image) => ({
      inlineData: {
        data: image.buffer.toString("base64"),
        mimeType: image.mimetype,
      },
    }));

    const result = await model.generateContent([
      ...imageParts,
      {
        text: "Analyze these images of a property. Describe the property in detail, including its type (e.g., house, apartment), architectural style, condition, notable features, and any standout elements. Also, list any objects or furnishings you see that are relevant to describing the property.",
      },
    ]);

    const text = result.response.text();

    if (!text) {
      throw new Error("No description generated");
    }

    // Unlink files after description has been generated
    await Promise.all(
      imageIds.map(async (fileId) => {
        const filePath = path.join(UPLOAD_DIR, fileId);
        const metaFilePath = `${filePath}.meta`;
        await Promise.all([fs.unlink(filePath), fs.unlink(metaFilePath)]);
      }),
    );

    return [text];
  } catch (error) {
    console.error("Error processing images:", error);
    throw new Error("Failed to process images");
  }
}
