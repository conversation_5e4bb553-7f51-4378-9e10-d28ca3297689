import pool from "../db";

interface AnalyticsEvent {
  event: string;
  category: string;
  userId?: string;
  metadata?: Record<string, any>;
}

export const createAnalyticsTag = async (eventObject: AnalyticsEvent) => {
  const { event, category } = eventObject;

  try {
    // Log to analytics_logger table
    await pool.query(
      `INSERT INTO analytics_logger (event, category) VALUES ($1, $2);`,
      [event, category],
    );
  } catch (error) {
    console.error("Error creating analytics tag:", error);
    throw error;
  }
};

// Helper function to map legacy events to new format
export const mapLegacyEventType = (category: string, event: string): string => {
  const eventMap: Record<string, Record<string, string>> = {
    Authentication: {
      Signup: "user_signup",
      Signin: "user_login",
      "Verified account": "email_verified",
    },
    Property: {
      Generation: "property_generation",
    },
    Onboarding: {
      Complete: "onboarding_complete",
      Step: "onboarding_step_complete",
    },
  };

  return eventMap[category]?.[event] || `${category}_${event}`.toLowerCase();
};
