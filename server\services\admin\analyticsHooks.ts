import { User } from "../../@types";
import { createAnalyticsTag } from "../../utils/sendAnalytics";

export class AnalyticsHooks {
  static async onUserSignup(user: User) {
    await createAnalyticsTag({
      event: "Signup",
      category: "Authentication",
    });
  }

  static async onEmailVerification(user: User) {
    await createAnalyticsTag({
      event: "Verified account",
      category: "Authentication",
    });
  }

  static async onUserLogin(user: User) {
    await createAnalyticsTag({
      event: "Signin",
      category: "Authentication",
    });
  }

  static async onOnboardingComplete(user: User) {
    await createAnalyticsTag({
      event: "OnboardingComplete",
      category: "Onboarding",
    });
  }

  static async onPropertyGeneration(userId: string, propertyDetails: any) {
    await createAnalyticsTag({
      event: "PropertyGenerated",
      category: "Property",
    });
  }

  static async onFirstApiCall(userId: string) {
    await createAnalyticsTag({
      event: "FirstApiCall",
      category: "API",
    });
  }

  static async onFindApiKey(userId: string) {
    await createAnalyticsTag({
      event: "FindApiKey",
      category: "Onboarding",
    });
  }

  static async onReadDocs(userId: string) {
    await createAnalyticsTag({
      event: "ReadDocs",
      category: "Onboarding",
    });
  }

  static async onBookDemo(userId: string) {
    await createAnalyticsTag({
      event: "BookDemo",
      category: "Onboarding",
    });
  }

  static async onOnboardingQuestionnaire(userId: string) {
    await createAnalyticsTag({
      event: "OnboardingQuestionnaire",
      category: "Onboarding",
    });
  }

  static async onUserActivity(userId: string, activityType: string) {
    await createAnalyticsTag({
      event: activityType,
      category: "Activity",
    });
  }
}
