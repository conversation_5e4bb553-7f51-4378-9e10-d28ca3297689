import styled from 'styled-components';
import { Link } from 'react-router-dom';
import Rectangle from '../assets/images/Rectangle.png';
import { H3, Img, Button } from '../styles/styled';
import left from '../assets/images/left.png';
import right from '../assets/images/right.png';
import 'aos/dist/aos.css';
import useWindowSize from '../hooks/useWindowSize';
import { Colors, device, TextStyles } from '../styles/Theme';

export function CTABanner() {
  const windowSize = useWindowSize();
  return (
    <Body>
      <Container>
        {windowSize.width >= 760 && (
        <Img src={left} width="30%" height="30%" />
        )}
        <ColumnFlex data-aos="fade-up">
          <Title>Start vandaag nog met het gebruik van de data!</Title>
          <Button size="lg">
            <Link to="/" style={{ color: 'white' }}>
              Account aanmaken
            </Link>
          </Button>
        </ColumnFlex>

        {windowSize.width >= 760 && (
        <Img src={right} width="30%" height="30%" />
        )}
      </Container>
    </Body>
  );
}
const Body = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	background-image: ${Colors.main.white};
`;

const Container = styled.div`
	display: flex;
	justify-content: center;
	align-items: center;
	width: 90%;
	background: url(${Rectangle});
	padding: 2rem;
`;

const ColumnFlex = styled.div`
	display: flex;
	flex-direction: column;
	gap: 2rem;
`;

const Title = styled(H3)`
	@media ${device.laptop} {
		${TextStyles.Bundler(TextStyles.Subtitle.H3mobile)};
	}
	@media ${device.mobileL} {
		${TextStyles.Bundler(TextStyles.Subtitle.H3mobile)};
		text-align: center;
	}
`;
