import styled from 'styled-components';
import { Colors, TextStyles, device } from '../styles/Theme';
import { H1, Img } from '../styles/styled';
import forUsers from '../assets/images/forUsers.png';
import 'aos/dist/aos.css';

export function ForUsers() {
  return (
    <Container>
      <Title>Voor gebruikers</Title>
      <Flex data-aos="fade-right">
        <Text>
          Mopsus is ontworpen om iedereen toegang tot vastgoed data te geven, of
          je nu makelaar, risk analist of developer bent. Voor wie alleen
          inzicht in de data wilt, biedt Mopsus een eenvoudige interface die
          geen enkele technische kennis vereist. Met slechts de input van een
          postcode en huisnummer kan alle relevante woningdata opgehaald worden.
          Door extra input mee te geven kan de data vervolgens aangepast
          <br />
          <br />
          worden.Voor wie de data wilt gebruiken om te integreren in andere
          applicaties, biedt Mopsus API’s aan die door developers te gebruiken
          zijn. Op deze manier kan de Mopsus vastgoeddata in een bestaande
          customer journey, dashboards of reports geïntegreerd worden. De API’s
          zijn uitgebreid gedocumenteerd en zeer eenvoudig toe te passen.
        </Text>
        <ImageContainer>
          <Img src={forUsers} width="100%" height="100%" />
        </ImageContainer>
      </Flex>
    </Container>
  );
}
const Container = styled.div`
	display: flex;
	align-items: center;
	flex-direction: column;
	padding: 1rem 0;
	width: 100%;
	justify-content: space-between;
	background: ${Colors.main.white};
	padding: 2rem 5rem;
	gap: 1rem;
	@media ${device.laptop} {
		padding: 2rem 4rem;
	}
	@media ${device.mobileL} {
		padding: 2rem;
	}
`;
const Flex = styled.div`
	display: flex;
	width: 100%;
	justify-content: space-between;
`;
const Text = styled.p`
	${TextStyles.Bundler(TextStyles.Text.SmallText)}
	margin-right: 2rem;

	@media ${device.laptop} {
		margin-right: 0;
	}

	@media ${device.laptop} {
		margin-right: 0;
	}
`;
const Title = styled(H1)`
	margin-bottom: 1rem;
	text-align: center;
	@media ${device.laptop} {
		${TextStyles.Bundler(TextStyles.Subtitle.H3tablet)}
	}
`;

const ImageContainer = styled.div`
	@media ${device.laptop} {
		display: none;
	}
`;
