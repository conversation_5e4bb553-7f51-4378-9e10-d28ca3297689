import express from "express";
import { protect } from "../controllers/authController";
import {
  addAllOnboardingState,
  addOnboardingQuestionResponse,
  addOnboardingState,
  getOnboardingQuestions,
  getOnboardingState,
  getOnboardingSteps,
  getUserQuestion5Response,
  onboardingStepsCompleted,
} from "../controllers/onboardingController";

const router = express.Router();

router.get("/steps", protect, getOnboardingSteps);
router.post("/states", protect, addOnboardingState);
router.get("/states", protect, getOnboardingState);
router.get("/questions", protect, getOnboardingQuestions);
router.post("/responses", protect, addOnboardingQuestionResponse);
router.get("/status", protect, onboardingStepsCompleted);
router.post("/update-states", protect, addAllOnboardingState);

router.get(
  "/user/:userId/question5/response",
  protect,
  getUserQuestion5Response,
);

export default router;
