# Two Factor Authentication (2FA) API Documentation

This document describes the payloads and responses for the 2FA endpoints provided by the application's API. The endpoints are implemented in:

- **Routes:** `server/routes/twoFactorAuthRoutes.ts`
- **Controllers:** `server/controllers/twoFactorAuthController.ts`

## Endpoints Overview

### 1. Verify 2FA Code (POST /verify)

- **Description:** Verifies the 2FA code during login.
- **Endpoint:** `/verify`
- **Method:** POST
- **Payload:**

  ```json
  {
    "userId": "string", // required
    "code": "string" // required
  }
  ```

- **Success Response:** Creates and sends a JWT token to the client.
- **Error Response:**
  - HTTP 400 if `userId` or `code` is missing.
  - HTTP 500 for internal errors with an `error` message.

### 2. Request 2FA Code (POST /request-code)

- **Description:** Requests a new 2FA code for the user.
- **Endpoint:** `/request-code`
- **Method:** POST
- **Payload:**

  ```json
  {
    "userId": "string" // required
  }
  ```

- **Success Response:**

  ```json
  {
    "status": "success",
    "message": "2FA code sent"
  }
  ```

- **Error Response:**
  - HTTP 400 if `userId` is missing.
  - HTTP 500 for internal errors with an `error` message.

### 3. Enable 2FA (POST /enable)

- **Description:** Enables 2FA for the authenticated user using either email or an authenticator app.
- **Endpoint:** `/enable`
- **Method:** POST
- **Authentication:** Required
- **Payload:**

  ```json
  {
    "type": "email" | "authenticator" // required
  }
  ```

- **Success Response:**

  ```typescript
  interface Enable2FAResponse {
    status: "success";
    message: string; // "2FA enabled with {type}"
    data: {
      qrCode?: string;    // QR code URL for authenticator setup
      secret?: string;    // TOTP secret in base32 format
    }
  }
  ```

  Examples:
  ```json
  // For authenticator type
  {
    "status": "success",
    "message": "2FA enabled with authenticator",
    "data": {
      "qrCode": "data:image/png;base64,...",
      "secret": "JBSWY3DPEHPK3PXP"
    }
  }

  // For email type
  {
    "status": "success",
    "message": "2FA enabled with email",
    "data": {}
  }
  ```

- **Error Response:**
  - HTTP 404 if user not found.
  - HTTP 500 for internal errors with an `error` message.

### 4. Disable 2FA (POST /disable)

- **Description:** Disables 2FA for the authenticated user.
- **Endpoint:** `/disable`
- **Method:** POST
- **Authentication:** Required
- **Payload:** None required.
- **Success Response:**

  ```json
  {
    "status": "success",
    "message": "2FA disabled"
  }
  ```

- **Error Response:**
  - HTTP 404 if user not found.
  - HTTP 500 for internal errors with an `error` message.

## Common Error Format

Error responses will include an error message and an appropriate HTTP status code. For example:

```json
{
  "status": "error",
  "message": "Error enabling 2FA"
}
```

## Notes

- Public routes: `/verify` and `/request-code` do not require authentication.
- Protected routes: `/enable` and `/disable` require authentication (via middleware).
- The JWT token on successful verification is generated using the application's `createTokenResponse` function.
- Ensure that proper validation is applied to input payloads to avoid errors.
