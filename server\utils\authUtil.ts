import jwt, { SignOptions } from "jsonwebtoken";
import { CookieOptions, Response } from "express";
import { User } from "../@types";
import { send2FACode } from "./twoFactorAuth";

const signJWTToken = (user_id: string): string => {
  const options: SignOptions = {
    expiresIn: 24 * 60 * 60, // 24 hours in seconds
  };
  return jwt.sign(
    { id: user_id },
    process.env.JWT_SECRET_KEY || "default-secret-key",
    options,
  );
};

// FUNCTION: Create cookie and JSON response

export const createTokenResponse = (user: User, res: Response): void => {
  const token = cookieFunction(user, res);

  // Send success as JSON response
  res.status(200).json({
    status: "success",
    user,
    token,
  });
};
export function cookieFunction(
  user: User,
  res: Response<any, Record<string, any>>,
) {
  const token = signJWTToken(user.user_id);

  // Create cookie
  const cookieOptions: CookieOptions = {
    expires: new Date(
      Date.now() +
        parseInt(process.env.JWT_COOKIE_EXPIRY!) * 24 * 60 * 60 * 1000,
    ),
    // this means that we cannot manipulate cookie in the browser in any way
    httpOnly: true,
    // secure: req.secure || req.headers("x-forwarded-proto") === "https",
    secure: false,
    sameSite: "none",
  };
  if (
    process.env.NODE_ENV === "production" ||
    process.env.NODE_ENV === "test"
  ) {
    cookieOptions.secure = true;
  }
  res.cookie("x-mopsus-jwt", token, cookieOptions);
  return token;
}

// Shared function to handle 2FA checks for both social and regular auth
export const handle2FACheck = async (
  user: User,
): Promise<{
  status: string;
  message: string;
  userId: string;
  type: "email" | "authenticator";
} | null> => {
  if (user.two_factor_enabled) {
    // Ensure two_factor_type is defined and valid
    if (user.two_factor_type && user.two_factor_type === "email") {
      await send2FACode(user);
    }

    // Return 2FA required response
    return {
      status: "2fa_required",
      message: "2FA verification required",
      userId: user.user_id,
      type: user.two_factor_type || "authenticator", // Default to authenticator if type is undefined
    };
  }
  // If 2FA is not enabled, return null to indicate no 2FA required
  return null;
};
