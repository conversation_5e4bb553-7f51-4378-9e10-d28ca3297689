import { Request, Response, NextFunction } from "express";

// Timeout middleware to prevent long-running requests
export const timeout = (time: number) => {
  return (req: Request, res: Response, next: NextFunction) => {
    res.setTimeout(time, () => {
      res.status(408).send("Request Timeout");
    });
    next();
  };
};

// Security headers middleware
export const additionalSecurityHeaders = (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  // Prevent clickjacking attacks
  res.setHeader("X-Frame-Options", "DENY");

  // Prevent MIME type sniffing
  res.setHeader("X-Content-Type-Options", "nosniff");

  // Enable XSS filter in browser
  res.setHeader("X-XSS-Protection", "1; mode=block");

  // Control cross-domain policies
  res.setHeader("X-Permitted-Cross-Domain-Policies", "none");

  // Remove X-Powered-By header
  res.removeHeader("X-Powered-By");

  next();
};
