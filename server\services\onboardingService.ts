import pool from "../db";

export const addOnboardingStateService = async (
  stepId: string,
  userId: string,
) => {
  await pool.query(
    `INSERT INTO user_onboarding_state (user_id, step_id, is_complete) VALUES ($1, $2, $3)`,
    [userId, stepId, true],
  );
};

export const countUserResponses = async (userId: string): Promise<number> => {
  try {
    const result = await pool.query(
      `SELECT COUNT(DISTINCT question_id) AS response_count FROM user_onboarding_responses WHERE user_id = $1`,
      [userId],
    );
    return parseInt(result.rows[0].response_count);
  } catch (error) {
    console.error("Error counting user responses:", error);
    throw error;
  }
};
