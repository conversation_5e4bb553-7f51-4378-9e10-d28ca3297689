import { GraphQLClient } from "graphql-request";
import AppError from "./appError";

export default async (
  status: string | number,
  time: number,
  apiKey: string,
) => {
  try {
    const keyLength = apiKey.length;
    const last6 = apiKey.slice(keyLength - 6);
    const hidden = "*".repeat(keyLength - 6);
    const hiddenApiKey = `${hidden}${last6}`;

    let query = `
    {
      actor {
        account(id: $id) {
          nrql(
            query: "SELECT count(*) FROM Log WHERE status = ${status} AND apiKey = '${hiddenApiKey}' SINCE ${time} days ago"
          ) {
            results
          }
        }
      }
    }
  `;

    query = query.replace("$id", process.env.GRAPHQL_ID!);

    const client = new GraphQLClient("https://api.eu.newrelic.com/graphql", {
      headers: { "api-key": `${process.env.GRAPHQL_APIKEY}` },
    });

    return await client
      .request(query)
      .then((data) => data.actor.account.nrql.results[0].count)
      .catch((error) => {
        console.log(error);
        new AppError("unable to fetch from new relic", 400);
      });
  } catch (err) {
    console.log(err);
    return new AppError("unable to fetch from new relic", 400);
  }
};
