import { FC, useState, useEffect, useCallback } from "react";
import Text from "../../../../components/Text";
import Taxapi from "../../../../assets/images/taxapi.png";
import Woonu from "../../../../assets/images/woonnu.png";
import NN from "../../../../assets/images/national.png";
import Verduurzaming from "../../../../assets/images/Verduurzaming-v2.png";
import { FiChevronLeft, FiChevronRight } from "react-icons/fi";
import { Img } from "@/styles/styled";

const SigninRightSection: FC = () => {
  const [activeCard, setActiveCard] = useState(0);
  const totalCards = 3;
  const autoChangeInterval = 3000; // 3 seconds

  const nextCard = useCallback(() => {
    setActiveCard((prev) => (prev === totalCards - 1 ? 0 : prev + 1));
  }, [totalCards]);

  const prevCard = useCallback(() => {
    setActiveCard((prev) => (prev === 0 ? totalCards - 1 : prev - 1));
  }, [totalCards]);

  // Auto-change carousel
  useEffect(() => {
    const interval = setInterval(() => {
      nextCard();
    }, autoChangeInterval);

    return () => clearInterval(interval);
  }, [nextCard]);

  const renderDots = () => {
    return (
      <div className="flex justify-center mt-4 space-x-2">
        {Array.from({ length: totalCards }).map((_, index) => (
          <button
            key={index}
            onClick={() => setActiveCard(index)}
            className={`w-2 h-2 rounded-full ${
              activeCard === index ? "bg-white" : "bg-gray-400"
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    );
  };

  const renderSolarScanCard = () => (
    <div className="bg-white rounded-lg p-6 shadow-lg w-full">
      <img
        src="/images/Login_carousel_1.png"
        alt="Solar scan rapport"
        className="w-full h-[240px] object-contain"
      />
    </div>
  );
  const renderReconstructionValueCard = () => (
    <div className="bg-white rounded-lg p-6 shadow-lg w-full">
      <img
        src="/images/Login_carousel_2.png"
        alt="Reconstructiewaarde"
        className="w-full h-[240px] object-contain"
      />
    </div>
  );

  const renderRentalReferenceCard = () => (
    <div className="bg-white rounded-lg p-6 shadow-lg w-full">
      <img
        src="/images/Login_carousel_3.png"
        alt="Huurverwijzing"
        className="w-full h-[240px] object-contain"
      />
    </div>
  );

  const renderActiveCard = () => {
    switch (activeCard) {
      case 0:
        return renderSolarScanCard();
      case 1:
        return renderReconstructionValueCard();
      case 2:
        return renderRentalReferenceCard();
      default:
        return renderSolarScanCard();
    }
  };

  return (
    <div className="w-full md:w-2/5 bg-[radial-gradient(circle_at_center,_#27AE60,_#214932)] p-8 hidden md:flex md:flex-col md:justify-center">
      <div className="max-w-md mx-auto w-full flex flex-col justify-evenly h-full">
        <div className="text-white mb-8">
          <h2 className="text-2xl font-bold mb-4">
            Onze productlijst breidt zich uit...
          </h2>
          <p className="text-base">
            We voegen meer tools toe om uw inzichten te versterken - ontdek wat
            er nieuw is.
          </p>
        </div>

        <div className="relative">
          <div className="flex justify-center items-center">
            <button
              onClick={prevCard}
              className="absolute left-0 z-10 p-1 bg-white bg-opacity-30 rounded-full text-white hover:bg-opacity-50"
              aria-label="Previous slide"
            >
              <FiChevronLeft size={24} />
            </button>

            <div className="w-full">{renderActiveCard()}</div>

            <button
              onClick={nextCard}
              className="absolute right-0 z-10 p-1 bg-white bg-opacity-30 rounded-full text-white hover:bg-opacity-50"
              aria-label="Next slide"
            >
              <FiChevronRight size={24} />
            </button>
          </div>
          {renderDots()}
        </div>

        <div className="mt-8 flex items-center justify-center">
          <div className="flex">
            {[1, 2, 3, 4, 5].map((star) => (
              <svg
                key={star}
                className="w-5 h-5 text-yellow-300"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
            ))}
          </div>
          <span className="ml-2 text-white">9+ beoordelingen</span>
        </div>

        <div className="mt-8 flex justify-between items-center">
          <img src={Taxapi} alt="Taxapi" className="h-[100px] object-contain" />
          <img src={Woonu} alt="Woonnu" className="h-[100px] object-contain" />
          <img
            src={NN}
            alt="Nationale Nederlanden"
            className="h-[100px] object-contain"
          />
          {/* <img
            src={Verduurzaming}
            alt="Verduurzaming"
            className="h-[58px] object-contain"
          /> */}
        </div>
      </div>
    </div>
  );
};

export default SigninRightSection;
