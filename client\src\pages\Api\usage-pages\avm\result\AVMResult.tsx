import React, { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "../../../../../redux/hooks";
import { useFormContext } from "../../components/FormContext";
import { Redirect, useHistory } from "react-router-dom";
import RedoButton from "../../components/RedoButtons";
import ResultSkeleton from "../../sustainability/components/ResultSkeleton";
import Result from "../components/Result";
import { ResultSummary } from "../components/ResultSummary";
import {
  clearAvmResults,
  modifyAvmQueries,
} from "../../../../../redux/actions/avmActions";
import { AVMResult as AVMResultType } from "../types";
import { ApiInitState } from "../../../../../helpers/createApiSlice";

const AVMResult = () => {
  const { result, loading } = useAppSelector(
    (state) => state.avm as ApiInitState<AVMResultType>,
  );
  const { buildingPhoto, map, setPostalAddress } = useFormContext();
  const history = useHistory();
  const dispatch = useAppDispatch();

  const clearResults = () => {
    dispatch(clearAvmResults());
    history.push("/avm");
  };

  const modifyResults = () => {
    dispatch(modifyAvmQueries());
    history.push("/avm");
  };

  useEffect(() => {
    if (Object.keys(result).length > 0) {
      setPostalAddress(
        `${result.PostCode}-${result.HouseNumber}-${
          result.HouseAddition || ""
        }`,
      );
    }
  }, [result, setPostalAddress]);

  if (Object.keys(result).length === 0 && !loading) {
    return <Redirect to="/avm" />;
  }

  return (
    <>
      {loading ? (
        <ResultSkeleton />
      ) : (
        <>
          <ResultSummary
            property={result}
            buildingPhoto={buildingPhoto}
            map={map}
          />
          <Result property={result} buildingPhoto={buildingPhoto} map={map} />
          <RedoButton modify={modifyResults} clear={clearResults} />
        </>
      )}
    </>
  );
};

export default AVMResult;
