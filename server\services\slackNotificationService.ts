import axios from "axios";

const SLACK_WEBHOOK_URL = process.env.SLACK_WEBHOOK_URL;

export async function sendSlackNotification(
  email: string,
  description: string,
) {
  if (!SLACK_WEBHOOK_URL) {
    console.warn("SLACK_WEBHOOK_URL is not set. Skipping Slack notification.");
    return;
  }
  const slackMessage = {
    text: `New submission from ${email}`,
    attachments: [
      {
        color: "#36a64f", // You can use a good color for the attachment
        author_name: email,
        title: "New Property Description",
        text: `Email: ${email}`,
        fields: [
          {
            title: "Description",
            value: description,
          },
        ],
      },
    ],
  };

  try {
    await axios.post(SLACK_WEBHOOK_URL, JSON.stringify(slackMessage));
    console.log("Slack notification sent successfully");
  } catch (error) {
    console.error("Error sending Slack notification:", error);
  }
}
