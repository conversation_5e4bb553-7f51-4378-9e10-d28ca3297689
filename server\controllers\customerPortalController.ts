import Stripe from "stripe";
import { Request, Response } from "express";
import pool from "../db";
import { AuthRequest, User, UserSubscription } from "../@types";
import { SubscriptionTable } from "./stripeController";

const stripe = new Stripe(
  process.env.NODE_ENV === "production"
    ? process.env.STRIPE_SK_LIVE!
    : process.env.STRIPE_SK_TEST!,
  { apiVersion: "2025-02-24.acacia" },
);

const getCustomerId = async (userId: string): Promise<string> => {
  // Get customerId form Database
  const customerIdObj = await pool.query<User>(
    "SELECT stripe_customer_id FROM users WHERE user_id=$1",
    [userId],
  );
  const customerId = customerIdObj.rows[0].stripe_customer_id;

  return customerId;
};

export const incomingInvocice = async (
  customerId: string,
  subscriptionId: string,
) => {
  try {
    const comingEvent = await stripe.invoices.retrieveUpcoming({
      customer: customerId,
      subscription: subscriptionId,
    });

    return comingEvent;
  } catch (err) {
    throw err;
  }
};

export const getSubscription = async (subscriptionId: string) => {
  const subscription = await stripe.subscriptions.retrieve(subscriptionId);
  return subscription;
};

export const getProduct = async (productId: string) => {
  const product = await stripe.products.retrieve(productId.toString());
  return product;
};

export const getAllInvoces = async (req: Request, res: Response) => {
  try {
    if (req.user == null) {
      return;
    }
    const user = req.user as User;
    const userId = user.user_id;
    const { limit } = req.query;

    const customerId = await getCustomerId(userId);
    const invoiceOptions: Stripe.InvoiceListParams = {
      customer: customerId,
    };

    if (typeof limit === "string") {
      invoiceOptions.limit = parseInt(limit);
    }

    // Get all Invoices
    const invoices = await stripe.invoices.list(invoiceOptions);

    res.status(200).json(invoices);
  } catch (err) {
    res.status(500).json(err);
  }
};

export const getInvoice = async (
  req: Request,
  res: Response,
  table: string,
) => {
  try {
    if (req.user == null) {
      return;
    }
    const user = req.user as User;
    const userId = user.user_id;

    const customerId = await getCustomerId(userId);

    const query = await pool.query<UserSubscription>(
      `SELECT subscription_id FROM ${table} WHERE customer_id=$1 AND active=$2`,
      [customerId, true],
    );
    if (query.rows.length === 0) {
      return;
    }
    const subscriptionId = query.rows[0].subscription_id;
    const comingEvent = await incomingInvocice(customerId, subscriptionId);

    const subscription = await getSubscription(subscriptionId);

    const invItemCount = comingEvent.lines.data.length - 1;

    const productId = comingEvent.lines.data[invItemCount].plan
      ?.product as string;

    if (!productId) return;
    const product = await getProduct(productId);
    res.status(200).json({ product, comingEvent, subscription });
  } catch (err: any) {
    console.log(err);
    res.status(err?.statusCode || 400).json(err.raw?.message);
  }
};

export const getComingInvoice = async (req: Request, res: Response) => {
  getInvoice(req, res, SubscriptionTable.UserSubscriptionTable);
};

export const getTransactionComingInvoice = async (
  req: Request,
  res: Response,
) => {
  getInvoice(req, res, SubscriptionTable.TransactionSubscriptionTable);
};

export const getWozComingInvoice = async (req: Request, res: Response) => {
  getInvoice(req, res, SubscriptionTable.WozSubscriptionTable);
};

export const getObjectDataComingInvoice = async (
  req: Request,
  res: Response,
) => {
  getInvoice(req, res, SubscriptionTable.ObjectDataSubscriptionTable);
};
export const getAVMComingInvoice = async (req: Request, res: Response) => {
  getInvoice(req, res, SubscriptionTable.AVMSubscriptionTable);
};
export const getReferenceComingInvoice = async (
  req: Request,
  res: Response,
) => {
  getInvoice(req, res, SubscriptionTable.ReferenceSubscriptionTable);
};

export const getECOComingInvoice = async (req: Request, res: Response) => {
  getInvoice(req, res, SubscriptionTable.ECOSubscriptionTable);
};
export const getEnergyComingInvoice = async (req: Request, res: Response) => {
  getInvoice(req, res, SubscriptionTable.EnergySubscriptionTable);
};

export const getCardPaymentMethods = async (req: Request, res: Response) => {
  try {
    if (req.user == null) {
      return;
    }
    const user = req.user as User;
    const customerId = user.stripe_customer_id;

    const cardPaymentMethod = await stripe.paymentMethods.list({
      customer: customerId,
      type: "card",
      limit: 3,
    });
    res.status(200).json(cardPaymentMethod);
  } catch (err: any) {
    res.status(500).json(err.raw.message);
  }
};

export const getSepaPaymentMethods = async (req: Request, res: Response) => {
  try {
    if (req.user == null) {
      return;
    }
    const user = req.user as User;
    const customerId = user.stripe_customer_id;

    const sepaPaymentMethod = await stripe.paymentMethods.list({
      customer: customerId,
      type: "sepa_debit",
      limit: 2,
    });

    res.status(200).json(sepaPaymentMethod);
  } catch (err: any) {
    res.status(500).json(err.raw.message);
  }
};

export const getDefaultPaymentMethod = async (req: Request, res: Response) => {
  try {
    if (req.user == null) {
      return;
    }
    const user = req.user as User;
    const customerId = user.stripe_customer_id;
    const defaultPaymentMethod = await pool.query(
      `SELECT payment_method_id, MAX(date) as "max-date" FROM stripe_billing WHERE customer_id =$1 GROUP BY payment_method_id`,
      [customerId],
    );
    if (defaultPaymentMethod.rowCount) {
      res
        .status(200)
        .json(defaultPaymentMethod.rows[defaultPaymentMethod.rowCount - 1]);
    } else res.status(200).json([]);
  } catch (err) {
    console.log(err);
    res.status(500).json(err);
  }
};

export const editPaymentDetails = async (req: Request, res: Response) => {
  try {
    const paymentMethod = await stripe.paymentMethods.update(req.params.id, {
      card: { exp_month: req.body.expMonth, exp_year: req.body.expYear },
    });
    res.status(200).json({
      message: "editing successful",
      success: true,
    });
  } catch (err: any) {
    console.log(err);
    res.status(500).json(err.raw.message);
  }
};

export const createVatId = async (req: Request, res: Response) => {
  try {
    if (req.user == null) {
      return;
    }
    const user = req.user as User;
    const taxId = await stripe.customers.createTaxId(user.stripe_customer_id, {
      type: "eu_vat",
      value: req.body.vat,
    });
    res.status(201).json({ taxId });
  } catch (err: any) {
    console.log(err);
    res.status(500).json(err.raw.message);
  }
};

export const getVatId = async (req: Request, res: Response) => {
  try {
    if (req.user == null) {
      return;
    }
    const user = req.user as User;
    const taxIds = await stripe.customers.listTaxIds(user.stripe_customer_id, {
      limit: 1,
    });
    res.status(200).json({ taxIds });
  } catch (err: any) {
    console.log(err);
    res.status(500).json(err.raw.message);
  }
};
