export function camelCaseToReadable(str: string): string {
  return str
    .replace(/_/g, " ") // Replace underscores with spaces
    .replace(/([a-z])([A-Z])/g, "$1 $2") // Split camelCase words
    .replace(/([A-Z])([A-Z][a-z])/g, "$1 $2") // Handle acronyms
    .split(" ") // Split by space
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1)) // Capitalize first letter of each word
    .join(" "); // Join words into a single string
}
