user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;

    # Enhanced Security Headers
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    
    # Enhanced CSP for static content with frame-ancestors and other missing directives
    add_header Content-Security-Policy "
        default-src 'self';
        script-src 'self' https://js.stripe.com https://www.googletagmanager.com https://www.google-analytics.com;
        style-src 'self' https://fonts.googleapis.com;
        img-src 'self' data: https:;
        connect-src 'self' https://api.mopsus.altum.ai https://api.stripe.com https://www.google-analytics.com;
        font-src 'self' https://fonts.gstatic.com data:;
        object-src 'none';
        media-src 'self';
        frame-src 'self' https://js.stripe.com;
        frame-ancestors 'none';
        base-uri 'self';
        form-action 'self';
        manifest-src 'self';
        worker-src 'self';
        upgrade-insecure-requests
    " always;
    
    # Additional security measures
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=(), interest-cohort=()" always;
    add_header Cross-Origin-Embedder-Policy "require-corp" always;
    add_header Cross-Origin-Opener-Policy "same-origin" always;
    add_header Cross-Origin-Resource-Policy "same-origin" always;

    # Enhanced server information hiding
    server_tokens off;
    more_clear_headers 'Server';
    more_clear_headers 'X-Powered-By';
    proxy_hide_header 'X-Powered-By';
    proxy_hide_header 'X-AspNet-Version';
    proxy_hide_header 'X-AspNetMvc-Version';

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

    # Upstream configuration
    upstream nodejs_backend {
        server 127.0.0.1:5000;
        keepalive 32;
    }

    # HTTP redirect server
    server {
        listen 80;
        server_name _;
        return 301 https://$host$request_uri;
    }

    # HTTPS server
    server {
        listen 443 ssl;
        server_name _;
        root /app/dist/build;
        index index.html;

        # SSL configuration
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;
        ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;

        # Security configurations
        client_max_body_size 10M;
        client_body_timeout 60s;
        client_header_timeout 60s;
        large_client_header_buffers 4 16k;

        # Block access to version control and sensitive files
        location ~ /\.(git|hg|svn|bzr|cvs|idea|vscode) {
            deny all;
            return 404;
            access_log off;
            log_not_found off;
        }

        # Block access to dot files and sensitive extensions
        location ~ /\.(?!well-known) {
            deny all;
            return 404;
            access_log off;
            log_not_found off;
        }

        # Block access to sensitive files
        location ~ \.(env|log|sql|md|yml|yaml|conf|config|bak|backup|ini|sh|bash|xml)$ {
            deny all;
            return 404;
            access_log off;
            log_not_found off;
        }

        # API routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://nodejs_backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
        }

        # Auth endpoints
        location ~ ^/api/v1/(auth|login|register|2fa) {
            limit_req zone=login burst=5 nodelay;
            proxy_pass http://nodejs_backend;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Static files with caching
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Content-Type-Options "nosniff" always;
            try_files $uri =404;
        }

        # React app routes
        location / {
            try_files $uri $uri/ /index.html;
            location ~* \.html$ {
                expires -1;
                add_header Cache-Control "no-cache, no-store, must-revalidate";
                add_header Pragma "no-cache";
            }
        }

        # Health check
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Error pages
        error_page 404 /index.html;
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}
