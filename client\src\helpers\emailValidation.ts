// List of blocked forwarding email domains
export const BLOCKED_EMAIL_DOMAINS = [
  // 10 Minute Mail variants
  "10minutemail.com",
  "10minutemail.net",
  "10minutemail.org",

  // Temp Mail variants
  "temp-mail.org",
  "tempmail.com",
  "tempmail.net",
  "tempmail.org",
  "tempr.email",
  "tempinbox.co.uk",
  "tempinbox.com",
  "temporaryinbox.com",
  "tempemail.biz",
  "tempemail.com",
  "tempemail.net",
  "tempmail.dev",
  "tempmail.ninja",
  "tempmail.plus",
  "tempmailo.com",
  "minuteinbox.com",
  "tempmailin.com",
  "tempmail.altmails.com",
  ...Array.from(
    { length: 40 },
    (_, i) =>
      `tempmail.${
        [
          "co",
          "io",
          "app",
          "wiki",
          "cloud",
          "live",
          "space",
          "tech",
          "digital",
          "online",
          "site",
          "network",
          "center",
          "guru",
          "info",
          "fun",
          "design",
          "today",
          "agency",
          "services",
          "solutions",
          "systems",
          "works",
          "expert",
          "directory",
          "guide",
          "consulting",
          "management",
          "marketing",
          "media",
          "studio",
          "technology",
          "tools",
          "ventures",
          "vision",
          "wtf",
          "xyz",
          "zone",
          "email",
        ][i] || ""
      }`,
  ),

  // Trash Mail variants
  "throwawaymail.com",
  "trashmail.com",
  "trashmail.net",
  "trashmail.at",
  "trashmail.me",
  "trashmail.ws",
  "trashymail.com",
  "trashymail.net",
  "trash2009.com",
  "trashdevil.com",
  "trashdevil.de",
  "mytrashmail.com",

  // Mailinator variants
  "mailinator.com",
  "mailinator.net",

  // Guerrilla Mail variants
  "guerrillamail.com",
  "guerrillamail.net",
  "guerrillamail.org",
  "guerrillamail.biz",
  "sharklasers.com",
  "grr.la",

  // Yopmail variants
  "yopmail.com",
  "yopmail.net",
  "yopmail.fr",

  // Mail Drop variants
  "maildrop.cc",
  "spamgourmet.com",
  "spamgourmet.net",
  "spamgourmet.org",

  // Jetable variants
  "jetable.org",
  "jetable.net",
  "jetable.com",

  // Disposable variants
  "dispostable.com",
  "discard.email",
  "discardmail.com",
  "discardmail.de",

  // Spambog variants
  "spambog.com",
  "spambog.de",
  "spambog.ru",

  // Forward Mail Services
  "forward.cat",
  "forward.email",
  "forwardemail.net",
  "anonaddy.com",
  "anonaddy.me",
  "simplelogin.co",
  "simplelogin.io",
  "relay.firefox.com",
  "duck.com",
  "relay.duck.com",

  // Email Relay/Hide Services
  "hidemy.name",
  "hidemail.pro",
  "burnermail.io",
  "email-relay.com",

  // Additional Disposable Services
  "mailnesia.com",
  "mailnull.com",
  "spam4.me",
  "nospam.ze.tc",
  "nomail.xl.cx",
  "mega.zik.dj",
  "speed.1s.fr",
  "courriel.fr.nf",
  "moncourrier.fr.nf",
  "monemail.fr.nf",
  "monmail.fr.nf",

  // More Disposable Services
  "0815.ru",
  "0wnd.net",
  "0wnd.org",
  "33mail.com",
  "anonymail.dk",
  "anonymizer.com",
  "bloggeremail.com",
  "bugmenot.com",
  "deadaddress.com",
  "dodgeit.com",
  "e4ward.com",
  "emailias.com",
  "fakeinbox.com",
  "fastmail.fm",
  "filzmail.com",
  "fudgerub.com",
  "get2mail.fr",
  "getonemail.com",
  "gishpuppy.com",
  "great-host.in",
  "incognitomail.com",
  "kasmail.com",
  "keemail.me",
  "kleemail.com",
  "koszmail.pl",
  "kurzepost.de",
  "lavabit.com",
  "letthemeatspam.com",
  "lhsdv.com",
  "lifebyfood.com",
  "link2mail.net",
  "lortemail.dk",
  "mintemail.com",
  "myspamless.com",
  "netmails.net",
  "netzidiot.de",
  "no-spam.ws",
  "nobulk.com",
  "noclickemail.com",
  "nowmymail.com",
  "objectmail.com",
  "obobbo.com",
  "oneoffemail.com",
  "onewaymail.com",
  "otherinbox.com",
  "pjjkp.com",
  "politikerclub.de",
  "pookmail.com",
  "privacy.net",
  "proxymail.eu",
  "prtnx.com",
  "putthisinyourspamdatabase.com",
  "quickinbox.com",
  "rcpt.at",
  "recode.me",
  "recursor.net",
  "regbypass.com",
  "safetymail.info",
  "sandelf.de",
  "saynotospams.com",
  "selfdestructingmail.com",
  "sendspamhere.com",
  "shiftmail.com",
  "shitmail.me",
  "shitmail.org",
  "sneakemail.com",
  "sofort-mail.de",
  "sogetthis.com",
  "soodonims.com",
  "spam.la",
  "spam.su",
  "spamavert.com",
  "spambox.info",
  "spambox.us",
  "spamcannon.com",
  "spamcannon.net",
  "spamcero.com",
  "spamcon.org",
  "spamcorptastic.com",
  "spamday.com",
  "spamex.com",
  "spamobox.com",
  "spamslicer.com",
  "spamspot.com",
  "spamthis.co.uk",
  "spamthisplease.com",
  "spamtrail.com",
  "supergreatmail.com",
  "supermailer.jp",
  "suremail.info",
  "teewars.org",
  "teleworm.com",
  "thankyou2010.com",
  "thisisnotmyrealemail.com",
  "throwawayemailaddress.com",
  "tilien.com",
  "tmailinator.com",
  "toiea.com",
  "trashemail.de",
  "turual.com",
  "twinmail.de",
  "tyldd.com",
  "uggsrock.com",
  "upliftnow.com",
  "venompen.com",
  "wegwerfadresse.de",
  "wegwerfemail.com",
  "wegwerfemail.de",
  "wegwerfemail.net",
  "wegwerfemail.org",
  "wetrainbayarea.com",
  "wetrainbayarea.org",
  "wh4f.org",
  "whyspam.me",
  "willhackforfood.biz",
  "willselfdestruct.com",
  "winemaven.info",
  "wronghead.com",
  "wuzup.net",
  "wuzupmail.net",
  "xemaps.com",
  "xents.com",
  "xmaily.com",
  "xoxy.net",
  "yep.it",
  "yogamaven.com",
  "yuurok.com",
  "zehnminutenmail.de",
  "zippymail.info",
  "zoaxe.com",
  "zoemail.net",
  "zoemail.org",
  "passmail.com",
  "passmail.net",
  "passmail.org",
  "passmails.com",
  "passmails.net",
  "passmails.org",
  "passmails.co.uk",
  "passmails.info",
  "passmails.me",
  "passmails.us",
  "passmails.cc",
  "passmails.pro",
  "passmails.biz",
  "passmails.name",
  "passmails.asia",
  "passmails.tel",
  "passmails.mobi",
  "passmails.cc.uk",
];

/**
 * Checks if an email address uses a blocked forwarding domain
 * @param email The email address to check
 * @returns true if the email uses a blocked domain, false otherwise
 */
export const isBlockedEmailDomain = (email: string): boolean => {
  const domain = email.split("@")[1]?.toLowerCase();
  return BLOCKED_EMAIL_DOMAINS.includes(domain);
};
