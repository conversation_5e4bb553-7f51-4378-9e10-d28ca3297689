import { Router } from "express";
import { protect } from "../controllers/authController";
import {
  getAllInvoces,
  getComingInvoice,
  getDefaultPaymentMethod,
  editPaymentDetails,
  getTransactionComingInvoice,
  getCardPaymentMethods,
  getSepaPaymentMethods,
  getWozComingInvoice,
  getObjectDataComingInvoice,
  getAVMComingInvoice,
  getReferenceComingInvoice,
  getECOComingInvoice,
  getEnergyComingInvoice,
} from "../controllers/customerPortalController";

const router = Router();

router.route("/invoices").get(protect, getAllInvoces);
router.route("/coming-invoice").get(protect, getComingInvoice);
router.route("/payment-methods").get(protect, getDefaultPaymentMethod);
router.route("/card-payment-methods").get(protect, getCardPaymentMethods);
router.route("/sepa-payment-methods").get(protect, getSepaPaymentMethods);
router.route("/payment-details/:id").patch(protect, editPaymentDetails);

// Transaction Plan
router
  .route("/coming-transaction-invoice")
  .get(protect, getTransactionComingInvoice);
router.route("/coming-woz-invoice").get(protect, getWozComingInvoice);
router
  .route("/coming-object-data-invoice")
  .get(protect, getObjectDataComingInvoice);

router.route("/coming-avm-invoice").get(protect, getAVMComingInvoice);
router
  .route("/coming-reference-invoice")
  .get(protect, getReferenceComingInvoice);

router
  .route("/coming-sustainability-invoice")
  .get(protect, getECOComingInvoice);
router
  .route("/coming-energy-label-invoice")
  .get(protect, getEnergyComingInvoice);

export default router;
