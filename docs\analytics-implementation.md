# Analytics Implementation Plan

This document outlines the plan for enhancing the analytics features in our application.

## Overview
The current analytics implementation in the adminController provides basic metrics. However, enhancements are needed to align with the detailed dashboard requirements.

## Scope
This implementation will enhance tracking and reporting for:
- Active Users and Trend Graphing
- New Users/Signups with Growth Metrics
- Property AI Generations Metrics
- Detailed Onboarding Funnel Analysis

## Current State
Existing endpoints include:
- getActiveUsersAnalytics
- getMopsusAnalytics and getMopsusLineChartAnalytics
- getPropertyGenerationStats
- getOnboardingFunnelAnalytics

These endpoints provide basic metrics but lack detailed breakdowns and trend analysis.

## Enhancement Requirements
1. **Active Users Tracking & Trends:**  
   Include detailed logs to display active user counts along with growth trends and timestamps.

2. **New Users/Signups Analytics:**  
   Aggregate signup events, calculate the percentage growth, and display metrics for new user acquisitions.

3. **Property AI Generations Tracking:**  
   Log and report property generation events with accurate counts and percentage growth integration.

4. **Detailed Onboarding Funnel Analysis:**  
   Break down conversion rates at each stage – signup, email verification, onboarding completion, and API call initiation.

## Proposed Changes
- Modify and extend existing endpoints in adminController to integrate detailed logging via analytics_logs.
- Update authController to capture signup events.
- Enhance propertyDescriptionController to log property generation events.
- Refine onboarding analytics in onboardingController using detailed funnel data.

## Logging Enhancements
Centralize analytics logging to allow real-time calculations and trend analysis across multiple endpoints.

## Implementation Steps
1. Update logging mechanisms to capture detailed event data.
2. Enhance data aggregation in service and controller layers.
3. Modify existing analytics endpoints for detailed metrics.
4. Deploy updated endpoints in a staging environment and perform testing.

## Future Considerations
- Integration of automated dashboards and alert systems once detailed data is validated.
- Regular review cycles to refine analytics metrics based on user feedback.

## Timeline
- **Phase 1:** Logging enhancements (1-2 weeks)
- **Phase 2:** Endpoint modifications and testing (2-3 weeks)
- **Phase 3:** Dashboard integration and review (1 week)

## Next Steps
Review this document with the team, finalize the requirements, and initiate the implementation process.

End of Document.