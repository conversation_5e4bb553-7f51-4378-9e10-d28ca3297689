import { FormEvent, useEffect } from "react";
import { AvmFields } from "./avmfields";
import FormBody from "../FormBody";
import { AVM } from "../../../assets/images/api/APIimages";
import { postAvmDetails } from "../../../redux/actions/avmActions";
import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { FormOptions, FormOptionsSustainability } from "../../../@types";
import { tryParseInt } from "../../../helpers/tryParseInt";
import { useFormContext } from "../../../pages/Api/usage-pages/components/FormContext";

const AvmForm = () => {
  const sectionFields = [
    { title: "Welke woning wil je waarderen?", startIndex: 0, endIndex: 6 },
  ];
  const savedQueries = useAppSelector((state) => state.avm.savedQueries) as any;

  const apiKey = useAppSelector((state) => state.auth.user?.api_key);

  const dispatch = useAppDispatch();

  const { formValues, setFormValues } = useFormContext();

  useEffect(() => {
    const sliderCircle = document.querySelector(
      "#energylabel > div:last-child > div:last-child",
    ) as HTMLElement;
    const Track1 = document.querySelector(
      "#energylabel > div:first-child > div",
    );
    if (Track1) {
      Track1 && Track1.classList.add("energylabel-track");
    }
    if (sliderCircle) {
      sliderCircle.classList.add("form-slider");
      sliderCircle.style.backgroundColor = "rgba(0,166,82,255)";
      sliderCircle.textContent = savedQueries?.energylabel || "A";
    }
  }, [savedQueries?.energylabel]);
  useEffect(() => {
    if (Object.keys(savedQueries).length > 0) {
      setFormValues({
        ...(savedQueries as FormOptions),
      });
    }
  }, [savedQueries, setFormValues]);
  const handleFormSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const newFormData: FormOptions | FormOptionsSustainability = {
      postcode: "",
      housenumber: "",
    };
    formValues.postcode = formValues.postcode.split(" ").join("").toUpperCase();
    Object.entries(formValues).forEach(([key, value]) => {
      if (value !== undefined) {
        const [isInt, inputValue] = tryParseInt(value);
        newFormData[key as keyof (FormOptions | FormOptionsSustainability)] =
          isInt ? inputValue : value;
      }
    });
    apiKey && dispatch(postAvmDetails({ formData: newFormData, apiKey }));
  };

  return (
    <FormBody
      title={"Woningwaarde model"}
      desc={
        "Maak gebruik van de Woningwaarde API om de huidige marktwaarde van een huis te weten te komen. De API gebruikt gegevens uit openbare registers, die maandelijks worden bijgewerkt met nieuw geregistreerde verkoopprijzen."
      }
      path={"https://docs.altum.ai/apis/woningwaarde+-api"}
      img={AVM}
      sectionFields={sectionFields}
      initialFields={AvmFields}
      handleSubmit={handleFormSubmit}
    />
  );
};

export default AvmForm;
