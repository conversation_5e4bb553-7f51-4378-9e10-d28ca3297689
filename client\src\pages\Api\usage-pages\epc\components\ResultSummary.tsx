import { FC, useState } from "react";
import Text from "../../../../../components/Text";
import Subtitle from "../../../../../components/Subtitle";
import Button from "../../../../../components/Button";
import Modal from "../../../../../components/Modal";
import { FaFilePdf, FaLeaf } from "react-icons/fa";
import { IoMdEye } from "react-icons/io";
import { EPCResult } from "../types";

type Props = {
  property: EPCResult;
  buildingPhoto?: string;
  map?: string;
};

export const ResultSummary: FC<Props> = ({
  property,
  buildingPhoto = "",
  map = "",
}) => {
  const [isPreviewModalVisible, setIsPreviewModalVisible] = useState(false);

  const formatValue = (value: any, fieldName?: string): string => {
    if (value === null || value === undefined) return "-";

    // Don't format these specific fields
    const noFormatFields = ["post_code", "build_year", "house_number"];
    if (fieldName && noFormatFields.includes(fieldName)) {
      return value.toString();
    }

    if (typeof value === "number") {
      return value.toLocaleString("nl-NL", {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      });
    }
    if (typeof value === "string" && !isNaN(parseFloat(value))) {
      const numValue = parseFloat(value);
      return numValue.toLocaleString("nl-NL", {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      });
    }
    return value;
  };

  const mapPropertyData = (property: EPCResult) => {
    return {
      post_code: property.post_code,
      house_number: formatValue(property.house_number, "house_number"),
      house_addition: property.house_addition || "",
      build_year: formatValue(property.build_year, "build_year"),
      inner_surface_area: formatValue(property.inner_surface_area),
      house_type: property.house_type,
      installation: formatValue(property.installation),
      wall_insulation: formatValue(property.wall_insulation),
      roof_insulation: formatValue(property.roof_insulation),
      floor_insulation: formatValue(property.floor_insulation),
      living_room_windows: formatValue(property.living_room_windows),
      bedroom_windows: formatValue(property.bedroom_windows),
      shower: formatValue(property.shower),
      ventilation: formatValue(property.ventilation),
      solar_panels: formatValue(property.solar_panels),
      solarpanel_watt_peak: formatValue(property.solarpanel_watt_peak),
      CO2: formatValue(property.CO2),
      definitive_energy_label: property.definitive_energy_label,
      definitive_energy_label_type: property.definitive_energy_label_type,
      definitive_energy_label_validity_date:
        property.definitive_energy_label_validity_date,
      definitive_BENG2_score: property.definitive_BENG2_score,
      current_estimated_energy_label: property.current_estimated_energy_label,
      current_estimated_BENG1_score: formatValue(
        property.current_estimated_BENG1_score,
      ),
      current_estimated_BENG2_score: formatValue(
        property.current_estimated_BENG2_score,
      ),
    };
  };

  const desc = [
    {
      icon: <FaLeaf size={24} />,
      title: "Huidig energielabel",
      result: property?.current_estimated_energy_label || "-",
    },
    {
      icon: <FaFilePdf size={24} />,
      title: "Definitief energielabel",
      result: property?.definitive_energy_label || "-",
    },
  ];

  return (
    <>
      {/* Property Images Section */}
      {(buildingPhoto || map) && (
        <div className="bg-white shadow-[0px_2px_8px_0px_#00000026] rounded-md p-4 md:p-6 absolute lg:top-[120px] lg:left-[250px] lg:w-[496px] md:top-[80px] md:left-[110px] md:w-[409px] top-[80px] left-[40px] w-[300px]">
          <Text className="font-semibold mb-4 text-center">
            Woning Afbeeldingen
          </Text>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {buildingPhoto && (
              <div className="flex flex-col items-center">
                <Text className="text-sm text-gray-600 mb-2">Straatbeeld</Text>
                <img
                  src={buildingPhoto}
                  alt="Building street view"
                  className="w-full h-32 object-cover rounded-md border"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src =
                      "/images/empty-image.svg";
                  }}
                />
              </div>
            )}
            {map && (
              <div className="flex flex-col items-center">
                <Text className="text-sm text-gray-600 mb-2">Kaart</Text>
                <img
                  src={map}
                  alt="Property location map"
                  className="w-full h-32 object-cover rounded-md border"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src =
                      "/images/empty-image.svg";
                  }}
                />
              </div>
            )}
          </div>
        </div>
      )}

      {/* EPC Summary Data */}
      <div
        className={`bg-white shadow-[0px_2px_8px_0px_#00000026] rounded-md md:flex md:flex-wrap justify-between p-4 md:p-6 absolute ${
          buildingPhoto || map
            ? "lg:top-[420px] lg:left-[250px] lg:w-[496px] md:top-[320px] md:left-[110px] md:w-[409px] top-[320px] left-[40px] w-[300px]"
            : "lg:top-[320px] lg:left-[250px] lg:w-[496px] md:top-[200px] md:left-[110px] md:w-[409px] top-[200px] left-[40px] w-[300px]"
        } grid grid-cols-2 gap-2`}
      >
        {desc.map(({ icon, title, result }, index) => (
          <div key={index} className="flex flex-col items-center gap-2">
            <Text className="">{title}</Text>
            <Subtitle className="flex text-base md:text-xl">{result}</Subtitle>
          </div>
        ))}
      </div>

      <div
        className={`flex absolute ${
          buildingPhoto || map
            ? "lg:top-[490px] lg:left-[300px] md:top-[390px] md:left-[230px] top-[390px] left-[100px]"
            : "lg:top-[390px] lg:left-[300px] md:top-[250px] md:left-[230px] top-[250px] left-[100px]"
        } gap-2`}
      >
        <Button
          type="button"
          onClick={() => setIsPreviewModalVisible(true)}
          className="ml-2 bg-white text-primary border border-primary flex items-center"
          size="xl"
        >
          <IoMdEye size={20} className="text-primary" />
          <span className="hidden lg:inline ml-2">Preview PDF</span>
        </Button>

        {/* TODO: Add EPC PDF Download component when available */}
        <Button
          type="button"
          className="bg-primary text-white flex items-center"
          size="xl"
          disabled
        >
          <FaFilePdf size={20} />
          <span className="hidden lg:inline ml-2">Download PDF</span>
        </Button>
      </div>

      <Modal
        trigger={isPreviewModalVisible}
        setTrigger={setIsPreviewModalVisible}
      >
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-4">EPC Data Preview</h3>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {Object.entries(mapPropertyData(property)).map(([key, value]) => (
              <div key={key} className="flex justify-between">
                <span className="font-medium">{key.replace(/_/g, " ")}:</span>
                <span>{value || "-"}</span>
              </div>
            ))}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ResultSummary;
