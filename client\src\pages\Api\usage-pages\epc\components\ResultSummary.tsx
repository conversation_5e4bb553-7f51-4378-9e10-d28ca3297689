import { FC, useState } from "react";
import Text from "../../../../../components/Text";
import Subtitle from "../../../../../components/Subtitle";
import Button from "../../../../../components/Button";
import Modal from "../../../../../components/Modal";
import { FaFilePdf, FaLeaf } from "react-icons/fa";
import { IoMdEye } from "react-icons/io";
import { EPCResult } from "../types";

type Props = {
  property: EPCResult;
  buildingPhoto?: string;
  map?: string;
};

export const ResultSummary: FC<Props> = ({
  property,
  buildingPhoto = "",
  map = "",
}) => {
  const [isPreviewModalVisible, setIsPreviewModalVisible] = useState(false);

  const mapPropertyData = (property: EPCResult) => {
    return {
      post_code: property.post_code,
      house_number: property.house_number?.toString() || "",
      house_addition: property.house_addition || "",
      build_year: property.build_year?.toString() || "",
      inner_surface_area: property.inner_surface_area?.toString() || "",
      house_type: property.house_type,
      installation: property.installation?.toString() || "",
      wall_insulation: property.wall_insulation?.toString() || "",
      roof_insulation: property.roof_insulation?.toString() || "",
      floor_insulation: property.floor_insulation?.toString() || "",
      living_room_windows: property.living_room_windows?.toString() || "",
      bedroom_windows: property.bedroom_windows?.toString() || "",
      shower: property.shower?.toString() || "",
      ventilation: property.ventilation?.toString() || "",
      solar_panels: property.solar_panels?.toString() || "",
      solarpanel_watt_peak: property.solarpanel_watt_peak?.toString() || "",
      CO2: property.CO2?.toString() || "",
      definitive_energy_label: property.definitive_energy_label,
      definitive_energy_label_type: property.definitive_energy_label_type,
      definitive_energy_label_validity_date:
        property.definitive_energy_label_validity_date,
      definitive_BENG2_score: property.definitive_BENG2_score,
      current_estimated_energy_label: property.current_estimated_energy_label,
      current_estimated_BENG1_score:
        property.current_estimated_BENG1_score?.toString() || "",
      current_estimated_BENG2_score:
        property.current_estimated_BENG2_score?.toString() || "",
    };
  };

  const desc = [
    {
      icon: <FaLeaf size={24} />,
      title: "Huidig energielabel",
      result: property?.current_estimated_energy_label || "-",
    },
    {
      icon: <FaFilePdf size={24} />,
      title: "Definitief energielabel",
      result: property?.definitive_energy_label || "-",
    },
  ];

  return (
    <>
      <div className="bg-white shadow-[0px_2px_8px_0px_#00000026] rounded-md md:flex md:flex-wrap justify-between p-4 md:p-6 absolute lg:top-[320px] lg:left-[250px] lg:w-[496px] md:top-[200px] md:left-[110px] md:w-[409px] top-[200px] left-[40px] w-[300px] grid grid-cols-2 gap-2">
        {desc.map(({ icon, title, result }, index) => (
          <div key={index} className="flex flex-col items-center gap-2">
            <Text className="">{title}</Text>
            <Subtitle className="flex text-base md:text-xl">{result}</Subtitle>
          </div>
        ))}
      </div>

      <div className="flex absolute lg:top-[390px] lg:left-[300px] md:top-[250px] md:left-[230px] top-[250px] left-[100px] gap-2">
        <Button
          type="button"
          onClick={() => setIsPreviewModalVisible(true)}
          className="ml-2 bg-white text-primary border border-primary flex items-center"
          size="xl"
        >
          <IoMdEye size={20} className="text-primary" />
          <span className="hidden lg:inline ml-2">Preview PDF</span>
        </Button>

        {/* TODO: Add EPC PDF Download component when available */}
        <Button
          type="button"
          className="bg-primary text-white flex items-center"
          size="xl"
          disabled
        >
          <FaFilePdf size={20} />
          <span className="hidden lg:inline ml-2">Download PDF</span>
        </Button>
      </div>

      <Modal
        trigger={isPreviewModalVisible}
        setTrigger={setIsPreviewModalVisible}
      >
        <div className="p-4">
          <h3 className="text-lg font-semibold mb-4">EPC Data Preview</h3>
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {Object.entries(mapPropertyData(property)).map(([key, value]) => (
              <div key={key} className="flex justify-between">
                <span className="font-medium">{key.replace(/_/g, " ")}:</span>
                <span>{value || "-"}</span>
              </div>
            ))}
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ResultSummary;
