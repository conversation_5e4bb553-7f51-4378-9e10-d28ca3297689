import { Client } from "@elastic/elasticsearch";

const client = new Client({
  node: process.env.ELASTIC_SEARCH_URL,
});

export async function getEsOutput(houseAddress: string) {
  try {
    const houseAddressDataFromEs = {
      query: {
        bool: {
          must: {
            match_phrase: { houseaddress: houseAddress },
          },
        },
      },
    };
    const response = await client.search({
      index: process.env.BAG_ID,
      body: houseAddressDataFromEs,
      size: 1,
    });
    return response.hits.hits[0]._source;
  } catch (error) {
    console.error("Error executing search query", error);
    throw "Unable to find address";
  }
}
