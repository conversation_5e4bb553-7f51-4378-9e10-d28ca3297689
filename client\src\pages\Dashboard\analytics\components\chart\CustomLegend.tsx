import React from "react";
import Text from "../../../../../components/Text";
import apiColorPicker from "../../../../../helpers/apiColorPicker";
import apiNameConverter from "../../../../../helpers/apiNameConverter";

type Props = {
  legendKey: string[];
};

const CustomLegend = (props: Props) => {
  return (
    <div className="flex max-w-[100%] flex-wrap justify-center gap-2 self-end lg:max-w-[95%] ">
      {props.legendKey
        .filter((item) => item !== "ReferenceAPI")
        .map((key, index) => (
          <div className="flex items-center gap-2" key={index}>
            <div
              className={`w-2 h-2 rounded-full`}
              style={{
                backgroundColor: apiColorPicker(key),
              }}
            ></div>
            <Text>{apiNameConverter(key)}</Text>
          </div>
        ))}
    </div>
  );
};

export default CustomLegend;
