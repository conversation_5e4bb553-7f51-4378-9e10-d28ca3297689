import { FC, useState } from "react";
import { IoMdArrowDropleft, IoMdArrowDropright } from "react-icons/io";
import Text from "../../../../../components/Text";
import Subtitle from "../../../../../components/Subtitle";
import {
  MdOutlineRealEstateAgent,
  MdLocationOn,
  MdOutlineEco,
  MdInfo,
} from "react-icons/md";
import { RiHomeSmileLine } from "react-icons/ri";
import { FaSolarPanel } from "react-icons/fa";
import { EPCResult } from "../types";

type Props = {
  property: EPCResult;
  buildingPhoto?: string;
  map?: string;
};

const Result: FC<Props> = ({ property }) => {
  const formatValue = (value: any): string => {
    if (value === null || value === undefined) return "-";
    if (typeof value === "number") return value.toString();
    return value;
  };

  const overview = [
    {
      category: "Woningdetails",
      icon: <MdOutlineRealEstateAgent className="w-8 h-8" />,
      data: [
        {
          title: "Basisinformatie",
          details: [
            {
              key: "Postcode",
              value: formatValue(property.post_code),
              unit: "",
            },
            {
              key: "Huisnummer",
              value: formatValue(property.house_number),
              unit: "",
            },
            {
              key: "Huisnummer toevoeging",
              value: formatValue(property.house_addition),
              unit: "",
            },
            {
              key: "Woningtype",
              value: formatValue(property.house_type),
              unit: "",
            },
            {
              key: "Bouwjaar",
              value: formatValue(property.build_year),
              unit: "",
            },
            {
              key: "Woonoppervlakte",
              value: formatValue(property.inner_surface_area),
              unit: "m²",
            },
          ],
        },
      ],
    },
    {
      category: "Energielabels",
      icon: <RiHomeSmileLine className="w-8 h-8" />,
      data: [
        {
          title: "Energie Informatie",
          details: [
            {
              key: "Huidig energielabel",
              value: formatValue(property.current_estimated_energy_label),
              unit: "",
            },
            {
              key: "Definitief energielabel",
              value: formatValue(property.definitive_energy_label),
              unit: "",
            },
            {
              key: "Energielabel type",
              value: formatValue(property.definitive_energy_label_type),
              unit: "",
            },
            {
              key: "Geldigheid energielabel",
              value: formatValue(
                property.definitive_energy_label_validity_date,
              ),
              unit: "",
            },
            {
              key: "BENG1 score",
              value: formatValue(property.current_estimated_BENG1_score),
              unit: "",
            },
            {
              key: "BENG2 score (huidig)",
              value: formatValue(property.current_estimated_BENG2_score),
              unit: "",
            },
            {
              key: "BENG2 score (definitief)",
              value: formatValue(property.definitive_BENG2_score),
              unit: "",
            },
          ],
        },
      ],
    },
    {
      category: "Isolatie & Installaties",
      icon: <MdOutlineEco className="w-8 h-8" />,
      data: [
        {
          title: "Isolatie Details",
          details: [
            {
              key: "Installatie",
              value: formatValue(property.installation),
              unit: "",
            },
            {
              key: "Muurisolatie",
              value: formatValue(property.wall_insulation),
              unit: "",
            },
            {
              key: "Dakisolatie",
              value: formatValue(property.roof_insulation),
              unit: "",
            },
            {
              key: "Vloerisolatie",
              value: formatValue(property.floor_insulation),
              unit: "",
            },
            {
              key: "Woonkamer ramen",
              value: formatValue(property.living_room_windows),
              unit: "",
            },
            {
              key: "Slaapkamer ramen",
              value: formatValue(property.bedroom_windows),
              unit: "",
            },
            {
              key: "Douche",
              value: formatValue(property.shower),
              unit: "",
            },
            {
              key: "Ventilatie",
              value: formatValue(property.ventilation),
              unit: "",
            },
          ],
        },
      ],
    },
    {
      category: "Duurzaamheid",
      icon: <FaSolarPanel className="w-8 h-8" />,
      data: [
        {
          title: "Duurzaamheidsmaatregelen",
          details: [
            {
              key: "Zonnepanelen",
              value: formatValue(property.solar_panels),
              unit: "m²",
            },
            {
              key: "Zonnepaneel watt peak",
              value: formatValue(property.solarpanel_watt_peak),
              unit: "Wp",
            },
            {
              key: "CO2 uitstoot",
              value: formatValue(property.CO2),
              unit: "kg/jaar",
            },
          ],
        },
      ],
    },
  ];

  // Add raw data section if available
  if (property.raw_data) {
    overview.push({
      category: "Gedetailleerde Informatie",
      icon: <MdInfo className="w-8 h-8" />,
      data: [
        {
          title: "Energie Details",
          details: [
            {
              key: "Adres",
              value: formatValue(property.raw_data.Adres),
              unit: "",
            },
            {
              key: "Energieklasse",
              value: formatValue(property.raw_data.Class),
              unit: "",
            },
            {
              key: "Compactheid",
              value: formatValue(property.raw_data.Compactheid),
              unit: "",
            },
            {
              key: "Verwarming",
              value: formatValue(property.raw_data.Verwarming),
              unit: "",
            },
            {
              key: "Warm water",
              value: formatValue(property.raw_data["Warm water"]),
              unit: "",
            },
            {
              key: "Zonneboiler",
              value: formatValue(property.raw_data.Zonneboiler),
              unit: "",
            },
            {
              key: "Ventilatie",
              value: formatValue(property.raw_data.Ventilatie),
              unit: "",
            },
            {
              key: "Koeling",
              value: formatValue(property.raw_data.Koeling),
              unit: "",
            },
            {
              key: "Energieverbruik",
              value: formatValue(property.raw_data.Energy),
              unit: "kWh/m²/jaar",
            },
            {
              key: "CO2 per m²",
              value: formatValue(property.raw_data["CO2 per m2"]),
              unit: "kg/m²/jaar",
            },
            {
              key: "Warmtebehoefte",
              value: formatValue(property.raw_data.Warmtebehoefte),
              unit: "kWh/m²/jaar",
            },
          ],
        },
      ],
    });
  }

  const [activeResult, setActiveResult] = useState<any>(overview[0]);
  const [activeIndex, setActiveIndex] = useState(0);

  const popupDetails = (
    <div
      className={`md:shadow-[0px_2px_8px_0px_#00000026] md:rounded-md ${
        Object.keys(activeResult).length > 0
          ? "w-full h-full max-h-[600px] p-4 overflow-auto"
          : "w-0"
      } `}
    >
      {Object.keys(activeResult).length > 0 && (
        <>
          {activeResult?.data?.map((res: any, index: number) => (
            <div key={index} className="mt-6">
              <div className="flex gap-2 items-center">
                <Subtitle className="text-base">{res.title}</Subtitle>
              </div>

              {res.details.map((detail: any, idx: number) => (
                <div className="flex justify-between mt-4" key={idx}>
                  <Text className="text-gray-light">{detail.key}</Text>
                  <Text className="text-gray-dark font-medium">
                    {detail.unit
                      ? `${detail.value} ${detail.unit}`
                      : detail.value}
                  </Text>
                </div>
              ))}
            </div>
          ))}
        </>
      )}
    </div>
  );

  return (
    <div className="md:flex md:gap-8 md:justify-between">
      <div className="flex flex-col gap-4">
        {overview.map((res, idx) => (
          <>
            <div
              className={`shadow-[0px_2px_8px_0px_#00000026] rounded-md flex justify-between items-center w-full min-w-[300px] h-[72px] p-3 cursor-pointer`}
              key={idx}
              onClick={() => {
                setActiveResult(res);
                setActiveIndex(idx);
              }}
            >
              <div className={`flex items-center gap-3`}>
                <div
                  className={`${
                    idx === activeIndex ? "text-[#FFC694]" : "text-[#FFC694]/50"
                  }`}
                >
                  {res.icon}
                </div>

                <Text
                  className={`font-semibold text-base ${
                    idx === activeIndex ? "text-gray-dark" : "text-gray-light"
                  }`}
                >
                  {res.category}
                </Text>
              </div>

              {idx === activeIndex ? (
                <IoMdArrowDropleft className="w-6 h-6 text-primary" />
              ) : (
                <IoMdArrowDropright className="w-6 h-6 text-gray-light" />
              )}
            </div>

            <div className={`md:hidden`}>
              {idx === activeIndex && popupDetails}
            </div>
          </>
        ))}
      </div>

      <div className="hidden md:block w-full">{popupDetails}</div>
    </div>
  );
};

export default Result;
