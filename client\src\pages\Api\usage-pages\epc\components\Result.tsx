import { FC, useState } from "react";
import { IoMdArrowDropleft, IoMdArrowDropright } from "react-icons/io";
import Text from "../../../../../components/Text";
import Subtitle from "../../../../../components/Subtitle";
import {
  MdOutlineRealEstateAgent,
  MdLocationOn,
  MdOutlineEco,
  MdInfo,
} from "react-icons/md";
import { RiHomeSmileLine } from "react-icons/ri";
import { FaSolarPanel } from "react-icons/fa";
import { EPCResult } from "../types";

type Props = {
  property: EPCResult;
  buildingPhoto?: string;
  map?: string;
};

const Result: FC<Props> = ({ property }) => {
  const formatValue = (value: any, fieldName?: string): string => {
    if (value === null || value === undefined) return "-";

    // Don't format these specific fields
    const noFormatFields = ["post_code", "build_year", "house_number"];
    if (fieldName && noFormatFields.includes(fieldName)) {
      return value.toString();
    }

    if (typeof value === "number") {
      return value.toLocaleString("nl-NL", {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      });
    }
    if (typeof value === "string" && !isNaN(parseFloat(value))) {
      const numValue = parseFloat(value);
      return numValue.toLocaleString("nl-NL", {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2,
      });
    }
    return value;
  };

  const formatInsulationValue = (
    opp: string,
    thermalValue: string,
    thermalType: "Rc" | "Uw" | "Ud",
  ): string => {
    const oppFormatted = formatValue(opp);
    const thermalFormatted = formatValue(thermalValue);
    return `${oppFormatted} m² (${thermalType}: ${thermalFormatted})`;
  };

  const overview = [
    {
      category: "Woningdetails",
      icon: <MdOutlineRealEstateAgent className="w-8 h-8" />,
      data: [
        {
          title: "Basisinformatie",
          details: [
            {
              key: "Postcode",
              value: formatValue(property.post_code, "post_code"),
              unit: "",
            },
            {
              key: "Huisnummer",
              value: formatValue(property.house_number, "house_number"),
              unit: "",
            },
            {
              key: "Huisnummer toevoeging",
              value: formatValue(property.house_addition),
              unit: "",
            },
            {
              key: "Woningtype",
              value: formatValue(property.house_type),
              unit: "",
            },
            {
              key: "Bouwjaar",
              value: formatValue(property.build_year, "build_year"),
              unit: "",
            },
            {
              key: "Woonoppervlakte",
              value: formatValue(property.inner_surface_area),
              unit: "m²",
            },
          ],
        },
      ],
    },
    {
      category: "Energielabels",
      icon: <RiHomeSmileLine className="w-8 h-8" />,
      data: [
        {
          title: "Energie Informatie",
          details: [
            {
              key: "Huidig energielabel",
              value: formatValue(property.current_estimated_energy_label),
              unit: "",
            },
            {
              key: "Definitief energielabel",
              value: formatValue(property.definitive_energy_label),
              unit: "",
            },
            {
              key: "Energielabel type",
              value: formatValue(property.definitive_energy_label_type),
              unit: "",
            },
            {
              key: "Geldigheid energielabel",
              value: formatValue(
                property.definitive_energy_label_validity_date,
              ),
              unit: "",
            },
            {
              key: "BENG1 score",
              value: formatValue(property.current_estimated_BENG1_score),
              unit: "",
            },
            {
              key: "BENG2 score (huidig)",
              value: formatValue(property.current_estimated_BENG2_score),
              unit: "",
            },
            {
              key: "BENG2 score (definitief)",
              value: formatValue(property.definitive_BENG2_score),
              unit: "",
            },
          ],
        },
      ],
    },
    {
      category: "Isolatie & Installaties",
      icon: <MdOutlineEco className="w-8 h-8" />,
      data: [
        {
          title: "Isolatie Details",
          details: [
            {
              key: "Installatie",
              value: formatValue(property.installation),
              unit: "",
            },
            {
              key: "Muurisolatie",
              value: formatValue(property.wall_insulation),
              unit: "",
            },
            {
              key: "Dakisolatie",
              value: formatValue(property.roof_insulation),
              unit: "",
            },
            {
              key: "Vloerisolatie",
              value: formatValue(property.floor_insulation),
              unit: "",
            },
            {
              key: "Woonkamer ramen",
              value: formatValue(property.living_room_windows),
              unit: "",
            },
            {
              key: "Slaapkamer ramen",
              value: formatValue(property.bedroom_windows),
              unit: "",
            },
            {
              key: "Douche",
              value: formatValue(property.shower),
              unit: "",
            },
            {
              key: "Ventilatie",
              value: formatValue(property.ventilation),
              unit: "",
            },
          ],
        },
      ],
    },
    {
      category: "Duurzaamheid",
      icon: <FaSolarPanel className="w-8 h-8" />,
      data: [
        {
          title: "Duurzaamheidsmaatregelen",
          details: [
            {
              key: "Zonnepanelen",
              value: formatValue(property.solar_panels),
              unit: "m²",
            },
            {
              key: "Zonnepaneel watt peak",
              value: formatValue(property.solarpanel_watt_peak),
              unit: "Wp",
            },
            {
              key: "CO2 uitstoot",
              value: formatValue(property.CO2),
              unit: "kg/jaar",
            },
          ],
        },
      ],
    },
  ];

  // Add raw data section if available
  if (property.raw_data) {
    overview.push({
      category: "Gedetailleerde Informatie",
      icon: <MdInfo className="w-8 h-8" />,
      data: [
        {
          title: "Energie Details",
          details: [
            {
              key: "Adres",
              value: formatValue(property.raw_data.Adres),
              unit: "",
            },
            {
              key: "Energieklasse",
              value: formatValue(property.raw_data.Class),
              unit: "",
            },
            {
              key: "Compactheid",
              value: formatValue(property.raw_data.Compactheid),
              unit: "",
            },
            {
              key: "Verwarming",
              value: formatValue(property.raw_data.Verwarming),
              unit: "",
            },
            {
              key: "Warm water",
              value: formatValue(property.raw_data["Warm water"]),
              unit: "",
            },
            {
              key: "Zonneboiler",
              value: formatValue(property.raw_data.Zonneboiler),
              unit: "",
            },
            {
              key: "Ventilatie",
              value: formatValue(property.raw_data.Ventilatie),
              unit: "",
            },
            {
              key: "Koeling",
              value: formatValue(property.raw_data.Koeling),
              unit: "",
            },
            {
              key: "Energieverbruik",
              value: formatValue(property.raw_data.Energy),
              unit: "kWh/m²/jaar",
            },
            {
              key: "CO2 per m²",
              value: formatValue(property.raw_data["CO2 per m2"]),
              unit: "kg/m²/jaar",
            },
            {
              key: "Warmtebehoefte",
              value: formatValue(property.raw_data.Warmtebehoefte),
              unit: "kWh/m²/jaar",
            },
          ],
        },
        // Add detailed insulation data if available
        ...(property.raw_data.Isolatie
          ? [
              {
                title: "Isolatie Details",
                details: [
                  // Gevels (Walls)
                  ...(property.raw_data.Isolatie.Gevels &&
                  Object.keys(property.raw_data.Isolatie.Gevels).length > 0
                    ? Object.entries(property.raw_data.Isolatie.Gevels).flatMap(
                        ([direction, walls]) =>
                          walls.map((wall: any, index: number) => ({
                            key: `Gevel ${direction}${
                              walls.length > 1 ? ` (${index + 1})` : ""
                            }`,
                            value: formatInsulationValue(
                              wall.Opp,
                              wall.Rc,
                              "Rc",
                            ),
                            unit: "",
                          })),
                      )
                    : []),
                  // Daken (Roofs)
                  ...(property.raw_data.Isolatie.Daken &&
                  Object.keys(property.raw_data.Isolatie.Daken).length > 0
                    ? Object.entries(property.raw_data.Isolatie.Daken).flatMap(
                        ([direction, roofs]) =>
                          roofs.map((roof: any, index: number) => ({
                            key: `Dak ${direction}${
                              roofs.length > 1 ? ` (${index + 1})` : ""
                            }`,
                            value: formatInsulationValue(
                              roof.Opp,
                              roof.Rc,
                              "Rc",
                            ),
                            unit: "",
                          })),
                      )
                    : []),
                  // Vloeren (Floors)
                  ...(property.raw_data.Isolatie.Vloeren &&
                  Object.keys(property.raw_data.Isolatie.Vloeren).length > 0
                    ? Object.entries(
                        property.raw_data.Isolatie.Vloeren,
                      ).flatMap(([type, floors]) =>
                        floors.map((floor: any, index: number) => ({
                          key: `Vloer ${type}${
                            floors.length > 1 ? ` (${index + 1})` : ""
                          }`,
                          value: formatInsulationValue(
                            floor.Opp,
                            floor.Rc,
                            "Rc",
                          ),
                          unit: "",
                        })),
                      )
                    : []),
                  // Ramen (Windows)
                  ...(property.raw_data.Isolatie.Ramen &&
                  Object.keys(property.raw_data.Isolatie.Ramen).length > 0
                    ? Object.entries(property.raw_data.Isolatie.Ramen).flatMap(
                        ([direction, windows]) =>
                          windows.map((window: any, index: number) => ({
                            key: `Raam ${direction}${
                              windows.length > 1 ? ` (${index + 1})` : ""
                            }`,
                            value: formatInsulationValue(
                              window.Opp,
                              window.Uw,
                              "Uw",
                            ),
                            unit: "",
                          })),
                      )
                    : []),
                  // Buitendeuren (Exterior doors)
                  ...(property.raw_data.Isolatie.Buitendeuren &&
                  Object.keys(property.raw_data.Isolatie.Buitendeuren).length >
                    0
                    ? Object.entries(
                        property.raw_data.Isolatie.Buitendeuren,
                      ).flatMap(([direction, doors]) =>
                        doors.map((door: any, index: number) => ({
                          key: `Buitendeur ${direction}${
                            doors.length > 1 ? ` (${index + 1})` : ""
                          }`,
                          value: formatInsulationValue(door.Opp, door.Ud, "Ud"),
                          unit: "",
                        })),
                      )
                    : []),
                ],
              },
            ]
          : []),
      ],
    });
  }

  const [activeResult, setActiveResult] = useState<any>(overview[0]);
  const [activeIndex, setActiveIndex] = useState(0);

  const popupDetails = (
    <div
      className={`md:shadow-[0px_2px_8px_0px_#00000026] md:rounded-md ${
        Object.keys(activeResult).length > 0
          ? "w-full h-full max-h-[600px] p-4 overflow-auto"
          : "w-0"
      } `}
    >
      {Object.keys(activeResult).length > 0 && (
        <>
          {activeResult?.data?.map((res: any, index: number) => (
            <div key={index} className="mt-6">
              <div className="flex gap-2 items-center">
                <Subtitle className="text-base">{res.title}</Subtitle>
              </div>

              {res.title === "Isolatie Details" ? (
                // Special rendering for insulation details
                <div className="mt-4">
                  <div className="bg-blue-50 p-3 rounded-md mb-4">
                    <Text className="text-sm text-blue-800">
                      <strong>Uitleg:</strong> Rc-waarde = thermische weerstand
                      (hoe hoger, hoe beter de isolatie). Uw/Ud-waarde =
                      warmtedoorgangscoëfficiënt (hoe lager, hoe beter de
                      isolatie).
                    </Text>
                  </div>
                  <div className="grid grid-cols-1 gap-3">
                    {res.details.map((detail: any, idx: number) => {
                      const isWall = detail.key.includes("Gevel");
                      const isRoof = detail.key.includes("Dak");
                      const isFloor = detail.key.includes("Vloer");
                      const isWindow = detail.key.includes("Raam");
                      const isDoor = detail.key.includes("Buitendeur");

                      let bgColor = "bg-gray-50";
                      let textColor = "text-gray-700";

                      if (isWall) {
                        bgColor = "bg-orange-50";
                        textColor = "text-orange-700";
                      } else if (isRoof) {
                        bgColor = "bg-blue-50";
                        textColor = "text-blue-700";
                      } else if (isFloor) {
                        bgColor = "bg-green-50";
                        textColor = "text-green-700";
                      } else if (isWindow) {
                        bgColor = "bg-cyan-50";
                        textColor = "text-cyan-700";
                      } else if (isDoor) {
                        bgColor = "bg-purple-50";
                        textColor = "text-purple-700";
                      }

                      return (
                        <div
                          key={idx}
                          className={`${bgColor} p-2 rounded border-l-4 ${textColor.replace(
                            "text-",
                            "border-",
                          )}`}
                        >
                          <div className="flex justify-between items-center">
                            <Text className={`font-medium ${textColor}`}>
                              {detail.key}
                            </Text>
                            <Text className={`${textColor} font-semibold`}>
                              {detail.value}
                            </Text>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              ) : (
                // Regular rendering for other details
                res.details.map((detail: any, idx: number) => (
                  <div className="flex justify-between mt-4" key={idx}>
                    <Text className="text-gray-light">{detail.key}</Text>
                    <Text className="text-gray-dark font-medium">
                      {detail.unit
                        ? `${detail.value} ${detail.unit}`
                        : detail.value}
                    </Text>
                  </div>
                ))
              )}
            </div>
          ))}
        </>
      )}
    </div>
  );

  return (
    <div className="md:flex md:gap-8 md:justify-between">
      <div className="flex flex-col gap-4">
        {overview.map((res, idx) => (
          <>
            <div
              className={`shadow-[0px_2px_8px_0px_#00000026] rounded-md flex justify-between items-center w-full min-w-[300px] h-[72px] p-3 cursor-pointer`}
              key={idx}
              onClick={() => {
                setActiveResult(res);
                setActiveIndex(idx);
              }}
            >
              <div className={`flex items-center gap-3`}>
                <div
                  className={`${
                    idx === activeIndex ? "text-[#FFC694]" : "text-[#FFC694]/50"
                  }`}
                >
                  {res.icon}
                </div>

                <Text
                  className={`font-semibold text-base ${
                    idx === activeIndex ? "text-gray-dark" : "text-gray-light"
                  }`}
                >
                  {res.category}
                </Text>
              </div>

              {idx === activeIndex ? (
                <IoMdArrowDropleft className="w-6 h-6 text-primary" />
              ) : (
                <IoMdArrowDropright className="w-6 h-6 text-gray-light" />
              )}
            </div>

            <div className={`md:hidden`}>
              {idx === activeIndex && popupDetails}
            </div>
          </>
        ))}
      </div>

      <div className="hidden md:block w-full">{popupDetails}</div>
    </div>
  );
};

export default Result;
