import { AnalyticsService } from "../services/admin/analyticsService";
import { AnalyticsHooks } from "../services/admin/analyticsHooks";
import { User } from "../@types";
import pool from "../db";
import { v4 as uuidv4 } from "uuid";

describe("Analytics Service", () => {
  const testUserId = uuidv4();

  beforeAll(async () => {
    // Create test events
    const events = [
      {
        event_type: "user_signup",
        user_id: testUserId,
        timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      },
      {
        event_type: "email_verified",
        user_id: testUserId,
        timestamp: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000), // 6 days ago
      },
      {
        event_type: "onboarding_complete",
        user_id: testUserId,
        timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      },
      {
        event_type: "property_generation",
        user_id: testUserId,
        timestamp: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000), // 4 days ago
      },
      {
        event_type: "property_generation",
        user_id: testUserId,
        timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      },
      {
        event_type: "user_activity",
        user_id: testUserId,
        timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      },
    ];

    for (const event of events) {
      await pool.query(
        `INSERT INTO analytics_events (event_type, user_id, timestamp)
         VALUES ($1, $2, $3)`,
        [event.event_type, event.user_id, event.timestamp],
      );
    }
  });

  afterAll(async () => {
    // Clean up test events
    await pool.query("DELETE FROM analytics_events WHERE user_id = $1", [
      testUserId,
    ]);
    await pool.end();
  });

  describe("Analytics Metrics", () => {
    it("should correctly calculate active users metrics", async () => {
      const metrics = await AnalyticsService.getMopsusAnalytics("7d");

      expect(metrics.activeUsers).toBeDefined();
      expect(typeof metrics.activeUsers.count).toBe("number");
      expect(typeof metrics.activeUsers.growth).toBe("number");
    });

    it("should correctly calculate new users metrics", async () => {
      const metrics = await AnalyticsService.getMopsusAnalytics("7d");

      expect(metrics.newUsers).toBeDefined();
      expect(typeof metrics.newUsers.count).toBe("number");
      expect(typeof metrics.newUsers.growth).toBe("number");
    });

    it("should correctly calculate property generation metrics", async () => {
      const metrics = await AnalyticsService.getMopsusAnalytics("7d");

      expect(metrics.propertyGenerations).toBeDefined();
      expect(typeof metrics.propertyGenerations.count).toBe("number");
      expect(typeof metrics.propertyGenerations.growth).toBe("number");
    });

    it("should correctly calculate onboarding funnel metrics", async () => {
      const metrics = await AnalyticsService.getMopsusAnalytics("7d");

      expect(metrics.onboardingFunnel).toBeDefined();
      expect(metrics.onboardingFunnel.signups).toBeDefined();
      expect(metrics.onboardingFunnel.emailVerifications).toBeDefined();
      expect(metrics.onboardingFunnel.onboardingCompletes).toBeDefined();
      expect(metrics.onboardingFunnel.firstApiCalls).toBeDefined();
    });
  });

  describe("Analytics Hooks", () => {
    it("should log user signup events", async () => {
      const mockUser: User = {
        user_id: uuidv4(),
        first_name: "Test",
        last_name: "User",
        email: "<EMAIL>",
        password: "hashedpassword",
        role: "user",
        active: true,
        password_reset_token: "",
        password_reset_token_expiry: new Date(),
        api_key: "test-api-key",
        api_key_id: "test-api-key-id",
        stripe_customer_id: "test-customer-id",
        current_usage_plan: "free",
        receive_email: true,
        created_at: new Date(),
        updated_at: new Date(),
        plan_changed_at: new Date(),
        usage_tracked_at: new Date(),
        usage_tracked_daily: new Date(),
        metered_billing: false,
        transaction_api_key: null,
        transaction_usage_plan: null,
        transaction_api_key_id: null,
        company: "Test Company",
        otp: "123456",
        otp_expiration: Date.now() + 600000,
        two_factor_enabled: false,
      };

      await AnalyticsHooks.onUserSignup(mockUser);

      const result = await pool.query(
        "SELECT * FROM analytics_events WHERE user_id = $1 AND event_type = $2",
        [mockUser.user_id, "user_signup"],
      );

      expect(result.rows.length).toBe(1);
      expect(result.rows[0].metadata).toHaveProperty("email", mockUser.email);
    });

    it("should log property generation events", async () => {
      const mockUserId = uuidv4();
      const mockPropertyDetails = { address: "123 Test St" };
      await AnalyticsHooks.onPropertyGeneration(
        mockUserId,
        mockPropertyDetails,
      );

      const result = await pool.query(
        "SELECT * FROM analytics_events WHERE user_id = $1 AND event_type = $2",
        [mockUserId, "property_generation"],
      );

      expect(result.rows.length).toBe(1);
      expect(result.rows[0].metadata).toHaveProperty("address", "123 Test St");
    });

    it("should log onboarding completion events", async () => {
      const mockUser: User = {
        user_id: uuidv4(),
        first_name: "Test",
        last_name: "User",
        email: "<EMAIL>",
        password: "hashedpassword",
        role: "user",
        active: true,
        password_reset_token: "",
        password_reset_token_expiry: new Date(),
        api_key: "test-api-key",
        api_key_id: "test-api-key-id",
        stripe_customer_id: "test-customer-id",
        current_usage_plan: "free",
        receive_email: true,
        created_at: new Date(),
        updated_at: new Date(),
        plan_changed_at: new Date(),
        usage_tracked_at: new Date(),
        usage_tracked_daily: new Date(),
        metered_billing: false,
        transaction_api_key: null,
        transaction_usage_plan: null,
        transaction_api_key_id: null,
        company: "Test Company",
        otp: "123456",
        otp_expiration: Date.now() + 600000,
        two_factor_enabled: false,
      };

      await AnalyticsHooks.onOnboardingComplete(mockUser);

      const result = await pool.query(
        "SELECT * FROM analytics_events WHERE user_id = $1 AND event_type = $2",
        [mockUser.user_id, "onboarding_complete"],
      );

      expect(result.rows.length).toBe(1);
    });
  });
});
