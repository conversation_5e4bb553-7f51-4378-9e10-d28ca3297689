import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "express";
import { body, validationResult } from "express-validator";
import AppError from "../utils/appError";

// Helper to check for at least one field in an update
const hasAtLeastOneField = (value: any, { req }: any) => {
  const fields = ["name", "description", "quota", "throttle"];
  return fields.some((field) => field in req.body);
};

export const validateUsagePlanCreate: RequestHandler[] = [
  body("name").notEmpty().withMessage("Name is required"),
  body("description").optional(),
  body("quota").optional().isObject().withMessage("Quota must be an object"),
  body("quota.limit")
    .if(body("quota").exists())
    .notEmpty()
    .isNumeric()
    .withMessage("Quota limit must be a number"),
  body("quota.period")
    .if(body("quota").exists())
    .isIn(["DAY", "WEEK", "MONTH"])
    .withMessage("Invalid quota period"),
  body("throttle")
    .optional()
    .isObject()
    .withMessage("Throttle must be an object"),
  body("throttle.burstLimit")
    .if(body("throttle").exists())
    .notEmpty()
    .isNumeric()
    .withMessage("Burst limit must be a number"),
  body("throttle.rateLimit")
    .if(body("throttle").exists())
    .notEmpty()
    .isNumeric()
    .withMessage("Rate limit must be a number"),

  // Validation result handler
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError(errors.array()[0].msg, 400));
    }
    next();
  },
];

export const validateUsagePlanUpdate: RequestHandler[] = [
  body()
    .custom(hasAtLeastOneField)
    .withMessage("At least one field must be provided"),
  body("name").optional().notEmpty().withMessage("Name cannot be empty"),
  body("description").optional(),
  body("quota").optional().isObject().withMessage("Quota must be an object"),
  body("quota.limit")
    .if(body("quota").exists())
    .notEmpty()
    .isNumeric()
    .withMessage("Quota limit must be a number"),
  body("quota.period")
    .if(body("quota").exists())
    .isIn(["DAY", "WEEK", "MONTH"])
    .withMessage("Invalid quota period"),
  body("throttle")
    .optional()
    .isObject()
    .withMessage("Throttle must be an object"),
  body("throttle.burstLimit")
    .if(body("throttle").exists())
    .notEmpty()
    .isNumeric()
    .withMessage("Burst limit must be a number"),
  body("throttle.rateLimit")
    .if(body("throttle").exists())
    .notEmpty()
    .isNumeric()
    .withMessage("Rate limit must be a number"),

  // Validation result handler
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError(errors.array()[0].msg, 400));
    }
    next();
  },
];
