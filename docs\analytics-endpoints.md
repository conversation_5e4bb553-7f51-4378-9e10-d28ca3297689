# Analytics Endpoints Documentation

## Overview

These endpoints allow frontend applications to track various user events and interactions in the platform.

## Base URL

```
http://localhost:4000/api/v1
```

## Endpoints

### Get All API Usage Analytics

```http
GET /analytics/api-usage/all
```

Retrieves total API usage analytics across all users within a specified date range, with daily time series data.

#### Query Parameters

| Parameter  | Type     | Required | Description                           |
|------------|----------|----------|---------------------------------------|
| startDate  | string   | Yes      | Start date in YYYY-MM-DD format      |
| endDate    | string   | Yes      | End date in YYYY-MM-DD format        |

#### Response Format

```json
{
  "status": "success",
  "data": [
    { "beginTimeSeconds": number, "endTimeSeconds": number, "count": number }
  ]
}
```

### Log Analytics Event

```http
POST /analytics/event
```

Logs a single analytics event.

#### Request Body

```typescript
{
  event_type: string;  // The type of event to log
  metadata?: object;   // Optional additional data about the event
}
```

#### Authentication

Requires an authenticated user session.

#### Event Types and Categories

##### Authentication Events

| Event Type             | Category       | Description                  |
| ---------------------- | -------------- | ---------------------------- |
| `Signup`               | Authentication | Regular email signup         |
| `Signup-with-google`   | Authentication | Google OAuth signup          |
| `Signup-with-Linkedin` | Authentication | LinkedIn OAuth signup        |
| `Verified account`     | Authentication | Email verification completed |
| `Signin`               | Authentication | User login                   |

##### Onboarding Events

| Event Type                | Category   | Description                     |
| ------------------------- | ---------- | ------------------------------- |
| `OnboardingComplete`      | Onboarding | User completed onboarding       |
| `OnboardingQuestionnaire` | Onboarding | Completed welcome questionnaire |
| `FindApiKey`              | Onboarding | User found their API key        |
| `ReadDocs`                | Onboarding | User accessed documentation     |
| `BookDemo`                | Onboarding | User booked a demo              |

##### API Events

| Event Type          | Category | Description             |
| ------------------- | -------- | ----------------------- |
| `FirstApiCall`      | API      | User's first API call   |
| `ApiCall`           | API      | Regular API call        |
| `PropertyGenerated` | Property | Property data generated |

### Example Usage

```typescript
// Using the api utility
import api from "../utils/api";

// Log a first API call event
await api.post("/analytics/event", {
  event_type: "first_api_call",
});

// Log onboarding step completion
await api.post("/analytics/event", {
  event_type: "OnboardingQuestionnaire",
});

// Log property generation with metadata
await api.post("/analytics/event", {
  event_type: "PropertyGenerated",
  metadata: {
    propertyType: "residential",
    address: "123 Main St",
  },
});
```

## Frontend Implementation

The frontend typically logs analytics events in the following scenarios:

1. **Onboarding Steps**

   - Email verification
   - Welcome questionnaire completion
   - Finding API key
   - First API call
   - Reading documentation
   - Booking demo

2. **User Authentication**

   - Regular signup
   - Social signup (Google/LinkedIn)
   - Email verification
   - Sign in

3. **API Usage**
   - First API call
   - Regular API calls
   - Property generation

### Analytics in Get Started Flow

The get-started page automatically logs events as users complete onboarding steps:

```typescript
// Example from get-started/index.tsx
const handleStep = async (stepId: string) => {
  switch (stepId) {
    case "onboarding-qs":
      await api.post("/analytics/event", {
        event_type: "OnboardingQuestionnaire",
      });
      break;
    case "find-api-key":
      await api.post("/analytics/event", {
        event_type: "FindApiKey",
      });
      break;
    // ... other steps
  }
};
```

## Notes

1. Events are stored in the `analytics_logger` table with the following structure:

   - id (auto-generated)
   - uuid (auto-generated)
   - event
   - category
   - created_at (auto-generated)

2. The analytics system uses these events to generate:

   - Active user metrics
   - New user metrics
   - Property generation stats
   - Onboarding funnel analytics

3. All timestamps are automatically handled by the backend.
