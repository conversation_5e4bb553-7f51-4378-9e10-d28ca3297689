import React, { useState } from "react";
import { FormProvider } from "../../components/FormContext";
import AVMForm from "./AVMForm";
import AVMContainer from "../components/AVMContainer";

const Index = () => {
  const [page, setPage] = useState(1);

  return (
    <FormProvider>
      <AVMContainer page={page}>
        <AVMForm page={page} setPage={setPage} />
      </AVMContainer>
    </FormProvider>
  );
};

export default Index;
