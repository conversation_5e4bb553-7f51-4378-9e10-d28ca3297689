import React, { useState } from "react";
import WWWContainer from "../components/WWWContainer";
import WWWForm from "./WWWForm";
import { FormProvider } from "../../components/FormContext";

const Index = () => {
  const [page, setPage] = useState(1);
  return (
    <FormProvider>
      <WWWContainer page={page}>
        <WWWForm page={page} setPage={setPage} />
      </WWWContainer>
    </FormProvider>
  );
};

export default Index;
