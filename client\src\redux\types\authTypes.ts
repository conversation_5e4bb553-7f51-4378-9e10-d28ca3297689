import { User } from "../../@types";
import {
  SignInPayload,
  SignUpPayload,
  TwoFARequiredResponse,
} from "../actions/authActions";

// Define the auth action types
export type AuthDispatch = {
  // Sign up related
  signUp: (formData: { email: string; password: string }) => Promise<string>;

  // Sign in related
  signIn: (params: {
    formData: { email: string; password: string };
    dispatch: AuthDispatch;
  }) => Promise<SignInPayload | TwoFARequiredResponse>;

  // User management
  loadUser: (dispatch: AuthDispatch) => Promise<User>;
  logout: () => Promise<void>;

  // Account verification
  verifyAccount: (userId: string) => Promise<boolean>;
  verifyEmail: (params: {
    email: string;
    otp: string;
  }) => Promise<{ token: string; user: User }>;
  resendCode: (email: string) => Promise<{ data: { message: string } }>;
  allowSignupConfirmation: (userId: string) => Promise<boolean>;

  // 2FA related
  verify2FA: (params: {
    userId: string;
    code: string;
    type?: "email" | "authenticator";
  }) => Promise<SignInPayload>;
  request2FACode: (userId: string) => Promise<void>;
};
