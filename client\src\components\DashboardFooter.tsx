import styled from 'styled-components';
import { Colors, device } from '../styles/Theme';

import altumai from '../assets/images/altumai-svg.svg';

const ColumnFlex = styled.div`
	display: flex;
	flex-direction: column;
	background-color: rgb(250, 250, 250);
	gap: 5px;
	align-items: center;
	justify-content: center;
	text-align: center;
	font-size: 16px !important;
	color: ${Colors.main.darkgrey};

	@media ${device.tablet} {
		flex-direction: column;
		justify-content: center;
	}
`;

function DashboardFooter() {
  return (
    <ColumnFlex>
      <div
        style={{
				  display: 'flex',
				  alignItems: 'center',
				  gap: '20px',
				  justifyContent: 'center',
        }}
      >
        <img src={altumai} width="10%" alt="altum" />
        <p>Copyright © Altum AI B.V.</p>
      </div>

      <p> BTW NL858932490B01</p>
      <p>KvK 71992375</p>
    </ColumnFlex>
  );
}

export default DashboardFooter;
