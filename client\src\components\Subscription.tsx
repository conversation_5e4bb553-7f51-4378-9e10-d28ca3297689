import styled from 'styled-components';
import { useAppSelector } from '../redux/hooks';
import { TextStyles, Colors, device } from '../styles/Theme';
import timeStampConverter from '../helpers/timeStampConverter';

const Container = styled.div`
	display: flex;
	flex-direction: column;
`;
const Title = styled.div`
	${TextStyles.Bundler(TextStyles.Label.Small)};
	margin-bottom: 20px;
	color: ${Colors.main.green};
	margin-top: 20px;

	@media ${device.tablet} {
		margin-top: 50px;
		margin-left: 2rem;
	}
	@media ${device.mobileL} {
		margin-top: 20px;
		margin-left: 0rem;
	}
`;

const TableContainer = styled.div`
	table {
		border-collapse: collapse;
		background-color: white;
		text-align: left;
		overflow: hidden;
		border-collapse: separate;
		border-spacing: 0 2em;
		width: 100%;

		thead {
		}

		th {
			padding: 1rem 2rem;
			text-transform: uppercase;
			letter-spacing: 0.1rem;
			font-size: 0.7rem;
			font-weight: 900;
		}

		td {
			padding: 1rem 2rem;
		}

		a {
			color: ${Colors.main.white};
			background-color: ${Colors.main.green};
			padding: 10px;
			border-radius: 3px;
			cursor: pointer;
		}

		tbody {
			tr {
				box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
				padding: 1rem;
				line-height: 40px;
			}
		}
	}
`;

function Subscriptions() {
  const { invoices } = useAppSelector((state) => state.portal);
  return (
    <Container>
      <TableContainer>
        <Title>Factuur</Title>

        <table>
          <thead>
            <tr>
              <th>Hoeveelheid</th>
              <th>Status</th>
              <th>Betaaldatum</th>
              <th />
            </tr>
          </thead>

          <tbody>
            {invoices?.data?.map((inv) => (
              <tr key={inv.id}>
                <td>
                &#8364;
{inv.amount_paid / 100}
              </td>
                <td>
                <div>
                {inv.status === 'paid'
										  ? 'Voldaan'
										  : inv.status?.toUpperCase()}
              </div>
              </td>
                <td>{timeStampConverter(inv.period_end)}</td>
                <td>
                <a href={inv.invoice_pdf!}>Download</a>
              </td>
              </tr>
            ))}
          </tbody>
        </table>
      </TableContainer>
    </Container>
  );
}

export default Subscriptions;
