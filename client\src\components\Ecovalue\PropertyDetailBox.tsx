import React from 'react';

interface Detail {
  key: string;
  value?: string | number | null;
  unit?: string;
}

interface PropertyDetailBoxProps {
  title: string;
  details: Detail[];
}

const PropertyDetailBox: React.FC<PropertyDetailBoxProps> = ({
  title,
  details,
}) => (
  <div className="result-item">
    <h3 className="result-title">{title}</h3>
    <div className="item-box">
      {details.map(
				  (detail, index) => detail.key && (
							<div className="item" key={index}>
  <p id="key">
    {detail.key}
    :
    {' '}
  </p>
  <p id="value">
    {detail.value
									  ? `${detail.value} ${detail.unit ? detail.unit : ''}`
									  : '-'}
  </p>
							</div>
				    ),
      )}
    </div>
  </div>
);

export default PropertyDetailBox;
