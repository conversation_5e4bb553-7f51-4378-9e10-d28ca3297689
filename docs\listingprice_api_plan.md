# ListingPrice API Enhancement Plan

## Overview
This document outlines the enhancements for the Vraagprijs (ListingPrice) API based on QA feedback.

## QA Feedback Summary

### Form Adjustments
1. **Title Update:**
   - Change the API title to reflect the correct application name as per QA feedback.

2. **Field Label Changes:**
   - **Energielabel:** Change label from "Energielabel" to **"Huidig energielabel"**.
   - **<PERSON><PERSON><PERSON> oppervlak (m2):** Change label to **"Perceeloppervlakte (m2)"** and add a default placeholder value of **150** (displayed in a gray color).
   - **Binnen oppervlakte (m2):** Change label to **"Woonoppervlakte (m2)"** and add a default placeholder value of **100** (displayed in a gray color).

3. **Additional Field:**
   - Add a new field **"Waarderingsdatum"** which uses a date picker component.  
     - This field should default to today's date.

> Note: There is interest in using the date picker widget (as seen in AVM and AVM+ forms) for the Waarderingsdatum. Consider adding similar date picker functionalities to other APIs if applicable.

### Result Page Adjustments
1. **Spacing Issues:**
   - Address layout and spacing issues on the result page to ensure a clean, well-aligned UI.

2. **Confidence Interval:**
   - Update the confidence interval display to match the AVM pages, e.g.,  
     **"90% interval € X tot € Y"**  
     with proper EURO formatting using helper functions (e.g., `convertToEuFormat`).

## Proposed Workflow Diagram

```mermaid
flowchart TD
    A[Review QA Feedback for ListingPrice API]
    B[Update ListingPrice API Endpoint Title]
    C[Modify Form Fields]
    C1[Change Energielabel → "Huidig energielabel"]
    C2[Change Buiten oppervlak (m2) → "Perceeloppervlakte (m2)" with placeholder 150]
    C3[Change Binnen oppervlakte (m2) → "Woonoppervlakte (m2)" with placeholder 100]
    C4[Add Waarderingsdatum field with date picker (default today)]
    D[Update Result Page]
    D1[Fix spacing issues for a clean layout]
    D2[Update confidence interval to display "90% interval € X tot € Y"]
    
    A --> B
    A --> C
    C --> C1
    C --> C2
    C --> C3
    C --> C4
    A --> D
    D --> D1
    D --> D2
```

## Next Steps
1. **Form Implementation:**
   - Update the form configuration files in `client/src/pages/Api/usage-pages/listingprice/form/` to reflect the above changes.
   - Adjust the API endpoint configuration (likely in a data or config file) with the new title.
   - Integrate a date picker component for the new Waarderingsdatum field, ensuring it defaults to today’s date.

2. **Result Page Implementation:**
   - Update the result components in `client/src/pages/Api/usage-pages/listingprice/result/` to fix spacing.
   - Use helper functions to format numbers as EURO currency and update the confidence interval display accordingly.

3. **Testing:**
   - Test the changes to ensure that default values, labels, and UI adjustments meet the QA feedback.
   - Ensure consistency with the AVM and AVM+ APIs regarding date picker functionalities and confidence interval formatting.

---

End of Plan.