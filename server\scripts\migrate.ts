import { Pool } from "pg";
import { readFileSync } from "fs";
import path from "path";
import logger from "../utils/logger";
import dotenv from "dotenv";
import readline from "readline";

dotenv.config();

const getConnectionString = () => {
  const env = process.env.NODE_ENV || "development";

  switch (env) {
    case "production":
      return process.env.POSTGRES_URI; // Production database
    case "test":
      return process.env.POSTGRES_TEST_URI; // Test database
    default:
      return process.env.POSTGRES_DEV_URI; // Development database
  }
};

const confirmProductionMigration = async (): Promise<boolean> => {
  if (process.env.NODE_ENV !== "production") return true;

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  return new Promise((resolve) => {
    rl.question(
      "\x1b[31m⚠️  WARNING: You are about to run migrations on PRODUCTION database!\x1b[0m\nAre you sure you want to continue? (yes/no): ",
      (answer) => {
        rl.close();
        resolve(answer.toLowerCase() === "yes");
      },
    );
  });
};

async function migrate() {
  const connectionString = getConnectionString();

  if (!connectionString) {
    throw new Error(
      `Database connection string not found for environment: ${process.env.NODE_ENV}`,
    );
  }

  logger.info(`Environment: ${process.env.NODE_ENV || "development"}`);

  // Confirm before running production migrations
  const shouldProceed = await confirmProductionMigration();
  if (!shouldProceed) {
    logger.info("Migration cancelled by user");
    process.exit(0);
  }

  const pool = new Pool({ connectionString });

  try {
    logger.info("Starting database migration");
    logger.info(`Using database: ${connectionString.split("@")[1]}`); // Safe logging of DB host

    // Take database backup in production
    if (process.env.NODE_ENV === "production") {
      logger.info("Creating database backup before migration...");
      // You would add your backup logic here
      // For example: await createDatabaseBackup();
    }

    // Read and execute the initial 2FA fields migration
    const twoFAMigration = readFileSync(
      path.join(__dirname, "../utils/migrations/add_2fa_fields.sql"),
      "utf8",
    );

    // Read and execute the 2FA security fields migration
    const twoFASecurityMigration = readFileSync(
      path.join(__dirname, "../utils/migrations/add_2fa_security_fields.sql"),
      "utf8",
    );

    logger.info("Executing 2FA migrations...");
    await pool.query(twoFAMigration);
    await pool.query(twoFASecurityMigration);
    logger.info("2FA base fields migration completed successfully");
    logger.info("2FA security fields migration completed successfully");

    logger.info("All migrations completed successfully");
  } catch (error) {
    logger.error("Error during migration:", error);

    if (process.env.NODE_ENV === "production") {
      logger.error(
        "⚠️ Production migration failed! Please restore from backup if necessary.",
      );
    }

    throw error;
  } finally {
    await pool.end();
  }
}

// Export for use in other scripts if needed
export const runMigration = migrate;

// Run if called directly
if (require.main === module) {
  migrate()
    .then(() => {
      logger.info("Migration script completed");
      process.exit(0);
    })
    .catch((error) => {
      logger.error("Migration failed:", error);
      process.exit(1);
    });
}
