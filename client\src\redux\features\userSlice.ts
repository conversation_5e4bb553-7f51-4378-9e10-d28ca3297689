import { createSlice } from '@reduxjs/toolkit';
import { changePassword, deleteAccount } from '../actions/userActions';

interface UserInitState {
  loading: boolean;
  success: boolean;
  errors: unknown[];
}
const initialState: UserInitState = {
  loading: false,
  success: false,
  errors: [],
};
const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    resetSuccess: (state) => {
      state.success = false;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(changePassword.pending, (state) => {
        state.loading = true;
        state.success = false;
        state.errors = [];
      })
      .addCase(changePassword.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(changePassword.rejected, (state, action) => {
        state.loading = false;
        state.errors.push(action.payload);
      })
      .addCase(deleteAccount.pending, (state) => {
        state.loading = true;
        state.success = false;
        state.errors = [];
      })
      .addCase(deleteAccount.fulfilled, (state) => {
        state.loading = false;
        state.success = true;
      })
      .addCase(deleteAccount.rejected, (state, action) => {
        state.loading = false;
        state.errors.push(action.payload);
      });
  },
});

export default userSlice.reducer;
