import React from "react";
import { Redirect } from "react-router";
import { useAppSelector } from "../../../redux/hooks";
import Loading from "../../Loading";
import RebuildForm from "./RebuildForm";
import { FormProvider } from "../../../pages/Api/usage-pages/components/FormContext";
const Rebuild = () => {
  const { loading, result } = useAppSelector((state) => state.rebuild);

  if (loading) {
    return <Loading />;
  }
  return (
    <FormProvider>
      {Object.keys(result).length > 0 ? (
        <Redirect
          to={{
            pathname: "/rebuild-result",
          }}
        />
      ) : (
        <RebuildForm />
      )}
    </FormProvider>
  );
};

export default Rebuild;
