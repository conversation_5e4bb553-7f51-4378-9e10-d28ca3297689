import passport from "passport";
import GoogleStrategy from "passport-google-oauth20";
import AWS from "aws-sdk";
import { nanoid } from "nanoid";
import pool from "../db";
import {
  createNewUser,
  addUserToEmailTable,
  addUserToNotificationTable,
} from "../controllers/authController";
import logger from "./logger";
import { createAnalyticsTag } from "./sendAnalytics";
import { updateContact } from "./sendgridContactHandler";
import { createStripeCustomer } from "../controllers/stripeController";
import { AuthUser, User } from "../@types";
import { createTokenResponse } from "./authUtil";
import { send2FACode } from "./twoFactorAuth";

passport.use(
  new GoogleStrategy.Strategy(
    {
      clientID: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      callbackURL: "/auth/google/callback",
      scope: ["email", "profile"],
    },
    async (_req, _accessToken, _refreshToken, profile, cb) => {
      try {
        logger.info("Sign up via google");

        const { name, emails } = profile;
        if (emails == null || name == null) return;
        const email = emails[0].value;
        const given_name = name.givenName;
        const family_name = name.familyName;

        const user = await pool.query<User>(
          `SELECT * FROM users WHERE email=$1`,
          [email],
        );

        if (user.rowCount === 0) {
          const stripeCustomerId = await createStripeCustomer(email);

          const verificationExpiry =
            (Date.now() + 24 * 60 * 60 * 1000) / 1000.0;
          const apiKeyName = `Mopsus-${email}-${nanoid(10)}`;

          // AUTO-CREATE API KEY FOR USER8
          logger.info("AUTO-CREATE API KEY FOR USER");
          let apiKey = "";
          let apiKeyId = "";
          const apiGateway = new AWS.APIGateway({
            accessKeyId: process.env.AWS_ACCESS_KEY,
            secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
            endpoint: "https://apigateway.eu-west-1.amazonaws.com",
          });

          // // Create AWS API key for usage plan
          logger.info("creating AWS API Key for usage plan");
          apiGateway.createApiKey(
            {
              name: apiKeyName,
              enabled: true,
            },
            async (err, data) => {
              if (err) console.log(err, err.stack);
              if (!data.value || !data.id) return;
              apiKey = data.value;
              apiKeyId = data.id;
              // LINK TO CALCHAS USAGE PLAN
              apiGateway.createUsagePlanKey(
                {
                  keyId: data.id,
                  keyType: "API_KEY",
                  usagePlanId: process.env.AWS_MOPSUS_USAGE_PLAN_FREE!,
                },
                async (err, data) => {
                  if (err) {
                    logger.error(err);
                    console.log(err, err.stack);
                  }

                  // If no errors, create new user
                  logger.info("No error found creating usage plan key");
                  const newUser = await createNewUser(
                    given_name || "",
                    family_name || "",
                    email,
                    "",
                    "",
                    "",
                    apiKey,
                    apiKeyId,
                    stripeCustomerId,
                    verificationExpiry,
                  );
                  await pool.query(
                    "update users set active=true where email=$1",
                    [email],
                  );
                  // enter user into usage_emails table
                  await addUserToEmailTable(newUser);
                  await addUserToNotificationTable(newUser);

                  await updateContact(
                    newUser,
                    process.env.SENDGRID_LIST_NEWUSERS!,
                  );
                  await createAnalyticsTag({
                    event: "Signup-with-google",
                    category: "Authentication",
                  });
                  logger.info("User created successfully");
                  // New users don't need 2FA check as it's not enabled by default
                  cb(null, newUser as AuthUser);
                },
              );
            },
          );
        } else {
          const existingUser = user.rows[0];

          // Only allow full access if 2FA is not enabled
          cb(null, existingUser);
        }
      } catch (err) {
        console.log(err);
        cb(err as Error);
      }
    },
  ),
);

// Override passport's type definitions to use AuthUser
declare global {
  namespace Express {
    interface User extends AuthUser {}
  }
}

passport.serializeUser((user, done) => {
  done(null, user);
});

passport.deserializeUser((user: Express.User, done) => {
  done(null, user);
});
