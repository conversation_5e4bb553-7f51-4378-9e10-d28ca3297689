// Import packages
import express, { Request, Response, NextFunction } from "express";
import cors from "cors";
import cookieParser from "cookie-parser";
import helmet from "helmet";
import path from "path";
import cron from "node-cron";
import {
  timeout,
  additionalSecurityHeaders,
} from "./middlewares/securityMiddleware";
import passport from "passport";
import session from "express-session";
import crypto from "crypto";
import logger from "./utils/logger";

// Extend Express Response type to include locals
declare global {
  namespace Express {
    interface Response {
      locals: {
        nonce?: string;
        [key: string]: any;
      };
    }
  }
}
import {
  send422Email,
  send500Email,
  sendEmailIfPrepaidCreditIsDepleted,
  sendResetApiEmail,
  exhaustedFreeCredits,
  addPayPerUseUsage,
  addPayPerUseUsageTransaction,
  updatePlanChangedAt,
  send429Email,
  send403Email,
  sendAnalytics,
} from "./utils/cron";
import { webhookCheckout } from "./controllers/stripeController";
import twoFactorAuthRoutes from "./routes/twoFactorAuthRoutes";

// Import utils
import errorHandler from "./utils/errorHandler";
import customerPortalRoute from "./routes/customerPortal";
// Import user routes

import userRoutes from "./routes/userRoutes";

// Import AWS routes
import awsRoutes from "./routes/awsRoutes";

// import altumRoutes from "./routes/altumRoutes";
// app.use("/api/v1/altum", altumRoutes);

import mopsusRoutes from "./routes/mopsusRoutes";

import messagesRoutes from "./routes/messageRoutes";

import adminRoutes from "./routes/adminRoutes";
import userNotification from "./routes/userNotification";
import onboarding from "./routes/onboardingRoutes";
import { cookieFunction, handle2FACheck } from "./utils/authUtil";
import { User } from "./@types";
import { propertyRoutes } from "./routes/propertDescriptionRoutes";

const xss = require("xss-clean");
require("./utils/googleStrategy");
require("./utils/linkedinStrategy");
// Serve React static files
const app = express();

// Disable X-Powered-By header
app.disable("x-powered-by");
app.use((req, res, next) => {
  res.removeHeader("X-Powered-By");
  next();
});

app.use(express.static(path.join(__dirname, "build")));
app.use(express.static(path.join(__dirname, "public")));
app.set("view engine", "pug");

logger.info(`Current Environment - ${process.env.NODE_ENV}`);

// Security Headers - Enhanced configuration to address ZAP findings
// Generate nonce early in the pipeline
const generateNonce = (req: Request, res: Response, next: NextFunction) => {
  res.locals.nonce = crypto.randomBytes(16).toString("base64");
  next();
};

// Apply security middleware in correct order
app.use(generateNonce);

// Add request timeout (30 seconds)
app.use(timeout(30000));

// Apply additional security headers
app.use(additionalSecurityHeaders);

// Set CSP headers before any other middleware
app.use((req: Request, res: Response, next: NextFunction) => {
  const nonce = res.locals.nonce;

  // Set CSP header with all required directives including WebAssembly support
  res.setHeader(
    "Content-Security-Policy",
    `default-src 'self';` +
      `script-src 'self' 'unsafe-inline' 'wasm-unsafe-eval' 'unsafe-eval' https://js.stripe.com https://www.googletagmanager.com https://www.google-analytics.com;` +
      `style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;` +
      `img-src 'self' data: https://mopsus-test.altum.ai https://mopsus.altum.ai;` +
      `connect-src 'self' https://api.mopsus.altum.ai https://api.stripe.com https://www.google-analytics.com;` +
      `font-src 'self' https://fonts.gstatic.com data:;` +
      `object-src 'none';` +
      `media-src 'self';` +
      `frame-src 'self' https://js.stripe.com;` +
      `base-uri 'self';` +
      `form-action 'self';` +
      `manifest-src 'self';` +
      `worker-src 'self';` +
      `upgrade-insecure-requests`,
  );

  // Set frame-ancestors via header since it's not supported in meta tags
  res.setHeader("Content-Security-Policy", "frame-ancestors 'none'");

  next();
});

// HTTPS redirect for production and test environments
if (process.env.NODE_ENV === "production" || process.env.NODE_ENV === "test") {
  app.set("trust proxy", true);
  app.use((req, res, next) => {
    if (!req.secure) {
      res.redirect(["https://", req.get("Host"), req.baseUrl].join(""));
      return;
    }
    next();
  });
}

// Apply other security headers with Helmet
app.use(
  helmet({
    hidePoweredBy: true,
    frameguard: {
      action: "deny",
    },
    hsts: {
      maxAge: 31536000, // 1 year in seconds
      includeSubDomains: true,
      preload: true,
    },
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: false, // We're handling CSP separately
    referrerPolicy: { policy: "no-referrer" },
    noSniff: true,
    xssFilter: true,
    dnsPrefetchControl: { allow: false },
    permittedCrossDomainPolicies: { permittedPolicies: "none" },
    expectCt: {
      maxAge: 0,
      enforce: true,
    },
  }),
);

// Add CSP violation reporting endpoint
const handleCSPViolation = (req: Request, res: Response) => {
  if (req.body) {
    logger.warn("CSP Violation:", req.body);
  }
  res.status(204).end();
};

app.post("/csp-violation-report", handleCSPViolation);

// Enable CORS with secure configuration
app.use(
  cors({
    origin:
      process.env.NODE_ENV === "production"
        ? [
            "https://mopsus.altum.ai",
            "https://api.mopsus.altum.ai",
            "https://platform-admin.replit.app",
            "https://platform-admin-staging.replit.app", // Added staging domain
            "https://admin.altum.ai", // Added admin domain
          ]
        : [
            "http://localhost:3000",
            "https://platform-admin-staging.replit.app",
            "https://mopsus-test.altum.ai",
            "https://admin.altum.ai", // Added admin domain
          ],
    credentials: true,
    methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
    exposedHeaders: ["Content-Range", "X-Content-Range"],
    maxAge: 600, // Cache preflight requests for 10 minutes
  }),
);

// Cookie Parser with secure settings
app.use(cookieParser());

// Session configuration with secure settings
app.use(
  session({
    secret: process.env.SESSION_SECRET || "your-secret-key",
    resave: false,
    saveUninitialized: false,
    cookie: {
      secure: process.env.NODE_ENV === "production",
      httpOnly: true,
      sameSite: "strict",
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
    },
  }),
);

// Data sanitization against XSS
app.use(xss());

// Stripe webhook endpoint
// Body should not be in JSON, hence placed before express.json()
app.post(
  "/webhook-checkout",
  express.raw({ type: "application/json" }),
  webhookCheckout,
);

// Body Parser
app.use(express.json());

app.use(passport.authenticate("session"));
app.use(passport.initialize());
app.use(passport.session());

cron.schedule("0 2 * * *", () => {
  logger.info(
    "Running CRON task every 2AM to check if prepaid credit has been depleted",
  );
  sendEmailIfPrepaidCreditIsDepleted();
});
cron.schedule("0 3 1 * *", () => {
  logger.info(
    "Running CRON task at the start of month to send reset API usage email",
  );
  sendResetApiEmail();
});

cron.schedule("0 */1 * * *", () => {
  logger.info(
    "Running CRON task every hour to check free credits exhaustion from user",
  );
  exhaustedFreeCredits();
});

//
// cron.schedule("0 */1 * * *", function () {
// 	logger.info("Running CRON task every hour to check API usage inactivity");
// 	checkAPIUsageInactivity();
// });

cron.schedule("0 16 * * FRI", () => {
  logger.info("Running CRON task every friday at 4pm to send Analytics");
  sendAnalytics(7);
});
cron.schedule("0 0 * * *", () => {
  logger.info("Running CRON task everyday at 12AM to add PPU usage");
  addPayPerUseUsage();
});
cron.schedule("5 0 * * *", () => {
  logger.info("Running CRON task everyday at 12AM to add PPU usage");
  addPayPerUseUsageTransaction();
});

cron.schedule("0 0 1 * *", () => {
  logger.info(
    "Running CRON task at the start of month to update plan_changed_at value for free tier users",
  );
  updatePlanChangedAt();
});
cron.schedule("10 0 * * *", () => {
  logger.info("Running CRON task 12AM for 429 errors in the past 24 hrs");
  send429Email();
});
cron.schedule("15 0 * * *", () => {
  logger.info("Running CRON task at 12AM for 403 errors in the past 24 hrs");
  send403Email();
});
cron.schedule("20 0 * * *", () => {
  logger.info("Running CRON task at 12AM for 422 errors in the past 24 hrs");
  send422Email();
});
cron.schedule("25 0 * * *", () => {
  logger.info("Running CRON task at 12AM for 500 errors in the past 24 hrs");
  send500Email();
});
// updateCustomerNameOnStripe();
// cron.schedule("*/10 * * * *", function () {
// 	logger.info("Running CRON task at 10 minutes");
// 	getUnlistedEmails();
// });
// Render API homepage
app.get("/api", (_req, res) => {
  // Remove conflicting CSP header - use the global helmet CSP configuration instead
  res.render("api");
});

app.get(
  "/auth/google",
  passport.authenticate("google", { scope: ["email", "profile"] }),
);
app.get(
  "/auth/google/callback",
  passport.authenticate("google", {
    failureRedirect: "/signin",
  }),
  async (req, res) => {
    if (!req.user) {
      return res.redirect("/signin");
    }

    const user = req.user as User;
    const twoFactorCheck = await handle2FACheck(user);

    cookieFunction(user, res);

    if (!!twoFactorCheck) {
      // If 2FA is required, redirect with 2FA data
      const redirectParams = encodeURIComponent(
        JSON.stringify({
          requires2FA: true,
          userId: twoFactorCheck.userId,
          type: twoFactorCheck.type,
        }),
      );
      res.redirect(`/signin?user=${redirectParams}`);
    } else {
      res.redirect("/dashboard/startpagina");
    }
  },
);

app.get(
  "/auth/linkedin",
  passport.authenticate("linkedin", { state: "SOME STATE" }),
);

app.get(
  "/auth/linkedin/callback",
  passport.authenticate("linkedin", {
    successRedirect: "/",
    failureRedirect: "/login",
  }),
  async (req, res) => {
    if (!req.user) {
      return res.redirect("/login");
    }

    const user = req.user as User;
    const twoFactorCheck = await handle2FACheck(user);

    cookieFunction(user, res);

    if (twoFactorCheck) {
      // If 2FA is required, redirect with 2FA data
      const redirectParams = encodeURIComponent(
        JSON.stringify({
          requires2FA: true,
          userId: twoFactorCheck.userId,
          type: twoFactorCheck.type,
        }),
      );
      res.redirect(`/login?user=${redirectParams}`);
    } else {
      res.redirect("/dashboard/startpagina");
    }
  },
);
app.use("/api/v1/customer-portal", customerPortalRoute);
app.use("/api/v1/users", userRoutes);
app.use("/api/v1/aws", awsRoutes);
app.use("/api/v1/mopsus", mopsusRoutes);
app.use("/api/v1/messages", messagesRoutes);
app.use("/api/v1/user-notifications", userNotification);
app.use("/api/v1/onboarding", onboarding);
app.use("/api/v1/property-description", propertyRoutes);
app.use("/api/v1/admin", adminRoutes);
app.use("/api/v1/2fa", twoFactorAuthRoutes);

app.get("/*", (_req, res) => {
  // Remove conflicting CSP header - use the global helmet CSP configuration instead
  // This fixes the CSP wildcard directive and unsafe-inline/unsafe-eval issues
  res.sendFile(path.resolve(__dirname, "build", "index.html"));
});

// Global error handler => To catch errors
app.use(errorHandler);

export default app;
