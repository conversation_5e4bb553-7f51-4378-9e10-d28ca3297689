import React from "react";
import { Redirect } from "react-router";
import { useAppSelector } from "../../../redux/hooks";
import Loading from "../../Loading";
import EnergyClimateForm from "./EnergyClimateForm";
import { FormProvider } from "../../../pages/Api/usage-pages/components/FormContext";
const EnergyClimate = () => {
  const { loading, result } = useAppSelector((state) => state.energyClimate);

  if (loading) {
    return <Loading />;
  }

  return (
    <FormProvider>
      {Object.keys(result).length > 0 ? (
        <Redirect
          to={{
            pathname: "/energy-climate-result",
          }}
        />
      ) : (
        <EnergyClimateForm />
      )}
    </FormProvider>
  );
};

export default EnergyClimate;
