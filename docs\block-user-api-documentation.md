# Block User API Documentation

This documentation describes the endpoints for managing blocked user emails in the system.

## Authentication

All endpoints require admin authentication. Include your authentication token in the request header:

```
Authorization: Bearer <your-token>
```

## Endpoints

### Block Email

**POST** `/api/v1/admin/block-email`

Blocks an email address from accessing the system.

#### Request Body

```json
{
  "email": "string",
  "reason": "string" (optional)
}
```

#### Response

```json
{
  "status": "success",
  "message": "Email <EMAIL> has been blocked"
}
```

#### Error Responses

- `400 Bad Request`: Email is required
- `500 Internal Server Error`: Failed to block email

### Unblock Email

**POST** `/api/v1/admin/unblock-email`

Removes an email address from the blocked list.

#### Request Body

```json
{
  "email": "string"
}
```

#### Response

```json
{
  "status": "success",
  "message": "Email <EMAIL> has been unblocked"
}
```

#### Error Responses

- `404 Not Found`: Email is not blocked
- `500 Internal Server Error`: Failed to unblock email

### Get Blocked Emails

**GET** `/api/v1/admin/blocked-emails`

Retrieves a paginated list of blocked emails.

#### Query Parameters

- `page` (optional): Page number (default: 1)
- `limit` (optional): Number of items per page (default: 10)

#### Response

```json
{
  "status": "success",
  "data": {
    "items": [
      {
        "email": "string",
        "reason": "string",
        "created_at": "timestamp",
        "updated_at": "timestamp"
      }
    ],
    "totalItems": "number",
    "currentPage": "number",
    "totalPages": "number",
    "hasNextPage": "boolean",
    "hasPreviousPage": "boolean"
  }
}
```

#### Error Response

- `500 Internal Server Error`: Failed to fetch blocked emails

## Examples

### Block an Email

```bash
curl -X POST https://api.example.com/api/v1/admin/block-email \
  -H "Authorization: Bearer <your-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "reason": "Suspicious activity"
  }'
```

### Unblock an Email

```bash
curl -X POST https://api.example.com/api/v1/admin/unblock-email \
  -H "Authorization: Bearer <your-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>"
  }'
```

### Get Blocked Emails (with pagination)

```bash
curl https://api.example.com/api/v1/admin/blocked-emails?page=1&limit=10 \
  -H "Authorization: Bearer <your-token>"
```