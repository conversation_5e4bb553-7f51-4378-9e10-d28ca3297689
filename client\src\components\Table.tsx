import {
  Table as FlTable,
  TableBody,
  TableCell,
  TableHead,
  TableHeadCell,
  TableRow,
} from "flowbite-react";
import { FC } from "react";
import Text from "./Text";

type Props = {
  headers: string[];
  noDataText: string;
  tableItems?: { [key: string]: string | number | null | JSX.Element }[];
};

const NoTableData: FC<{ noDataText: string }> = ({ noDataText }) => {
  return (
    <div className="flex flex-col items-center justify-center h-full min-h-[300px] self-center gap-2">
      <svg
        width="65"
        height="64"
        viewBox="0 0 65 64"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g clip-path="url(#clip0_2902_1042)">
          <path
            d="M25.0253 26.667C24.4948 26.667 23.9861 26.8777 23.6111 27.2528C23.236 27.6279 23.0253 28.1366 23.0253 28.667C23.0253 29.1974 23.236 29.7061 23.6111 30.0812C23.9861 30.4563 24.4948 30.667 25.0253 30.667H42.3586C42.889 30.667 43.3977 30.4563 43.7728 30.0812C44.1479 29.7061 44.3586 29.1974 44.3586 28.667C44.3586 28.1366 44.1479 27.6279 43.7728 27.2528C43.3977 26.8777 42.889 26.667 42.3586 26.667H25.0253ZM18.6733 37.3337C18.1428 37.3337 17.6341 37.5444 17.2591 37.9194C16.884 38.2945 16.6733 38.8032 16.6733 39.3337C16.6733 39.8641 16.884 40.3728 17.2591 40.7479C17.6341 41.1229 18.1428 41.3337 18.6733 41.3337H36.0066C36.537 41.3337 37.0457 41.1229 37.4208 40.7479C37.7959 40.3728 38.0066 39.8641 38.0066 39.3337C38.0066 38.8032 37.7959 38.2945 37.4208 37.9194C37.0457 37.5444 36.537 37.3337 36.0066 37.3337H18.6733ZM14.4573 48.0003C13.9268 48.0003 13.4181 48.211 13.0431 48.5861C12.668 48.9612 12.4573 49.4699 12.4573 50.0003C12.4573 50.5308 12.668 51.0395 13.0431 51.4145C13.4181 51.7896 13.9268 52.0003 14.4573 52.0003H31.7906C32.321 52.0003 32.8298 51.7896 33.2048 51.4145C33.5799 51.0395 33.7906 50.5308 33.7906 50.0003C33.7906 49.4699 33.5799 48.9612 33.2048 48.5861C32.8298 48.211 32.321 48.0003 31.7906 48.0003H14.4573Z"
            fill="#BDBDBD"
          />
          <path
            d="M11.5005 9.06652e-05H53.5005C54.9464 -0.00579756 56.3792 0.275177 57.7158 0.826748C59.0523 1.37832 60.2662 2.18953 61.2872 3.21342C62.3111 4.23438 63.1223 5.44828 63.6739 6.78487C64.2254 8.12146 64.5064 9.55418 64.5005 11.0001C64.5005 14.6908 63.2312 18.4508 61.4925 22.0934C59.7539 25.7334 57.4525 29.4454 55.2205 33.0374L55.1992 33.0721C52.9352 36.7201 50.7485 40.2481 49.1192 43.6588C47.4819 47.0801 46.5005 50.1921 46.5005 53.0001C46.5006 54.2095 46.814 55.3982 47.4102 56.4504C48.0063 57.5026 48.8649 58.3825 49.9023 59.0041C50.9397 59.6258 52.1204 59.9681 53.3294 59.9976C54.5384 60.0272 55.7345 59.743 56.801 59.1728C57.8675 58.6026 58.7681 57.7658 59.415 56.7439C60.0619 55.7221 60.433 54.5501 60.4921 53.3422C60.5513 52.1343 60.2965 50.9316 59.7526 49.8515C59.2087 48.7713 58.3942 47.8505 57.3885 47.1788C57.1702 47.0327 56.9827 46.8451 56.8369 46.6267C56.6911 46.4082 56.5897 46.1631 56.5385 45.9055C56.4874 45.6478 56.4875 45.3826 56.5389 45.125C56.5902 44.8674 56.6918 44.6224 56.8379 44.4041C56.9839 44.1857 57.1715 43.9983 57.39 43.8525C57.6084 43.7066 57.8535 43.6052 58.1112 43.5541C58.3688 43.503 58.634 43.5031 58.8916 43.5544C59.1492 43.6058 59.3942 43.7074 59.6125 43.8534C61.5732 45.1655 63.0601 47.073 63.8541 49.2946C64.648 51.5161 64.7069 53.9341 64.022 56.1916C63.3371 58.4492 61.9448 60.4268 60.0503 61.8328C58.1559 63.2387 55.8597 63.9985 53.5005 64.0001H12.5005C9.58315 64.0001 6.78525 62.8412 4.72235 60.7783C2.65945 58.7154 1.50053 55.9175 1.50053 53.0001C1.50053 47.0428 4.85519 40.5841 8.40719 34.5948C9.33519 33.0321 10.2792 31.4961 11.2072 29.9868C13.3405 26.5094 15.3859 23.1788 16.9245 20.0188H9.54053C7.34586 20.0188 4.51119 19.5041 2.69519 17.2588C1.2535 15.4938 0.476829 13.2789 0.500527 11.0001C0.500527 8.08271 1.65945 5.28482 3.72235 3.22192C5.78525 1.15902 8.58315 9.06652e-05 11.5005 9.06652e-05ZM42.5005 53.0001C42.5005 49.3201 43.7699 45.5708 45.5085 41.9334C47.2392 38.3121 49.5325 34.6188 51.7565 31.0374L51.8019 30.9628C54.0659 27.3174 56.2525 23.7841 57.8819 20.3708C59.5192 16.9441 60.5005 13.8188 60.5005 11.0001C60.5041 10.0799 60.3254 9.16801 59.9749 8.31714C59.6243 7.46627 59.1089 6.6932 58.4581 6.04248C57.8074 5.39177 57.0344 4.87629 56.1835 4.52575C55.3326 4.17522 54.4208 3.99656 53.5005 4.00009C51.644 4.00009 49.8635 4.73759 48.5508 6.05034C47.238 7.3631 46.5005 9.14357 46.5005 11.0001C46.5005 13.3068 47.6232 15.0241 49.6125 16.3574C49.9686 16.5961 50.2386 16.9429 50.3827 17.3467C50.5267 17.7504 50.5373 18.1898 50.4127 18.6C50.2881 19.0102 50.035 19.3695 49.6907 19.6249C49.3465 19.8804 48.9292 20.0184 48.5005 20.0188H21.3325C19.5992 23.9734 17.0019 28.2028 14.4605 32.3361C13.5699 33.7868 12.6845 35.2241 11.8472 36.6348C8.23386 42.7334 5.50053 48.2908 5.50053 53.0001C5.50053 54.8566 6.23802 56.6371 7.55078 57.9498C8.86353 59.2626 10.644 60.0001 12.5005 60.0001H45.0152C43.3847 58.0323 42.4949 55.5556 42.5005 53.0001ZM4.50053 11.0001C4.47386 12.3628 4.93519 13.6881 5.79919 14.7414C6.47653 15.5761 7.77253 16.0188 9.53786 16.0188H43.8312C42.9414 14.4972 42.4815 12.7626 42.5005 11.0001C42.4949 8.44458 43.3847 5.96783 45.0152 4.00009H11.5005C9.64401 4.00009 7.86353 4.73759 6.55078 6.05034C5.23802 7.3631 4.50053 9.14357 4.50053 11.0001Z"
            fill="#BDBDBD"
          />
        </g>
        <defs>
          <clipPath id="clip0_2902_1042">
            <rect
              width="64"
              height="64"
              fill="white"
              transform="translate(0.5)"
            />
          </clipPath>
        </defs>
      </svg>

      <Text className="text-center text-gray-200 w-[300px]">{noDataText}</Text>
    </div>
  );
};

const Table: FC<Props> = ({ headers, tableItems, noDataText }) => {
  return (
    <div className="overflow-x-auto">
      <FlTable>
        {tableItems && tableItems.length > 0 ? (
          <>
            <TableHead>
              {headers.map((header, index) => (
                <TableHeadCell key={index}>{header}</TableHeadCell>
              ))}
            </TableHead>
            <TableBody className="divide-y">
              {tableItems.map((item, index) => (
                <TableRow
                  key={index}
                  className="bg-white dark:border-gray-700 dark:bg-gray-800"
                >
                  {Object.entries(item).map(([key, value], idx) => (
                    <TableCell
                      key={idx}
                      className="whitespace-nowrap font-medium text-gray-900"
                    >
                      {value}
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </>
        ) : (
          <NoTableData noDataText={noDataText} />
        )}
      </FlTable>
    </div>
  );
};

export default Table;
