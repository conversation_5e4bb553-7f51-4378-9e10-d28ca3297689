import React, { useState } from "react";
import { useAppSelector } from "../../../redux/hooks";
import convertToEuFormat from "../../../helpers/convertToEuFormat";
import formatDate from "../../../helpers/convertToDutchMonth";
import APIResult from "../APIResult";

const KadasterDemo = () => {
  const { result, loading } = useAppSelector((state) => state.transaction);
  // const property: any = oneTimeResult
  const [buildingPhoto, setBuildingPhoto] = useState<string>("");

  const property = {
    Transaction: "Transaction-20100315",
    BagID: null,
    PostCode: "2771HM",
    HouseNumber: 173.0,
    HouseAddition: null,
    City: "BOSKOOP",
    Street: "PARKLAAN",
    HouseType: "T",
    BuildingCode: 11,
    OuterSurfaceArea: 315.0,
    MoreRealEstate: "J",
    RentedOut: "J",
    BusinessRight: "VE",
    MutationVariety: 606,
    FamilyTransaction: "N",
    TransactionDate: "20191001",
    TransactionPrice: 500000.0,
  };

  const overview = [
    {
      title: "Woningeigenschappen",
      details: [
        {
          key: "Transaction",
          value: property.Transaction,
        },
        {
          key: "Bag ID",
          value: property.BagID,
        },
        {
          key: "Postcode",
          value: property.PostCode,
        },
        {
          key: "Huisnummer",
          value: property.HouseNumber,
        },
        {
          key: "Huisnummer toevoeging",
          value: `${property.HouseAddition ? property.HouseAddition : "-"}`,
        },
        {
          key: "Straat",
          value: property.Street,
        },
        {
          key: "Woonplaats",
          value: property.City,
        },
      ],
    },
    {
      title: "Resultaat",
      details: [
        {
          key: "Woningtype",
          value: property.HouseType,
        },
        {
          key: "Gebouwcode",
          value: property.BuildingCode,
        },
        {
          key: "Meerdere objecten in transactie",
          value: property.MoreRealEstate,
        },
        {
          key: "Verhuurde staat",
          value: property.RentedOut,
        },
        {
          key: "Zakelijk recht",
          value: property.BusinessRight,
        },
        {
          key: "Mutatiesoort (openbare verkoop, executieverkoop of overdracht met beperkt recht)",
          value: property.MutationVariety,
        },
        {
          key: "Perceeloppervlakte",
          value: `${convertToEuFormat(property.OuterSurfaceArea)} m2`,
        },
        {
          key: "Familietransactie",
          value: property.FamilyTransaction,
        },
        {
          key: "Verkoopdatum",
          value: formatDate(property.TransactionDate),
        },
        {
          key: "Koopsom",
          value: convertToEuFormat(property.TransactionPrice),
        },
      ],
    },
  ];

  const reference = [
    { text: "Marktwaarde bepalen", path: "https://mopsus.altum.ai/#/avm" },
    {
      text: "Referenties opvragen",
      path: "https://mopsus.altum.ai/#/referentie",
    },
    { text: "WOZ waarde opvragen", path: "https://mopsus.altum.ai/#/woz" },
  ];

  const houseType = (type: string) => {
    switch (type) {
      case "A":
        return "Appartement";
      case "H":
        return "Hoekwoning";
      case "K":
        return "Twee onder 1 kap";
      case "O":
        return "Onbekend";
      case "T":
        return "Tussenwoning";
      case "V":
        return "Vrijstaand";
    }
  };

  const desc = [
    { img: "", title: "Transaction", result: property.Transaction },
    {
      img: "",
      title: "Potentiële Ecowaarde",
      result: property.BusinessRight,
    },
    {
      img: "",
      title: "Gebouwcode",
      result: houseType(property.HouseType),
    },
    {
      img: "",
      title: "Verkoopdatum",
      result: property.TransactionDate,
    },
  ];

  return (
    <>
      <APIResult
        property={property}
        loading={loading}
        title={"Kadaster transactie API"}
        street={property.Street + " " + property.HouseNumber}
        postCode={property.PostCode}
        city={property.City}
        overview={overview}
        reference={reference}
        descSection={desc}
        buildingPhoto={buildingPhoto}
        result={result}
        path="https://docs.altum.ai/apis/avm-api"
        clearResult={undefined}
        modifyResult={undefined}
      />
    </>
  );
};

export default KadasterDemo;
