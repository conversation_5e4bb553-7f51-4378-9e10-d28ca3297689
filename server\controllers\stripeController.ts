import { NextFunction, Request, Response } from "express";
import Stripe from "stripe";
import AppError from "../utils/appError";
import pool from "../db";
import fetchApiUsage from "../utils/fetchApiUsage";
import logger from "../utils/logger";
import {
  TransactionPlan,
  MopsusPlan,
  PlanDb,
  UnlimitedMonthlyPlan,
} from "./stripe-subscription/subscriptionClass";
import Email from "../utils/emailHandler";
import {
  AWS_MOPSUS_USAGE_AVM_UNLIMITED,
  AWS_MOPSUS_USAGE_ECO_UNLIMITED,
  AWS_MOPSUS_USAGE_ENERGY_UNLIMITED,
  AWS_MOPSUS_USAGE_OBJ_DATA_UNLIMITED,
  AWS_MOPSUS_USAGE_REFERENCE_UNLIMITED,
  AWS_MOPSUS_USAGE_WOZ_UNLIMITED,
  STRIPE_MOPSUS_TAX_RATE,
  STRIPE_MOPSUS_TAX_RATE_TEST,
} from "../utils/constants";

// Stripe config
import stripeErrorHandler from "../utils/stripeErrorHandler";
import {
  attachPaymentMethodToCustomer,
  changeUsagePlanSubscription,
  createStripeUsage,
  findOneByStripeId,
  findUserByStripeId,
  returnSubscriptionPlan,
  updateCustomerPaymentMethod,
} from "../utils/stripeHelper";
import {
  StripeBilling,
  StripeObject,
  StripePaymentData,
  User,
} from "../@types";

export const stripe = new Stripe(
  process.env.NODE_ENV === "production"
    ? process.env.STRIPE_SK_LIVE!
    : process.env.STRIPE_SK_TEST!,
  { apiVersion: "2025-02-24.acacia" },
);
export enum SubscriptionTable {
  UserSubscriptionTable = "user_subscriptions",
  TransactionSubscriptionTable = "transaction_subscriptions",
  WozSubscriptionTable = "woz_subscriptions",
  ObjectDataSubscriptionTable = "object_data_subscriptions",
  AVMSubscriptionTable = "avm_subscriptions",
  ReferenceSubscriptionTable = "reference_subscriptions",
  ECOSubscriptionTable = "sustainability_subscriptions",
  EnergySubscriptionTable = "energy_label_subscriptions",
}
// Webhook endpoint
export const createStripeCustomer = async (
  userEmail: string,
): Promise<string> => {
  try {
    const customer = await stripe.customers.create({ email: userEmail });
    return customer.id;
  } catch (error: any) {
    throw new Error(`Stripe error: ${error.message || error}`);
  }
};

const validateStripeSignature = (req: Request) => {
  const signature = req.headers["stripe-signature"];
  if (!signature) {
    throw new Error("Missing Stripe signature");
  }
  return signature;
};

const constructStripeEvent = (
  req: Request,
  signature: string,
  webhookSecret: string,
): Stripe.Event => {
  let event: Stripe.Event;
  try {
    event = stripe.webhooks.constructEvent(req.body, signature, webhookSecret);
  } catch (error: any) {
    throw new Error(`Webhook error: ${error.message}`);
  }
  return event;
};

// Type guard to check for customer property in Stripe objects
const hasCustomerId = (obj: any): obj is { customer: string } => {
  return typeof obj?.customer === "string";
};

export const webhookCheckout = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const signature = validateStripeSignature(req);
    const webhookSecret =
      process.env.NODE_ENV === "production"
        ? `${process.env.STRIPE_WEBHOOK_SECRET}`
        : `${process.env.STRIPE_WEBHOOK_SECRET_TEST}`;

    const event = constructStripeEvent(req, signature as string, webhookSecret);
    // Verify customer ID exists and is valid

    const dataObject = event.data.object as Stripe.Event.Data.Object;

    switch (event.type) {
      case "customer.subscription.created":
        await handleSubscriptionCreatedEvent(dataObject as Stripe.Subscription);
        break;
      case "customer.subscription.deleted":
        await handleSubscriptionDeletedEvent(dataObject as Stripe.Subscription);
        break;
      case "customer.subscription.updated":
        await handleSubscriptionUpdatedEvent(dataObject as Stripe.Subscription);
        break;
      case "charge.succeeded":
        await handleChargeSucceededEvent(dataObject as Stripe.Charge);
        break;
      case "invoice.payment_failed":
        await handlePaymentFailedEvent(dataObject as Stripe.Invoice);
        break;
      default:
        break;
    }

    return res.status(200).json({ status: "success", updatedPlan: true });
  } catch (error) {
    next(stripeErrorHandler(error as Stripe.StripeRawError));
  }
};

const handleSubscriptionCreatedEvent = async (
  dataObject: Stripe.Subscription,
) => {
  const user = await getUserByCustomerId(dataObject.customer as string);
  const planId = dataObject.items.data[0].plan.id;
  const changedPlan = changeUsagePlanSubscription(planId);

  let planObject: TransactionPlan | UnlimitedMonthlyPlan | MopsusPlan;
  if (changedPlan === process.env.AWS_MOPSUS_USAGE_TRANSACTION_PPU) {
    planObject = new TransactionPlan(
      dataObject,
      changedPlan,
      user,
      getPlanTable(changedPlan),
    );
  } else if (
    [
      AWS_MOPSUS_USAGE_WOZ_UNLIMITED,
      AWS_MOPSUS_USAGE_OBJ_DATA_UNLIMITED,
      AWS_MOPSUS_USAGE_AVM_UNLIMITED,
      AWS_MOPSUS_USAGE_REFERENCE_UNLIMITED,
      AWS_MOPSUS_USAGE_ECO_UNLIMITED,
      AWS_MOPSUS_USAGE_ENERGY_UNLIMITED,
    ].includes(changedPlan)
  ) {
    planObject = new UnlimitedMonthlyPlan(
      dataObject,
      changedPlan,
      user,
      getPlanTable(changedPlan),
    );
  } else {
    planObject = new MopsusPlan(
      dataObject,
      changedPlan,
      user,
      SubscriptionTable.UserSubscriptionTable,
    );
  }

  const planChanged = await planObject.createUsage();

  if (!planChanged) {
    throw new Error("Failed to save subscription details in DB");
  }
  new Email(user).subscriptionBought(returnSubscriptionPlan(planId));

  return { status: "success", updatedPlan: true };
};

const handleSubscriptionDeletedEvent = async (
  dataObject: Stripe.Subscription,
) => {
  const user = await getUserByCustomerId(dataObject.customer as string);
  const planId = dataObject.items.data[0].plan.id;
  const changedPlan = changeUsagePlanSubscription(planId);
  let planChanged: boolean;
  if (changedPlan === process.env.AWS_MOPSUS_USAGE_TRANSACTION_PPU) {
    const transactionPlan = new TransactionPlan(
      dataObject,
      changedPlan,
      user,
      SubscriptionTable.TransactionSubscriptionTable,
    );
    planChanged = await transactionPlan.deleteUsagePlan();
  } else if (
    [
      AWS_MOPSUS_USAGE_WOZ_UNLIMITED,
      AWS_MOPSUS_USAGE_OBJ_DATA_UNLIMITED,
    ].includes(changedPlan)
  ) {
    const unlimited = new UnlimitedMonthlyPlan(
      dataObject,
      changedPlan,
      user,
      getPlanTable(changedPlan),
    );
    planChanged = await unlimited.deleteUsagePlan();
  } else {
    const mopsusPlan = new MopsusPlan(
      dataObject,
      changedPlan,
      user,
      SubscriptionTable.UserSubscriptionTable,
    );
    planChanged = await mopsusPlan.deleteUsagePlan();
  }

  if (!planChanged) {
    throw new AppError("Failed to update subscription details in DB", 400);
  }

  new Email(user).subscriptionCancelled(returnSubscriptionPlan(planId));

  return { status: "success", updatedPlan: true };
};

//TODO: [ALTUMAI-1627] update the handleSubscriptionUpdatedEvent function to cater for daily quota subscription and unlimited
const handleSubscriptionUpdatedEvent = async (
  dataObject: Stripe.Subscription,
) => {
  const user = await getUserByCustomerId(dataObject.customer as string);
  const planId = dataObject.items.data[0].plan.id;
  const changedPlan = changeUsagePlanSubscription(planId);

  if (!changedPlan) {
    throw new Error("Failed to change plan subscription");
  }

  let planDb: PlanDb;
  let usagePlanTrackUpdateFunction: Function;
  let usagePlanChangeTimeUpdateFunction: Function;
  let sendRenewalEmailParams: {
    apiKey: string;
    minutesSincePlanChange: number;
    user: User;
    planId: string;
  };
  let minutesSincePlanChange: number = 0;

  if (
    changedPlan === process.env.AWS_MOPSUS_USAGE_TRANSACTION_PPU &&
    user.transaction_api_key
  ) {
    planDb = new PlanDb(
      dataObject.customer,
      "transaction_subscriptions",
      dataObject.id,
      dataObject.items.data[0].id,
    );
    usagePlanTrackUpdateFunction = planDb.updateSubscriptionUsageTrack;
    usagePlanChangeTimeUpdateFunction = planDb.updateSubscriptionChangeTime;
    const transactionQuery = await pool.query(
      `SELECT * FROM transaction_subscriptions WHERE customer_id=$1;`,
      [dataObject.customer],
    );

    if (!dataObject.customer || typeof dataObject.customer !== "string") {
      throw new Error("Invalid customer ID");
    }

    if (transactionQuery.rowCount !== 0) {
      const date = new Date();
      const transactionPlanChangedAt = new Date(
        transactionQuery.rows[0].plan_changed_at,
      );
      const msSinceTransactionPlanChange =
        date.getTime() - transactionPlanChangedAt.getTime();
      minutesSincePlanChange = Math.round(msSinceTransactionPlanChange / 60000);
    }
    sendRenewalEmailParams = {
      apiKey: user.transaction_api_key,
      minutesSincePlanChange,
      user,
      planId,
    };
  } else {
    planDb = new PlanDb(
      dataObject.customer,
      "user_subscriptions",
      dataObject.id,
      dataObject.items.data[0].id,
    );
    usagePlanTrackUpdateFunction = planDb.updateSubscriptionUsageTrackMopsusPPU;

    if (!dataObject.customer || typeof dataObject.customer !== "string") {
      throw new Error("Invalid customer ID");
    }
    usagePlanChangeTimeUpdateFunction =
      planDb.updateSubscriptionChangeTimeMopsusPPU;
    const date = new Date();
    const msSincePlanChange =
      date.getTime() - new Date(user.plan_changed_at).getTime();
    minutesSincePlanChange = Math.round(msSincePlanChange / 60000);
    sendRenewalEmailParams = {
      apiKey: user.api_key,
      minutesSincePlanChange: minutesSincePlanChange,
      user,
      planId,
    };
  }

  try {
    await sendRenewalEmail(sendRenewalEmailParams);
    await usagePlanChangeTimeUpdateFunction();
    await usagePlanTrackUpdateFunction();
    if (changedPlan !== process.env.AWS_MOPSUS_USAGE_TRANSACTION_PPU) {
      const userId = user.user_id;
      await pool.query<Email>(
        "UPDATE usage_emails set api_extension_updated_at=(to_timestamp($1)) where user_id=$2 RETURNING *",
        [Date.now() / 1000, userId],
      );
    }
    return { status: "success", message: "subscription successfully updated" };
  } catch (error: any) {
    return new Error(`Subscription update failed: ${error.message}`);
  }
};

const handleChargeSucceededEvent = async (dataObject: Stripe.Charge) => {
  const user = await getUserByCustomerId(dataObject.customer as string);
  if (dataObject.receipt_url)
    new Email(user, dataObject.receipt_url).sendPaymentReceipt();
  return { success: true, message: "receipt sent" };
};

const handlePaymentFailedEvent = async (dataObject: Stripe.Invoice) => {
  const user = await getUserByCustomerId(dataObject.customer as string);
  if (dataObject.hosted_invoice_url)
    new Email(user, dataObject.hosted_invoice_url).sendFailedPayment();
  return { success: true, message: "Failed invoice email sent" };
};

const getUserByCustomerId = async (
  customerId: string | Stripe.Customer | Stripe.DeletedCustomer | null,
) => {
  const user = await pool.query<User>(
    "SELECT * FROM users WHERE stripe_customer_id=$1",
    [customerId],
  );
  if (user.rows.length === 0) {
    throw new Error("No user found with that stripe customer ID");
  }
  return user.rows[0];
};

async function sendRenewalEmail({
  apiKey,
  minutesSincePlanChange,
  user,
  planId,
}: {
  apiKey: string;
  minutesSincePlanChange: number;
  user: User;
  planId: string;
}) {
  const count = await fetchApiUsage(apiKey, minutesSincePlanChange);
  new Email(user).sendMonthlyResetApiUsage(
    returnSubscriptionPlan(planId),
    count,
  );
}
export const createSubscription = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { paymentMethodId, customerId, meteredPriceId, recurringPriceId } =
      req.body;
    const { paymentSaved, subscription } = await subscriptionCreation(
      paymentMethodId,
      customerId,
      meteredPriceId,
      recurringPriceId,
    );
    if (!paymentSaved) {
      next(new Error("Failed to save payment details in DB"));
      return;
    }

    res.status(200).send(subscription);
  } catch (error: any) {
    return res.status(402).send({ error: { message: error.message } });
  }
};

export const createTransactionSubscription = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { paymentMethodId, customerId, meteredPriceId, recurringPriceId } =
      req.body;
    const { paymentSaved, subscription } = await subscriptionCreation(
      paymentMethodId,
      customerId,
      meteredPriceId,
      recurringPriceId,
    );
    if (!paymentSaved) {
      next(new Error("Failed to save payment details in DB"));
      return;
    }

    res.status(200).send(subscription);
  } catch (error: any) {
    console.log(error);
    return res.status(402).send({ error: { message: error.message } });
  }
};
export const cancelSubscription = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { apikey, customerId, planId } = req.body;
    const changedPlan = changeUsagePlanSubscription(planId);
    const table = getPlanTable(changedPlan);
    if (!apikey && !customerId) {
      next(new AppError("Could not cancel subscription.", 400, true));
      return;
    }

    const query = await findOneByStripeId(table, customerId);
    if (query.rowCount === 0) {
      next(new AppError("Could not cancel subscription.", 400, true));
      return;
    }

    const subscriptionItem = query.rows[0].subscription_item;
    const subscriptionId = query.rows[0].subscription_id;

    // Before cancelling, fetch usage of user between last usage_tracked_at - current time
    const user = await findUserByStripeId(customerId);
    if (user.rows.length === 0) {
      return;
    }
    const meteredBilling = user.rows[0].meterd_billing;
    const { active } = query.rows[0];
    const date = new Date();
    const msSincePlanChange =
      date.getTime() - new Date(user.rows[0].usage_tracked_at).getTime();
    const minutesSincePlanChange = Math.round(msSincePlanChange / 60000);
    const minutesSince = minutesSincePlanChange;

    const count = await fetchApiUsage(apikey, minutesSince);
    // Add usage
    if (meteredBilling && active) {
      await createStripeUsage(subscriptionItem, count);
    }
    // Cancel the subscription and invoice them
    const canceledSubscription = await deleteSubscription(subscriptionId);
    res.status(200).send(canceledSubscription);
  } catch (error: any) {
    console.log(error);
    return res.status(402).send({ error: { message: error.message } });
  }
};

export const cancelTransactionSubscription = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { apikey, customerId } = req.body;
    const table = "transaction_subscriptions";
    if (!apikey && !customerId) {
      next(new AppError("Could not cancel subscription.", 400, true));
      return;
    }

    const query = await findOneByStripeId(table, customerId);
    if (query.rowCount === 0) {
      next(new AppError("Could not cancel subscription.", 400, true));
      return;
    }

    const subscriptionItem = query.rows[0].subscription_item;
    const subscriptionId = query.rows[0].subscription_id;

    // Before cancelling, fetch usage of user between last usage_tracked_at - current time
    const user = await findUserByStripeId(customerId);
    if (user.rows.length === 0) {
      return;
    }

    const date = new Date();
    const msSincePlanChange =
      date.getTime() - new Date(query.rows[0].usage_tracked_at).getTime();

    const minutesSincePlanChange = Math.round(msSincePlanChange / 60000);

    const count = await fetchApiUsage(apikey, minutesSincePlanChange);
    // Add usage
    await createStripeUsage(subscriptionItem, count);

    // Cancel the subscription and invoice them
    const canceledSubscription = await deleteSubscription(subscriptionId);
    res.status(200).send(canceledSubscription);
  } catch (error: any) {
    console.log(error);
    return res.status(402).send({ error: { message: error.message } });
  }
};

export const updateSubscription = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const {
      paymentMethodId,
      customerId,
      meteredPriceId,
      recurringPriceId,
      apikey,
    } = req.body;

    await attachPaymentMethodToCustomer(paymentMethodId, customerId);

    await updateCustomerPaymentMethod(customerId, paymentMethodId);
    const changedPlan = changeUsagePlanSubscription(meteredPriceId);
    const table = getPlanTable(changedPlan);
    if (!apikey && !customerId) {
      next(new AppError("Could not update subscription.", 400, true));
      return;
    }

    const query = await findOneByStripeId(table, customerId);
    if (query.rowCount === 0) {
      next(new AppError("Could not cancel subscription.", 400, true));
      return;
    }

    const subscriptionItem = query.rows[0].subscription_item;
    const subscriptionId = query.rows[0].subscription_id;

    // Before cancelling, fetch usage of user between last usage_tracked_at - current time
    const user = await findUserByStripeId(customerId);
    if (user.rows.length === 0) {
      return;
    }
    const meteredBilling = user.rows[0].meterd_billing;
    const { active } = query.rows[0];
    const date = new Date();
    const msSincePlanChange =
      date.getTime() - new Date(user.rows[0].usage_tracked_at).getTime();

    const minutesSincePlanChange = Math.round(msSincePlanChange / 60000);

    const minutesSince = minutesSincePlanChange;

    const count = await fetchApiUsage(apikey, minutesSince);
    // Add usage
    if (meteredBilling && active) {
      await createStripeUsage(subscriptionItem, count);
    }

    // Cancel the existing subscription
    await deleteSubscription(subscriptionId);

    const { paymentSaved, subscription } = await subscriptionCreation(
      paymentMethodId,
      customerId,
      meteredPriceId,
      recurringPriceId,
    );
    if (!paymentSaved) {
      next(new Error("Failed to save payment details in DB"));
      return;
    }
    res.status(200).send(subscription);
  } catch (error: any) {
    return res.status(402).send({ error: { message: error.message } });
  }
};

export const updateTransactionSubscription = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const {
      paymentMethodId,
      customerId,
      meteredPriceId,
      recurringPriceId,
      apikey,
    } = req.body;
    logger.info("running transaction sunscription update");
    await attachPaymentMethodToCustomer(paymentMethodId, customerId);

    await updateCustomerPaymentMethod(customerId, paymentMethodId);

    if (!apikey && !customerId) {
      next(new AppError("Could not update subscription.", 400, true));
      return;
    }
    const query = await findOneByStripeId(
      "transaction_subscriptions",
      customerId,
    );
    if (query.rowCount === 0) {
      next(new AppError("Could not cancel subscription.", 400, true));
      return;
    }
    const subscriptionItem = query.rows[0].subscription_item;
    const subscriptionId = query.rows[0].subscription_id;

    // Before cancelling, fetch usage of user between last usage_tracked_at - current time
    const user = await findUserByStripeId(customerId);
    if (user.rows.length === 0) {
      return;
    }

    const date = new Date();
    const msSincePlanChange =
      date.getTime() - new Date(query.rows[0].usage_tracked_at).getTime();
    const minutesSincePlanChange = Math.round(msSincePlanChange / 60000);

    const count = await fetchApiUsage(apikey, minutesSincePlanChange);
    // Add usage
    await stripe.subscriptionItems.createUsageRecord(subscriptionItem, {
      quantity: count,
    });

    // Cancel the existing subscription
    await deleteSubscription(subscriptionId);

    const { paymentSaved, subscription } = await subscriptionCreation(
      paymentMethodId,
      customerId,
      meteredPriceId,
      recurringPriceId,
    );
    if (!paymentSaved) {
      next(new Error("Failed to save payment details in DB"));
      return;
    }
    res.status(200).send(subscription);
  } catch (error: any) {
    return res.status(402).send({ error: { message: error.message } });
  }
};

export const createUsage = async (subscriptionItem: string, amount: number) => {
  try {
    const usageRecord = await stripe.subscriptionItems.createUsageRecord(
      subscriptionItem,
      { quantity: amount },
    );
    if (!usageRecord) {
      return false;
    }

    return true;
  } catch (error: any) {
    logger.error(error.message);
    stripeErrorHandler(error as Stripe.StripeRawError);
    return false;
  }
};

const savePaymentInDB = async (paymentData: StripePaymentData) => {
  try {
    const { paymentMethodId, customerId } = paymentData;
    const paymentMethodResponse = await stripe.paymentMethods.retrieve(
      paymentMethodId,
    );
    const { card, sepa_debit } = paymentMethodResponse;
    if (card) {
      await pool.query(`DELETE FROM stripe_billing WHERE customer_id=$1;`, [
        customerId,
      ]);
      const paymentSaved = await pool.query<StripeBilling>(
        `INSERT INTO stripe_billing (customer_id, payment_method_id, last_four_digits) VALUES ($1, $2, $3);`,
        [customerId, paymentMethodId, card?.last4],
      );
      if (paymentSaved.rowCount === 0) {
        return false;
      }
    }
    if (sepa_debit) {
      await pool.query(`DELETE FROM stripe_billing WHERE customer_id=$1;`, [
        customerId,
      ]);
      const paymentSaved = await pool.query<StripeBilling>(
        `INSERT INTO stripe_billing (customer_id, payment_method_id, last_four_digits) VALUES ($1, $2, $3);`,
        [customerId, paymentMethodId, sepa_debit?.last4],
      );
      if (paymentSaved.rowCount === 0) {
        return false;
      }
    }

    return true;
  } catch (error: any) {
    return new AppError(error.message, 500);
  }
};
const getPlanTable = (plan: string) => {
  switch (plan) {
    case process.env.AWS_MOPSUS_USAGE_PLAN_PPU:
      return SubscriptionTable.UserSubscriptionTable;
    case process.env.AWS_MOPSUS_USAGE_TRANSACTION_PPU:
      return SubscriptionTable.TransactionSubscriptionTable;
    case AWS_MOPSUS_USAGE_WOZ_UNLIMITED:
      return SubscriptionTable.WozSubscriptionTable;
    case AWS_MOPSUS_USAGE_OBJ_DATA_UNLIMITED:
      return SubscriptionTable.ObjectDataSubscriptionTable;
    case AWS_MOPSUS_USAGE_AVM_UNLIMITED:
      return SubscriptionTable.AVMSubscriptionTable;
    case AWS_MOPSUS_USAGE_REFERENCE_UNLIMITED:
      return SubscriptionTable.ReferenceSubscriptionTable;
    case AWS_MOPSUS_USAGE_ECO_UNLIMITED:
      return SubscriptionTable.ECOSubscriptionTable;
    case AWS_MOPSUS_USAGE_ENERGY_UNLIMITED:
      return SubscriptionTable.EnergySubscriptionTable;
    default:
      return "";
  }
};
export const checkSubscriptionStatus = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { stripe_customer_id } = req.user as User;
    const table = "user_subscriptions";
    const { status, subscription } = await checkStripeSubscription(
      table,
      stripe_customer_id,
    );
    res.status(200).json({
      status,
      subscription,
    });
  } catch (error) {
    console.log(error);
    next(
      new AppError("je bent momenteel niet geabonneerd op een abonnement", 500),
    );
  }
};

export const updatePaymentDetails = async (req: Request, res: Response) => {
  try {
    await stripe.paymentMethods.attach(req.body.paymentMethodId, {
      customer: req.body.customerId,
    });

    // Change the default invoice settings on the customer to the new payment method

    res
      .status(200)
      .json({ success: true, msg: "Payment method successfully updated" });
  } catch (error) {
    res.status(500).json({ error });
  }
};

export const removePaymentDetails = async (req: Request, res: Response) => {
  try {
    await stripe.paymentMethods.detach(req.body.paymentMethodId);

    // Change the default invoice settings on the customer to the new payment method

    res
      .status(200)
      .json({ success: true, msg: " Betalingsmethode succesvol verwijderd" });
  } catch (error) {
    res.status(500).json({ error });
  }
};
export const setDefaultPaymentMethod = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    if (req.user == null) {
      return;
    }
    const user = req.user as User;
    await updateCustomerPaymentMethod(
      user.stripe_customer_id,
      req.body.paymentMethodId,
    );
    const paymentData = {
      paymentMethodId: req.body.paymentMethodId,
      customerId: user.stripe_customer_id,
    };
    const paymentSaved = await savePaymentInDB(paymentData);
    if (!paymentSaved) {
      next(new Error("Failed to save payment details in DB"));
      return;
    }
    res.status(200).json({
      success: true,
      msg: "Standaard betalingsmethode succesvol gewijzigd",
    });
  } catch (error) {
    logger.error(error);
    res.status(500).json(error);
  }
};

export const checkTransactionSubscriptionStatus = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { stripe_customer_id } = req.user as User;
    const table = "transaction_subscriptions";
    const { status, subscription } = await checkStripeSubscription(
      table,
      stripe_customer_id,
    );

    res.status(200).json({
      status,
      subscription,
    });
  } catch (error) {
    console.log(error);
    next(
      new AppError("je bent momenteel niet geabonneerd op een abonnement", 500),
    );
  }
};
async function subscriptionCreation(
  paymentMethodId: string,
  customerId: string,
  meteredPriceId: string,
  recurringPriceId: string,
) {
  await attachPaymentMethodToCustomer(paymentMethodId, customerId);

  await updateCustomerPaymentMethod(customerId, paymentMethodId);

  // Create the subscription
  const subscription = await createSubscriptionObj(
    customerId,
    meteredPriceId,
    recurringPriceId,
  );

  // Store payment method ID in DB
  const paymentData = {
    paymentMethodId,
    customerId,
  };
  const paymentSaved = await savePaymentInDB(paymentData);
  return { paymentSaved, subscription };
}

async function checkStripeSubscription(table: string, customerId: string) {
  const subscriptionTable = await pool.query(
    `SELECT * FROM ${table} where customer_id=$1;`,
    [customerId],
  );
  let apiKey;
  const subscriptionId = subscriptionTable.rows[0].subscription_id;
  if (
    table !== SubscriptionTable.UserSubscriptionTable ||
    table !== SubscriptionTable.UserSubscriptionTable
  ) {
    apiKey = subscriptionTable.rows[0].api_key;
  }

  const subscription = await stripe.subscriptions.retrieve(subscriptionId);

  const { status } = subscription;
  return { status, subscription, apiKey };
}

async function createSubscriptionObj(
  customerId: string,
  meteredPriceId: string,
  recurringPriceId: string,
) {
  const taxRate =
    process.env.NODE_ENV === "production"
      ? STRIPE_MOPSUS_TAX_RATE
      : STRIPE_MOPSUS_TAX_RATE_TEST;

  const body: Stripe.SubscriptionCreateParams = {
    customer: customerId,
    items: [],
    expand: ["latest_invoice.payment_intent"],
  };
  if (meteredPriceId && body.items != null) {
    body.items.push({ price: meteredPriceId, tax_rates: [taxRate] });
  }
  if (recurringPriceId && body.items != null) {
    body.items.push({
      price: recurringPriceId,
      tax_rates: [taxRate],
    });
  }
  const subscription = await stripe.subscriptions.create(body);
  return subscription;
}
export async function deleteSubscription(subscriptionId: string) {
  return await stripe.subscriptions.cancel(subscriptionId, {
    invoice_now: true,
  });
}

export async function deleteStripeCustomer(customerId: string): Promise<void> {
  try {
    await stripe.customers.del(customerId);
  } catch (error: any) {
    throw new AppError(
      `Failed to delete Stripe customer: ${error.message}`,
      500,
    );
  }
}

export const checkWozSubscriptionStatus = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { stripe_customer_id } = req.user as User;
    const table = SubscriptionTable.WozSubscriptionTable;
    const { status, apiKey } = await checkStripeSubscription(
      table,
      stripe_customer_id,
    );

    res.status(200).json({
      status,
      apiKey,
    });
  } catch (error) {
    console.log(error);
    next(
      new AppError("je bent momenteel niet geabonneerd op een abonnement", 500),
    );
  }
};

export const checkObjectDataSubscriptionStatus = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { stripe_customer_id } = req.user as User;
    const table = SubscriptionTable.ObjectDataSubscriptionTable;
    const { status, apiKey } = await checkStripeSubscription(
      table,
      stripe_customer_id,
    );

    res.status(200).json({
      status,
      apiKey,
    });
  } catch (error) {
    console.log(error);
    next(
      new AppError("je bent momenteel niet geabonneerd op een abonnement", 500),
    );
  }
};

export const checkAVMSubscriptionStatus = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { stripe_customer_id } = req.user as User;
    const table = SubscriptionTable.AVMSubscriptionTable;
    const { status, apiKey } = await checkStripeSubscription(
      table,
      stripe_customer_id,
    );

    res.status(200).json({
      status,
      apiKey,
    });
  } catch (error) {
    console.log(error);
    next(
      new AppError("je bent momenteel niet geabonneerd op een abonnement", 500),
    );
  }
};

export const checkReferenceSubscriptionStatus = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { stripe_customer_id } = req.user as User;
    const table = SubscriptionTable.ReferenceSubscriptionTable;
    const { status, apiKey } = await checkStripeSubscription(
      table,
      stripe_customer_id,
    );

    res.status(200).json({
      status,
      apiKey,
    });
  } catch (error) {
    console.log(error);
    next(
      new AppError("je bent momenteel niet geabonneerd op een abonnement", 500),
    );
  }
};

export const checkECOSubscriptionStatus = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { stripe_customer_id } = req.user as User;
    const table = SubscriptionTable.ECOSubscriptionTable;
    const { status, apiKey } = await checkStripeSubscription(
      table,
      stripe_customer_id,
    );

    res.status(200).json({
      status,
      apiKey,
    });
  } catch (error) {
    console.log(error);
    next(
      new AppError("je bent momenteel niet geabonneerd op een abonnement", 500),
    );
  }
};

export const checkEnergySubscriptionStatus = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { stripe_customer_id } = req.user as User;
    const table = SubscriptionTable.EnergySubscriptionTable;
    const { status, apiKey } = await checkStripeSubscription(
      table,
      stripe_customer_id,
    );

    res.status(200).json({
      status,
      apiKey,
    });
  } catch (error) {
    console.log(error);
    next(
      new AppError("je bent momenteel niet geabonneerd op een abonnement", 500),
    );
  }
};
