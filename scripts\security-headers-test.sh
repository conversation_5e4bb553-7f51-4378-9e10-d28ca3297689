#!/bin/bash

# Security Headers Testing Script for Mopsus Data Platform
# This script validates that all required security headers are properly configured

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REPORT_DIR="security-reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
HEADERS_REPORT="$REPORT_DIR/security-headers-test-$TIMESTAMP.txt"

# Default target URL (can be overridden)
TARGET_URL="${1:-https://mopsus-test.altum.ai}"

# Create reports directory
mkdir -p $REPORT_DIR

# Initialize report
{
    echo "=========================================="
    echo "SECURITY HEADERS VALIDATION REPORT"
    echo "=========================================="
    echo "Test Date: $(date)"
    echo "Target URL: $TARGET_URL"
    echo "Project: Mopsus Data Platform"
    echo ""
} > "$HEADERS_REPORT"

echo -e "${BLUE}🔒 Testing Security Headers for: $TARGET_URL${NC}"
echo ""

# Function to test a specific header
test_header() {
    local header_name="$1"
    local expected_pattern="$2"
    local description="$3"
    local severity="$4"
    
    echo -e "${YELLOW}Testing: $description${NC}"
    
    # Get the header value
    header_value=$(curl -s -I "$TARGET_URL" | grep -i "^$header_name:" | cut -d' ' -f2- | tr -d '\r\n' || echo "")
    
    if [[ -n "$header_value" ]]; then
        if [[ "$header_value" =~ $expected_pattern ]]; then
            echo -e "${GREEN}✅ PASS: $header_name header is properly configured${NC}"
            echo "   Value: $header_value"
            echo "✅ PASS: $description" >> "$HEADERS_REPORT"
            echo "   Header: $header_name: $header_value" >> "$HEADERS_REPORT"
        else
            echo -e "${YELLOW}⚠️  WARN: $header_name header exists but may not be optimal${NC}"
            echo "   Value: $header_value"
            echo "   Expected pattern: $expected_pattern"
            echo "⚠️  WARN: $description" >> "$HEADERS_REPORT"
            echo "   Header: $header_name: $header_value" >> "$HEADERS_REPORT"
            echo "   Expected pattern: $expected_pattern" >> "$HEADERS_REPORT"
        fi
    else
        echo -e "${RED}❌ FAIL: $header_name header is missing${NC}"
        echo "❌ FAIL: $description" >> "$HEADERS_REPORT"
        echo "   Header: $header_name is MISSING" >> "$HEADERS_REPORT"
        
        if [[ "$severity" == "HIGH" ]]; then
            echo -e "${RED}   🚨 HIGH SEVERITY: This header is critical for security${NC}"
            echo "   🚨 HIGH SEVERITY: This header is critical for security" >> "$HEADERS_REPORT"
        fi
    fi
    echo "" >> "$HEADERS_REPORT"
    echo ""
}

# Function to test for absence of headers (information disclosure)
test_header_absence() {
    local header_name="$1"
    local description="$2"
    
    echo -e "${YELLOW}Testing: $description${NC}"
    
    header_value=$(curl -s -I "$TARGET_URL" | grep -i "^$header_name:" | cut -d' ' -f2- | tr -d '\r\n' || echo "")
    
    if [[ -z "$header_value" ]]; then
        echo -e "${GREEN}✅ PASS: $header_name header is not present (good for security)${NC}"
        echo "✅ PASS: $description" >> "$HEADERS_REPORT"
        echo "   Header: $header_name is properly hidden" >> "$HEADERS_REPORT"
    else
        echo -e "${YELLOW}⚠️  WARN: $header_name header is present${NC}"
        echo "   Value: $header_value"
        echo "   This may reveal server information"
        echo "⚠️  WARN: $description" >> "$HEADERS_REPORT"
        echo "   Header: $header_name: $header_value" >> "$HEADERS_REPORT"
        echo "   Recommendation: Hide this header to prevent information disclosure" >> "$HEADERS_REPORT"
    fi
    echo "" >> "$HEADERS_REPORT"
    echo ""
}

# Main security headers tests
echo -e "${BLUE}🛡️  Testing Required Security Headers${NC}"
echo ""

# Test Content Security Policy
test_header "Content-Security-Policy" "default-src.*'self'" "Content Security Policy (CSP) - Prevents XSS and injection attacks" "HIGH"

# Test X-Frame-Options (Clickjacking protection)
test_header "X-Frame-Options" "(DENY|SAMEORIGIN)" "X-Frame-Options - Prevents clickjacking attacks" "HIGH"

# Test X-Content-Type-Options
test_header "X-Content-Type-Options" "nosniff" "X-Content-Type-Options - Prevents MIME type sniffing" "MEDIUM"

# Test Strict-Transport-Security (HSTS)
test_header "Strict-Transport-Security" "max-age=[0-9]+" "Strict-Transport-Security (HSTS) - Enforces HTTPS" "HIGH"

# Test X-XSS-Protection
test_header "X-XSS-Protection" "1.*mode=block" "X-XSS-Protection - XSS filtering" "MEDIUM"

# Test Referrer-Policy
test_header "Referrer-Policy" "(strict-origin|strict-origin-when-cross-origin|no-referrer)" "Referrer-Policy - Controls referrer information" "LOW"

echo -e "${BLUE}🔍 Testing Information Disclosure Headers${NC}"
echo ""

# Test for headers that should NOT be present
test_header_absence "X-Powered-By" "X-Powered-By header should be hidden"
test_header_absence "Server" "Server header should be hidden or minimal"

# Additional security checks
echo -e "${BLUE}🔧 Additional Security Checks${NC}"
echo ""

# Check if HTTPS is enforced
echo -e "${YELLOW}Testing: HTTPS enforcement${NC}"
http_response=$(curl -s -o /dev/null -w "%{http_code}" "http://$(echo $TARGET_URL | sed 's|https://||')" || echo "000")
if [[ "$http_response" == "301" || "$http_response" == "302" ]]; then
    echo -e "${GREEN}✅ PASS: HTTP redirects to HTTPS${NC}"
    echo "✅ PASS: HTTPS enforcement - HTTP redirects to HTTPS" >> "$HEADERS_REPORT"
else
    echo -e "${RED}❌ FAIL: HTTP does not redirect to HTTPS (Response: $http_response)${NC}"
    echo "❌ FAIL: HTTPS enforcement - HTTP does not redirect to HTTPS" >> "$HEADERS_REPORT"
fi
echo ""

# Check SSL/TLS configuration
echo -e "${YELLOW}Testing: SSL/TLS configuration${NC}"
ssl_info=$(echo | openssl s_client -connect "$(echo $TARGET_URL | sed 's|https://||'):443" -servername "$(echo $TARGET_URL | sed 's|https://||')" 2>/dev/null | openssl x509 -noout -subject -dates 2>/dev/null || echo "SSL check failed")
if [[ "$ssl_info" != "SSL check failed" ]]; then
    echo -e "${GREEN}✅ PASS: SSL certificate is valid${NC}"
    echo "✅ PASS: SSL/TLS configuration - Certificate is valid" >> "$HEADERS_REPORT"
    echo "   Certificate info: $ssl_info" >> "$HEADERS_REPORT"
else
    echo -e "${RED}❌ FAIL: SSL certificate check failed${NC}"
    echo "❌ FAIL: SSL/TLS configuration - Certificate check failed" >> "$HEADERS_REPORT"
fi
echo ""

# Generate summary
{
    echo ""
    echo "=========================================="
    echo "SUMMARY AND RECOMMENDATIONS"
    echo "=========================================="
    echo ""
    echo "CRITICAL SECURITY HEADERS (Must Fix):"
    echo "- Content-Security-Policy: Prevents XSS and injection attacks"
    echo "- X-Frame-Options: Prevents clickjacking"
    echo "- Strict-Transport-Security: Enforces HTTPS"
    echo ""
    echo "IMPORTANT SECURITY HEADERS (Should Fix):"
    echo "- X-Content-Type-Options: Prevents MIME sniffing"
    echo "- X-XSS-Protection: Additional XSS protection"
    echo ""
    echo "INFORMATION DISCLOSURE (Should Hide):"
    echo "- X-Powered-By: Remove to hide server technology"
    echo "- Server: Minimize or remove version information"
    echo ""
    echo "NEXT STEPS:"
    echo "1. Fix any FAILED tests immediately"
    echo "2. Address WARNINGS in next deployment"
    echo "3. Re-run this test after fixes"
    echo "4. Integrate into CI/CD pipeline"
    echo ""
    echo "For more information, see:"
    echo "- OWASP Secure Headers Project"
    echo "- Mozilla Security Guidelines"
    echo "- docs/security-scanning-setup.md"
    echo ""
} >> "$HEADERS_REPORT"

echo -e "${GREEN}🎉 Security headers testing completed!${NC}"
echo -e "${BLUE}📋 Full report saved to: $HEADERS_REPORT${NC}"
echo -e "${YELLOW}💡 Tip: Run this test after each deployment to ensure security headers are working${NC}"
