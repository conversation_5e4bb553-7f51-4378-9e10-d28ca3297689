import pool from "../../db";
import AppError from "../../utils/appError";

export class BlockedUserService {
  /**
   * Add an email to the blocked list
   */
  static async blockEmail(email: string, reason?: string): Promise<void> {
    try {
      await pool.query(
        `
        INSERT INTO blocked_users (email, reason)
        VALUES ($1, $2)
        ON CONFLICT (email) DO UPDATE
        SET reason = $2, updated_at = NOW()
        `,
        [email.toLowerCase(), reason || null],
      );
    } catch (error: any) {
      throw new AppError(`Failed to block email: ${error.message}`, 500);
    }
  }

  /**
   * Remove an email from the blocked list
   */
  static async unblockEmail(email: string): Promise<void> {
    try {
      const result = await pool.query(
        `DELETE FROM blocked_users WHERE email = $1`,
        [email.toLowerCase()],
      );

      if (result.rowCount === 0) {
        throw new AppError(`Email ${email} is not blocked`, 404);
      }
    } catch (error: any) {
      if (error instanceof AppError) throw error;
      throw new AppError(`Failed to unblock email: ${error.message}`, 500);
    }
  }

  /**
   * Check if an email is blocked
   */
  static async isEmailBlocked(email: string): Promise<boolean> {
    try {
      const result = await pool.query(
        `SELECT EXISTS(SELECT 1 FROM blocked_users WHERE email = $1)`,
        [email.toLowerCase()],
      );
      return result.rows[0].exists;
    } catch (error: any) {
      throw new AppError(`Failed to check email status: ${error.message}`, 500);
    }
  }

  /**
   * Get all blocked emails with pagination
   */
  static async getBlockedEmails(page: number = 1, limit: number = 10) {
    try {
      const offset = (page - 1) * limit;
      const result = await pool.query(
        `
        SELECT email, reason, created_at, updated_at
        FROM blocked_users
        ORDER BY created_at DESC
        LIMIT $1 OFFSET $2
        `,
        [limit, offset],
      );

      const countResult = await pool.query(
        "SELECT COUNT(*) FROM blocked_users",
      );
      const totalItems = parseInt(countResult.rows[0].count);

      return {
        items: result.rows,
        totalItems,
        currentPage: page,
        totalPages: Math.ceil(totalItems / limit),
        hasNextPage: offset + limit < totalItems,
        hasPreviousPage: page > 1,
      };
    } catch (error: any) {
      throw new AppError(
        `Failed to fetch blocked emails: ${error.message}`,
        500,
      );
    }
  }
}
