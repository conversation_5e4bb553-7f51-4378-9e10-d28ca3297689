import React from "react";

const Modal = React.forwardRef<
  HTMLDivElement,
  {
    isOpen: boolean;
    onClose: () => void;
    children: React.ReactNode;
  }
>((props, ref) => {
  const { isOpen, onClose, children } = props;

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center"
      ref={ref}
    >
      <div className="fixed inset-0 bg-black opacity-50" onClick={onClose} />
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4 relative z-10">
        {children}
      </div>
    </div>
  );
});

Modal.displayName = "Modal";

export default Modal;
