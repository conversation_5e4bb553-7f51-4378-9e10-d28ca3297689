import React from "react";
import { Redirect } from "react-router";
import { useAppSelector } from "../../../redux/hooks";
import AvmForm from "./AvmForm";
import Loading from "../../Loading";
import { FormProvider } from "../../../pages/Api/usage-pages/components/FormContext";
const Avm = () => {
  const { result, loading } = useAppSelector((state) => state.avm);

  if (loading) {
    return <Loading />;
  }

  return (
    <FormProvider>
      {Object.keys(result).length > 0 ? (
        <Redirect
          to={{
            pathname: "/avm-result",
          }}
        />
      ) : (
        <AvmForm />
      )}
    </FormProvider>
  );
};

export default Avm;
