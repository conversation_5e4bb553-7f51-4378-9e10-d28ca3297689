node_modules/


*node_modules/

*.env
.env
*.sql

/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

package-lock.json

build/
.production

certificates/
debug.log

*.log

*dist/
.qodo

# Security scanning reports - DO NOT COMMIT
security-reports/
*-report.json
*-report.txt
semgrep-*.json
eslint-*-report.json
trivy-report.json
docker-bench-report.txt
security-summary.txt
security-summary.md
test-security/
