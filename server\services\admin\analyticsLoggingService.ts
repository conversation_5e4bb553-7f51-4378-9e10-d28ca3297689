import pool from "../../db";
import { User } from "../../@types";

export interface AnalyticsEvent {
  event_type: string;
  user_id?: string;
  metadata?: Record<string, any>;
  timestamp?: Date;
}

export class AnalyticsLoggingService {
  static async logEvent(event: AnalyticsEvent) {
    const { event_type, user_id, metadata } = event;

    const query = `
      INSERT INTO analytics_logger (event, category)
      VALUES ($1, $2)
      RETURNING *
    `;

    try {
      const result = await pool.query(query, [
        event_type,
        metadata?.category || "Other",
      ]);
      return result.rows[0];
    } catch (error) {
      console.error("Error logging analytics event:", error);
      throw error;
    }
  }

  static async getActiveUsersMetrics(timeframe: string = "30d") {
    const days = parseInt(timeframe.replace("d", ""));
    const halfPeriod = Math.floor(days / 2);

    const query = `
      WITH daily_active_users AS (
        SELECT 
          date as day,
          COUNT(DISTINCT id) as active_users
        FROM analytics_logger
        WHERE ((category = 'API' AND event = 'ApiCall')
           OR (category = 'Authentication' AND event = 'Signin'))
        AND date >= CURRENT_DATE - INTERVAL '${days} days'
        GROUP BY date
      ),
      previous_period AS (
        SELECT AVG(active_users) as avg_users
        FROM daily_active_users
        WHERE day < CURRENT_DATE - INTERVAL '${days / 2} days'
      ),
      current_period AS (
        SELECT AVG(active_users) as avg_users
        FROM daily_active_users
        WHERE day >= CURRENT_DATE - INTERVAL '${days / 2} days'
      )
      SELECT
        (SELECT COUNT(DISTINCT id)
         FROM analytics_logger
         WHERE ((category = 'API' AND event = 'ApiCall')
            OR (category = 'Authentication' AND event = 'Signin'))
         AND date = CURRENT_DATE) as current_active_users,
        COALESCE(
          ROUND(
            ((cp.avg_users - pp.avg_users) / NULLIF(pp.avg_users, 0)) * 100,
            2
          ),
          0
        ) as growth_percentage
      FROM previous_period pp, current_period cp;
    `;

    try {
      const result = await pool.query(query);
      console.log("active users", result.rows[0]);
      return result.rows[0];
    } catch (error) {
      console.error("Error getting active users metrics:", error);
      throw error;
    }
  }

  static async getNewUsersMetrics(timeframe: string = "30d") {
    const days = parseInt(timeframe.replace("d", ""));
    const halfPeriod = Math.floor(days / 2);

    const query = `
      WITH new_users_count AS (
        SELECT 
          date as day,
          COUNT(*) as new_users
        FROM analytics_logger
        WHERE category = 'Authentication'
        AND event IN ('Signup', 'Signup-with-google', 'Signup-with-Linkedin')
        AND date >= CURRENT_DATE - INTERVAL '${days} days'
        GROUP BY date
      ),
      previous_period AS (
        SELECT SUM(new_users) as total_users
        FROM new_users_count
        WHERE day < CURRENT_DATE - INTERVAL '${days / 2} days'
      ),
      current_period AS (
        SELECT SUM(new_users) as total_users
        FROM new_users_count
        WHERE day >= CURRENT_DATE - INTERVAL '${days / 2} days'
      )
      SELECT
        (SELECT COUNT(*)
         FROM analytics_logger
         WHERE category = 'Authentication'
         AND event IN ('Signup', 'Signup-with-google', 'Signup-with-Linkedin')
         AND date = CURRENT_DATE) as new_users_today,
        COALESCE(
          ROUND(
            ((cp.total_users - pp.total_users) / NULLIF(pp.total_users, 0)) * 100,
            2
          ),
          0
        ) as growth_percentage
      FROM previous_period pp, current_period cp;
    `;

    try {
      const result = await pool.query(query);
      return result.rows[0];
    } catch (error) {
      console.error("Error getting new users metrics:", error);
      throw error;
    }
  }

  static async getPropertyGenerationMetrics(timeframe: string = "30d") {
    const days = parseInt(timeframe.replace("d", ""));
    const halfPeriod = Math.floor(days / 2);

    const query = `
      WITH daily_generations AS (
        SELECT 
          date as day,
          COUNT(*) as generations
        FROM analytics_logger
        WHERE category = 'Property'
        AND event = 'PropertyGenerated'
        AND date >= CURRENT_DATE - INTERVAL '${days} days'
        GROUP BY date
      ),
      previous_period AS (
        SELECT SUM(generations) as total_generations
        FROM daily_generations
        WHERE day < CURRENT_DATE - INTERVAL '${days / 2} days'
      ),
      current_period AS (
        SELECT SUM(generations) as total_generations
        FROM daily_generations
        WHERE day >= CURRENT_DATE - INTERVAL '${days / 2} days'
      )
      SELECT
        (SELECT COUNT(*)
         FROM analytics_logger
         WHERE category = 'Property'
         AND event = 'PropertyGenerated'
         AND date >= CURRENT_DATE - INTERVAL '${days} days') as total_generations,
        COALESCE(
          ROUND(
            ((cp.total_generations - pp.total_generations) / NULLIF(pp.total_generations, 0)) * 100,
            2
          ),
          0
        ) as growth_percentage
      FROM previous_period pp, current_period cp;
    `;

    try {
      const result = await pool.query(query);
      return result.rows[0];
    } catch (error) {
      console.error("Error getting property generation metrics:", error);
      throw error;
    }
  }

  static async getOnboardingFunnelMetrics(timeframe: string = "30d") {
    const query = `
      WITH funnel_steps AS (
        SELECT
          COUNT(*) FILTER (
            WHERE category = 'Authentication' 
            AND event IN ('Signup', 'Signup-with-google', 'Signup-with-Linkedin')
          ) as signups,
          COUNT(*) FILTER (
            WHERE category = 'Authentication' 
            AND (event = 'Verified account' OR event = 'EmailVerified')
          ) as email_verifications,
          COUNT(*) FILTER (
            WHERE category = 'Onboarding'
            AND event = 'OnboardingComplete'
          ) as onboarding_completes,
          COUNT(*) FILTER (
            WHERE category = 'API'
            AND event = 'FirstApiCall'
          ) as first_api_calls
        FROM analytics_logger
        WHERE date >= CURRENT_DATE - INTERVAL '${timeframe}'
      )
      SELECT
        signups,
        email_verifications,
        onboarding_completes,
        first_api_calls,
        ROUND((email_verifications::numeric / NULLIF(signups, 0)) * 100, 2) as email_verification_rate,
        ROUND((onboarding_completes::numeric / NULLIF(email_verifications, 0)) * 100, 2) as onboarding_completion_rate,
        ROUND((first_api_calls::numeric / NULLIF(onboarding_completes, 0)) * 100, 2) as api_conversion_rate
      FROM funnel_steps;
    `;

    try {
      const result = await pool.query(query);
      return result.rows[0];
    } catch (error) {
      console.error("Error getting onboarding funnel metrics:", error);
      throw error;
    }
  }
}
