import { FC, Fragment } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faAngleRight } from '@fortawesome/free-solid-svg-icons';
import labelA from '../../imgs/labels/fill_A.svg';
import labelB from '../../imgs/labels/fill_B.svg';
import labelC from '../../imgs/labels/fill_C.svg';
import labelD from '../../imgs/labels/fill_D.svg';
import labelE from '../../imgs/labels/fill_E.svg';
import labelF from '../../imgs/labels/fill_F.svg';
import labelG from '../../imgs/labels/fill_G.svg';

interface InitialOverviewBoxProps {
  title: string;
  value?: string;
  toUpper?: boolean;
  label?: boolean;
  current?: string;
  potential?: string;
  labelNum?: boolean;
}

const InitialOverviewBox: FC<InitialOverviewBoxProps> = ({
  title,
  value,
  toUpper,
  label,
  current,
  potential,
  labelNum,
}) => {
  const titleCase = (str: string): string => str
    .toLowerCase()
    .split(' ')
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');

  const createLabel = (val: string): string | undefined => {
    switch (val) {
      case 'A++++':
      case 'A+++':
      case 'A++':
      case 'A+':
      case 'A':
        return labelA;
      case 'B':
        return labelB;
      case 'C':
        return labelC;
      case 'D':
        return labelD;
      case 'E':
        return labelE;
      case 'F':
        return labelF;
      case 'G':
        return labelG;
      default:
        return undefined;
    }
  };

  const alignLabelTextInImage = (label: string): string | undefined => {
    switch (label) {
      case 'A++':
        return 'labelAA';
      case 'A+++':
        return 'labelAAA';
      case 'A++++':
        return 'labelAAAA';
      default:
    }
  };

  return (
    <div className="initial-overview">
      <p className="overview-title">
        {title}
        :

      </p>
      {toUpper ? (
        <p className="overview-info">{value && titleCase(value)}</p>
      ) : (
        <p className="overview-info">
          {label ? (
            <>
              <span className="standardLabel current">
                <img
                  src={createLabel(current || '')}
                  alt="current energy label"
                />
                <span
                  className={`standardLabel__letter ${alignLabelTextInImage(
									  current || '',
              )}`}
                >
                  {current}
                </span>
              </span>

              <FontAwesomeIcon icon={faAngleRight} />

              <span className="standardLabel potential">
                <img
                  src={createLabel(potential || '')}
                  alt="potential energy label"
                />
                <span
                  className={`standardLabel__letter ${alignLabelTextInImage(
									  potential || '',
              )}`}
                >
                  {potential}
                </span>
              </span>
            </>
          ) : (
            <>
              {labelNum ? (
                <>
              <span>{current}</span>
              <FontAwesomeIcon icon={faAngleRight} />
              <span>{potential}</span>
            </>
              ) : (
							  value
              )}
            </>
          )}
        </p>
      )}
    </div>
  );
};

export default InitialOverviewBox;
