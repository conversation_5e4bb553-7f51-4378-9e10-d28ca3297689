import { User } from "../@types";
import pool from "../db";
import AppError from "../utils/appError";
import {
  generateTOTPSecret,
  generateQRCode,
  enable2FA,
  disable2FA,
  verify2FACode,
  send2FACode,
} from "../utils/twoFactorAuth";

export class TwoFactorAuthService {
  private readonly MAX_FAILED_ATTEMPTS = 5;
  private readonly LOCKOUT_DURATION = 15 * 60 * 1000; // 15 minutes in milliseconds

  /**
   * Enable 2FA for a user with either email or authenticator app
   */
  async enableTwoFactor(
    userId: string,
    type: "email" | "authenticator",
  ): Promise<{ qrCode?: string; secret?: string }> {
    try {
      const user = await pool.query<User>(
        "SELECT * FROM users WHERE user_id = $1",
        [userId],
      );

      if (user.rows.length === 0) {
        throw new AppError("User not found", 404, true);
      }

      if (type === "authenticator") {
        // Generate TOTP secret and QR code for authenticator setup
        const secret = await generateTOTPSecret(user.rows[0].email);

        const qrCodeUrl = await generateQRCode(secret.otpauth_url);

        // Enable 2FA with authenticator
        await enable2FA(userId, "authenticator", secret.base32);

        return {
          qrCode: qrCodeUrl,
          secret: secret.base32,
        };
      } else {
        // Enable email-based 2FA
        await enable2FA(userId, "email");

        return {};
      }
    } catch (error: any) {
      throw error instanceof AppError
        ? error
        : new AppError(error.message || "Error enabling 2FA", 500, false);
    }
  }

  /**
   * Disable 2FA for a user (requires password verification)
   */
  async disableTwoFactor(userId: string, password: string): Promise<void> {
    try {
      const user = await pool.query<User>(
        "SELECT * FROM users WHERE user_id = $1",
        [userId],
      );

      if (user.rows.length === 0) {
        throw new AppError("User not found", 404, true);
      }

      // Verify password before disabling 2FA
      const bcrypt = require("bcryptjs");
      const isValidPassword = await bcrypt.compare(
        password,
        user.rows[0].password,
      );
      if (!isValidPassword) {
        throw new AppError("Invalid password", 401, true);
      }

      await disable2FA(userId);
    } catch (error: any) {
      console.error("Error in disableTwoFactor:", error);
      throw error instanceof AppError
        ? error
        : new AppError(error.message || "Error disabling 2FA", 500, false);
    }
  }

  /**
   * Reset failed attempts counter
   */
  private async resetFailedAttempts(userId: string): Promise<void> {
    try {
      await pool.query(
        "UPDATE users SET two_factor_failed_attempts = 0, two_factor_lockout_until = NULL WHERE user_id = $1",
        [userId],
      );
    } catch (error: any) {
      console.error("Error resetting failed attempts:", error);
      throw error;
    }
  }

  /**
   * Increment failed attempts and implement lockout if necessary
   */
  private async handleFailedAttempt(userId: string): Promise<void> {
    try {
      const result = await pool.query(
        `UPDATE users 
         SET two_factor_failed_attempts = COALESCE(two_factor_failed_attempts, 0) + 1,
             two_factor_lockout_until = CASE 
               WHEN COALESCE(two_factor_failed_attempts, 0) + 1 >= $1 
               THEN NOW() + INTERVAL '15 minutes'
               ELSE two_factor_lockout_until
             END
         WHERE user_id = $2
         RETURNING two_factor_failed_attempts, two_factor_lockout_until`,
        [this.MAX_FAILED_ATTEMPTS, userId],
      );

      if (
        result.rows[0].two_factor_failed_attempts >= this.MAX_FAILED_ATTEMPTS
      ) {
        throw new AppError(
          "Account temporarily locked due to too many failed attempts. Please try again later.",
          429,
          true,
        );
      }
    } catch (error: any) {
      throw error instanceof AppError
        ? error
        : new AppError(error.message, 500, false);
    }
  }

  /**
   * Check if account is locked
   */
  private async checkLockout(userId: string): Promise<void> {
    try {
      const user = await pool.query<User>(
        "SELECT * FROM users WHERE user_id = $1",
        [userId],
      );

      if (user.rows.length === 0) {
        throw new AppError("User not found", 404, true);
      }

      const lockoutUntil = user.rows[0].two_factor_lockout_until;
      if (lockoutUntil && new Date() < new Date(lockoutUntil)) {
        const remainingTime = Math.ceil(
          (new Date(lockoutUntil).getTime() - new Date().getTime()) / 1000 / 60,
        );
        throw new AppError(
          `Account is locked. Please try again in ${remainingTime} minutes.`,
          429,
          true,
        );
      }
    } catch (error: any) {
      console.error("Error checking lockout:", error);
      throw error instanceof AppError
        ? error
        : new AppError(error.message, 500, false);
    }
  }

  /**
   * Verify 2FA code during login
   */
  async verifyTwoFactorLogin(userId: string, code: string): Promise<User> {
    try {
      // Check if account is locked
      await this.checkLockout(userId);

      const user = await pool.query<User>(
        "SELECT * FROM users WHERE user_id = $1",
        [userId],
      );

      if (user.rows.length === 0) {
        throw new AppError("User not found", 404, true);
      }

      // For email-based 2FA, check if password was changed after code generation
      if (user.rows[0].two_factor_type === "email") {
        const lastPasswordChange = user.rows[0].last_password_change;
        if (lastPasswordChange && user.rows[0].otp_expiration) {
          const passwordChangeTime = new Date(lastPasswordChange);
          const codeGenerationTime = new Date(user.rows[0].otp_expiration);

          if (passwordChangeTime > codeGenerationTime) {
            throw new AppError(
              "Password has been changed. Please request a new code.",
              400,
              true,
            );
          }
        }
      }

      const isValid = await verify2FACode(
        userId,
        code,
        user.rows[0].two_factor_type!,
      );

      if (!isValid) {
        throw new AppError("Invalid 2FA code", 401, true);
      }

      // Reset failed attempts on successful verification
      await this.resetFailedAttempts(userId);

      return user.rows[0];
    } catch (error: any) {
      if (error.message === "Invalid 2FA code") {
        await this.handleFailedAttempt(userId);
      }
      throw error instanceof AppError
        ? error
        : new AppError(error.message || "Error verifying 2FA code", 500, false);
    }
  }

  /**
   * Request a new 2FA code (for email-based 2FA)
   */
  async requestNewCode(userId: string): Promise<void> {
    try {
      const user = await pool.query<User>(
        "SELECT * FROM users WHERE user_id = $1",
        [userId],
      );

      if (user.rows.length === 0) {
        throw new AppError("User not found", 404, true);
      }

      if (user.rows[0].two_factor_type !== "email") {
        throw new AppError("Invalid 2FA type", 400, true);
      }

      // Reset the failed attempts when requesting a new code
      await pool.query(
        "UPDATE users SET two_factor_failed_attempts = 0, two_factor_lockout_until = NULL WHERE user_id = $1",
        [userId],
      );

      await send2FACode(user.rows[0]);
    } catch (error: any) {
      console.error("Error in requestNewCode:", error);
      throw error instanceof AppError
        ? error
        : new AppError(
            error.message || "Error requesting new code",
            500,
            false,
          );
    }
  }
}

export const twoFactorAuthService = new TwoFactorAuthService();
