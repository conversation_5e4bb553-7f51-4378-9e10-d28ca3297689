/**
 * Converts a date string from YYYY-MM-DD format to YYYYMMDD format
 * @param dateString Date string in YYYY-MM-DD format
 * @returns Formatted date string in YYYYMMDD format
 */
export const formatDateToYYYYMMDD = (dateString: string): string => {
  if (!dateString) return "";
  return dateString.replace(/-/g, "");
};

/**
 * Validates if a date string is in YYYYMMDD format
 * @param dateString Date string to validate
 * @returns boolean indicating if the format is valid
 */
export const isValidYYYYMMDD = (dateString: string): boolean => {
  return /^\d{8}$/.test(dateString);
};
