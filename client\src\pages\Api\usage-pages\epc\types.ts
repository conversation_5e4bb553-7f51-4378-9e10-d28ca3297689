export interface EPCResult {
  post_code: string;
  house_number: string;
  house_addition: string | null;
  build_year: number;
  inner_surface_area: number;
  house_type: string;
  installation: number;
  wall_insulation: number;
  roof_insulation: number;
  floor_insulation: number;
  living_room_windows: number;
  bedroom_windows: number;
  shower: number;
  ventilation: number;
  solar_panels: number;
  solarpanel_watt_peak: number;
  CO2: number;
  definitive_energy_label: string;
  definitive_energy_label_type: string;
  definitive_energy_label_validity_date: string;
  definitive_BENG2_score: string;
  current_estimated_energy_label: string;
  current_estimated_BENG1_score: number;
  current_estimated_BENG2_score: number;
}

export interface EPCRawData {
  Adres: string;
  Class: string;
  Bouwjaar: string;
  Compactheid: string;
  Vloeroppervlakte: string;
  Woningtype: string;
  Verwarming: string;
  "Warm water": string;
  Zonneboiler: string;
  Ventilatie: string;
  Koeling: string;
  Zonnepanelen: string;
  Energy: string;
  "CO2 per m2": string;
  Warmtebehoefte: string;
  Isolatie: {
    Gevels: Record<string, Array<{ Opp: string; Rc: string }>>;
    Gevelpanelen: Record<string, any>;
    Daken: Record<string, Array<{ Opp: string; Rc: string }>>;
    Vloeren: Record<string, Array<{ Opp: string; Rc: string }>>;
    Ramen: Record<string, Array<{ Opp: string; Uw: string }>>;
    Buitendeuren: Record<string, Array<{ Opp: string; Ud: string }>>;
  };
}

export interface EPCApiResponse {
  Output: EPCResult;
  raw_data: EPCRawData;
}

export interface PDFOptions {
  file: File | undefined;
  preview: string | undefined;
}
