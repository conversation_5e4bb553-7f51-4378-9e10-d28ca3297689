import React, { useState, forwardRef } from "react";
import FilteredSearch from "./FilteredSearch";
import Api from "../../../../components/ApiCard/Api";
import Subtitle from "../../../../components/Subtitle";
import { Link } from "react-router-dom";
import Text from "../../../../components/Text";

interface Props {
  apiPreferenceKeywords?: string[];
}

const RecommendedAPI = forwardRef<HTMLDivElement, Props>((props, ref) => {
  const [search, setSearch] = useState("");
  const [selected, setSelected] = useState("Alle producten");
  return (
    <div className="w-full flex flex-col items-start gap-4 shadow-[0px_0px_4px_0px_#00000026] rounded-lg p-4 md:h-[500px] lg:w-96 lg:h-[865px]">
      <div className="flex justify-between w-full">
        <Subtitle className="text-base">Aanbevolen API's</Subtitle>
        <Link to="/dashboard/products">
          <Text className="text-primary hover:underline">bekijk alle</Text>
        </Link>
      </div>

      {/* <FilteredSearch
        selected={selected}
        setSelected={setSelected}
        setSearch={setSearch}
      /> */}
      <div
        ref={ref}
        className="overflow-y-auto pr-4 flex flex-col gap-3"
        id="api-cards-container"
      >
        <Api
          activeCategory={selected}
          search={search}
          apiPreferenceKeywords={props.apiPreferenceKeywords}
        />
      </div>

      {/* Hidden button for tour targeting */}
      <button id="api-start-button" className="hidden">
        Starten
      </button>
    </div>
  );
});

export default RecommendedAPI;
