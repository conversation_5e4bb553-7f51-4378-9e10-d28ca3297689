import { Request, Response, NextFunction } from "express";
import { User } from "../@types";
import AppError from "../utils/appError";

export const restrictTo = (...roles: string[]) => {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new AppError("You are not logged in", 401));
    }

    const user = req.user as User;
    if (!roles.includes(user.role)) {
      return next(
        new AppError("You do not have permission to perform this action", 403),
      );
    }

    next();
  };
};
