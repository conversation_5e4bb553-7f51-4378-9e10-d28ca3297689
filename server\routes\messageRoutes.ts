import express from "express";
import { allowAdminOnly, protect } from "../controllers/authController";
import {
  createGeneralMessage,
  deleteAllMessages,
  deleteMessageById,
  deleteMessagesFromUserList,
  getAllMessages,
  getMessageById,
  getMessagesForUser,
  markAllAsReadForUser,
  setMessageToOpened,
} from "../controllers/messageController";

const router = express.Router();

/** Create message (Admin access only) */
router.post("/general-message", protect, allowAdminOnly, createGeneralMessage);

/** Get all messages created (Admin access only) */
router.get("/", protect, allowAdminOnly, getAllMessages);

/** Delete all messages (Admin access only) */
router.delete("/", protect, allowAdminOnly, deleteAllMessages);

/** Delete(by patch) array of messages (Admin access only) */
router.patch("/", protect, allowAdminOnly, deleteMessageById);

/** Get message by id */
router.get("/:id", protect, getMessageById);

/** Get all messages of user */
router.get("/users/:userId", protect, getMessagesForUser);

/** Mark message of user as read/opened */
router.patch("/:messageId/:userId", protect, setMessageToOpened);

/** Mark all messages of user as read/opened */
router.patch("/mark-all-read/users/:userId", protect, markAllAsReadForUser);

/** Delete(by patch) array of user messages */
router.patch("/delete-user-messages", protect, deleteMessagesFromUserList);

export default router;
