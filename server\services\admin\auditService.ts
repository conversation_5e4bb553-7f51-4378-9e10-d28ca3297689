import pool from "../../db";
import { User } from "../../@types";

export interface AuditLog {
  id: string;
  action: string;
  entity_type: string;
  entity_id: string;
  changes: Record<string, any>;
  performed_by: string;
  created_at: Date;
}

export class AuditService {
  /**
   * Logs an admin action
   */
  static async logAction(
    action: string,
    entityType: string,
    entityId: string,
    changes: Record<string, any>,
    performedBy: string,
  ): Promise<void> {
    await pool.query(
      `INSERT INTO audit_logs (
        action, entity_type, entity_id, changes, performed_by
      ) VALUES ($1, $2, $3, $4, $5)`,
      [action, entityType, entityId, JSON.stringify(changes), performedBy],
    );
  }

  /**
   * Gets audit logs for a specific entity
   */
  static async getEntityAuditLogs(
    entityType: string,
    entityId: string,
  ): Promise<AuditLog[]> {
    const result = await pool.query<AuditLog>(
      `SELECT * FROM audit_logs 
       WHERE entity_type = $1 AND entity_id = $2
       ORDER BY created_at DESC`,
      [entityType, entityId],
    );
    return result.rows;
  }

  /**
   * Gets all admin action logs with pagination
   */
  static async getAdminAuditLogs(
    page: number = 1,
    limit: number = 20,
  ): Promise<{ logs: AuditLog[]; total: number }> {
    const offset = (page - 1) * limit;

    const [logsResult, countResult] = await Promise.all([
      pool.query<AuditLog>(
        `SELECT * FROM audit_logs 
         ORDER BY created_at DESC
         LIMIT $1 OFFSET $2`,
        [limit, offset],
      ),
      pool.query<{ count: string }>("SELECT COUNT(*) FROM audit_logs"),
    ]);

    return {
      logs: logsResult.rows,
      total: parseInt(countResult.rows[0].count),
    };
  }
}
