# AVM API Enhancement Plan

## Overview
This document details the enhancements based on QA feedback for the Woningwaarde API.

## QA Feedback Summary
- **Afbeelding**: Remove the field and apply a default value of 1.
- **Energielabel**: Change the label to "Huidig energielabel".
- **<PERSON><PERSON><PERSON><PERSON> (m2)**: Change the label to "<PERSON><PERSON><PERSON><PERSON>vlak<PERSON> (m2)" and set a default/placeholder value of 150 (default gray value).
- **Woningtype**: Convert from a text input to a dropdown list with allowed options.
- **Betrouwbaarheid Result**: Adjust result page: remove outline text and format as "90% interval € 645.759 tot € 789.262" ensuring proper EURO notation.

## Detailed Plan

### 1. Form Adjustments (data.ts)
- **Afbeelding Field**: Remove this field from the form configuration and set a default value of 1 elsewhere in the API handling logic.
- **Energielabel Field**: Update label to "Huidig energielabel" (currently at line 63).
- **Oppervlak Field**: Update label from "Oppervlak (m2)" to "<PERSON><PERSON><PERSON><PERSON><PERSON>lak<PERSON> (m2)". Also add a default / placeholder value of 150.
- **Woningtype Field**: Change the input type from text to a dropdown, using an allowed list such as:
  - Vrijstaande woning
  - Tussenwoning
  - Appartement
  - Rijtjeswoning
  (Confirm allowed list if needed)

### 2. Result Page Adjustments
- **Betrouwbaarheid Output**: Locate component (likely in AVMResult.tsx) that shows "Betrouwbaarheid" and update it to display:
  "90% interval € 645.759 tot € 789.262"
  instead of the current text.
- Verify and adjust the EURO notation for consistency.

## Workflow Diagram

```mermaid
flowchart TD
    A[Review QA Feedback]
    B[Update form configuration in data.ts]
    B1[Remove Afbeelding field (set default image = 1)]
    B2[Change Energielabel label → "Huidig energielabel"]
    B3[Change Oppervlak label → "Perceeloppervlakte (m²)" + add default value 150]
    B4[Convert Woningtype input to dropdown with allowed options]
    C[Update Result Page]
    C1[Locate Betrouwbaarheid output in AVMResult.tsx]
    C2[Modify output to show "90% interval € 645.759 tot € 789.262"]
    D[Verify EURO notation]
    
    A --> B
    B --> B1
    B --> B2
    B --> B3
    B --> B4
    A --> C
    C --> C1
    C --> C2
    C2 --> D
```

## Next Steps
- Implement the changes identified above.
- Once implemented, review the changes for quality assurance.

---

End of Plan.