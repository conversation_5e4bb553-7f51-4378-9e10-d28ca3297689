import axios from "axios";
import { SendGridContact, User } from "../@types";

export const updateContact = async (user: User, listId: string) => {
  const API_KEY = `${process.env.SENDGRID_APIKEY}`;
  const URL = "https://api.sendgrid.com/v3/marketing/contacts";
  const config = {
    headers: {
      authorization: `Bearer ${API_KEY}`,
      "content-type": "application/json",
    },
  };
  const body = {
    list_ids: [`${listId}`],
    contacts: [
      {
        email: user.email,
      },
    ],
  };
  try {
    await axios.put(URL, body, config);
  } catch (err: any) {
    console.log(err);
    return err?.response;
  }
};

export const getContactByEmail = async (
  user: User,
): Promise<SendGridContact> => {
  const API_KEY = `${process.env.SENDGRID_APIKEY}`;
  const URL = "https://api.sendgrid.com/v3/marketing/contacts/search/emails";
  const config = {
    headers: {
      Authorization: `Bearer ${API_KEY}`,
      "content-type": "application/json",
    },
  };
  const body = {
    emails: [user.email],
  };

  try {
    const res = await axios.post(URL, body, config);
    return res.data.result[user.email].contact;
  } catch (err: any) {
    console.log(err);
    return err.response;
  }
};

export const isInList = (contact: SendGridContact, listId: string): boolean => {
  if (contact.status >= 400) {
    return false;
  }
  return contact.list_ids.includes(listId);
};

export const deleteContact = async (user: User) => {
  const API_KEY = `${process.env.SENDGRID_APIKEY}`;
  const URL = "https://api.sendgrid.com/v3/marketing/contacts";
  const config = {
    headers: {
      authorization: `Bearer ${API_KEY}`,
      "content-type": "application/json",
    },
  };
  try {
    const contact = await getContactByEmail(user);
    if (contact && contact.id) {
      await axios.delete(URL, {
        headers: config.headers,
        data: { ids: [contact.id] },
      });
    }
  } catch (err: any) {
    console.log(err);
    return err?.response;
  }
};
