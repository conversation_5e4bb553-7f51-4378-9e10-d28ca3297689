import pool from "../db";
import bcrypt from "bcryptjs";
import { v4 as uuidv4 } from "uuid";

async function createTestUsers() {
  try {
    // Hash password
    const salt = await bcrypt.genSalt(10);
    const password = await bcrypt.hash("test123", salt);

    // Create multiple test users
    for (let i = 1; i <= 25; i++) {
      await pool.query(
        `INSERT INTO users (
          user_id,
          email,
          password,
          role,
          active,
          first_name,
          last_name,
          created_at,
          api_key,
          api_key_id,
          current_usage_plan,
          company,
          kvk,
          stripe_customer_id
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)`,
        [
          uuidv4(),
          `test${i}@example.com`,
          password,
          "user",
          true,
          `Test${i}`,
          "User",
          new Date(
            Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000,
          ), // Random date within last 30 days
          `test-api-key-${i}`,
          `test-api-key-id-${i}`,
          "test-usage-plan",
          `Company ${i}`,
          `${********* + i}`,
          "cus_test_" + uuidv4(),
        ],
      );
    }

    console.log("Created 25 test users");
    process.exit(0);
  } catch (error) {
    console.error("Error:", error);
    process.exit(1);
  }
}

createTestUsers();
