export function removeExtraSpaces(inputStr: string): string {
  let cleanedStr = inputStr.replace(/<\/\w+>\s+(?=[^<])/g, (match) =>
    match.trim(),
  );

  // Add a space before opening tags if not already present and not preceded by a comma
  cleanedStr = cleanedStr.replace(/(?<=\S)(?<!,)</g, " <");

  // Remove spaces between tags
  cleanedStr = cleanedStr.replace(/>\s+</g, "><");

  // Replace multiple spaces with a single space between words
  cleanedStr = cleanedStr.replace(/\s+/g, " ");

  // Remove space before a comma
  cleanedStr = cleanedStr.replace(/ \,/g, ",");

  // Trim leading and trailing spaces
  cleanedStr = cleanedStr.trim();

  return cleanedStr;
}
