import React, { useState } from "react";
import { FormProvider } from "../../components/FormContext";
import EnergyLabelContainer from "../components/EnergyLabelContainer";
import EnergyLabelForm from "./EnergyLabelForm";

const Index = () => {
  const [page, setPage] = useState(1);

  return (
    <FormProvider>
      <EnergyLabelContainer page={page}>
        <EnergyLabelForm page={page} setPage={setPage} />
      </EnergyLabelContainer>
    </FormProvider>
  );
};

export default Index;
