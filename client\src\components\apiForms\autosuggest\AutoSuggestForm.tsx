import React, { FormEvent, useEffect, useState } from "react";
import FormBody from "../FormBody";
import { autosuggest } from "../../../assets/images/api/APIimages";
import { FormOptions, FormOptionsSustainability } from "../../../@types";
import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { autoSuggestField } from "./autoSuggestField";
import { postAutosuggest } from "../../../redux/actions/autosuggestAction";
import { useFormContext } from "../../../pages/Api/usage-pages/components/FormContext";

const AutoSuggestForm = () => {
  const sectionFields = [{ title: "Zoekopdracht", startIndex: 0, endIndex: 3 }];
  const apiKey = useAppSelector((state) => state.auth.user?.api_key);
  const { formValues, setFormValues } = useFormContext();
  const { savedQueries } = useAppSelector((state) => state.objectData);

  useEffect(() => {
    if (Object.keys(savedQueries).length > 0) {
      setFormValues({
        ...(savedQueries as FormOptions),
      });
    }
  }, [savedQueries, setFormValues]);

  const dispatch = useAppDispatch();

  const handleFormSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    apiKey &&
      dispatch(postAutosuggest({ formData: formValues, apiKey: apiKey }));
  };

  return (
    <div>
      <FormBody
        title={"Autosuggestie API"}
        sectionFields={sectionFields}
        desc={
          "Vraag de Autosuggestie API voor een Nederlandse woning aan om een automatische suggestie te krijgen."
        }
        img={autosuggest}
        path={"https://docs.altum.ai/apis/zonnepanelen-dakenscan-api"}
        initialFields={autoSuggestField}
        handleSubmit={handleFormSubmit}
      />
    </div>
  );
};

export default AutoSuggestForm;
