# AVMplus API Enhancement Plan

## Overview
This document outlines enhancement for the Woningwaarde+ API based on QA feedback.

## QA Feedback Summary
- Change Energielabel to "Huidig energielabel".
- Change <PERSON><PERSON><PERSON><PERSON> to "Perceeloppervlakte (m2)" with default value 150.
- Remove BAG ID display.
- Change "Bouwjaar" label to "Waarderingsdatum" and show valuation_date.
- Update Confidence Interval display to "90% interval € X tot € Y".

## Implementation Steps
1. Update `client/src/pages/Api/usage-pages/avmplus/data.ts` for correct labels and defaults.
2. Update `client/src/pages/Api/usage-pages/avmplus/result/AVMResult.tsx` to:
   - Remove BAG ID.
   - Change "Bouwjaar" to "Waarderingsdatum" and display valuation_date.
   - Update confidence interval format as per instructions.
3. Test and verify results.

## Diagram

```mermaid
flowchart TD
    A[Review QA Feedback]
    B[Update data.ts]
    B1[Change Energielabel to "Huidig energielabel"]
    B2[Change Oppervlak to "Perceeloppervlakte (m2)" with default 150]
    C[Update result page (AVMResult.tsx)]
    C1[Remove BAG ID]
    C2[Change "Bouwjaar" to "Waarderingsdatum"]
    C3[Update Confidence Interval display]
    A --> B
    B --> B1
    B --> B2
    A --> C
    C --> C1
    C --> C2
    C --> C3
```

## Next Steps
Implement changes in respective files and verify with QA feedback.