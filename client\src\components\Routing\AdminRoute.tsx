import React, { <PERSON> } from "react";
import { Route, Redirect, RouteProps } from "react-router-dom";
import { useAppSelector } from "../../redux/hooks";

interface AdminRouteProps extends RouteProps {
	component: React.ComponentType<any>;
}

const AdminRoute: FC<AdminRouteProps> = ({ component: Component, ...rest }) => {
	const { user, isAuthenticated } = useAppSelector((state) => state.auth);
	return (
		<Route
			{...rest}
			render={(props) =>
				isAuthenticated && user?.role === "admin" ? (
					<Component {...props} />
				) : (
					<Redirect to="/" />
				)
			}
		/>
	);
};

export default AdminRoute;
