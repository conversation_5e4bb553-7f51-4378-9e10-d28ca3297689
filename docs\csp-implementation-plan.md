# CSP Implementation Plan

This document outlines the plan for ensuring our Content Security Policy (CSP) explicitly defines all resource directives to prevent unintentional fallback to an "allow-all" state.

## Overview

The current evidence shows a CSP header like:

```
default-src 'self'; 
script-src 'self' 'unsafe-inline' https://www.googletagmanager.com https://js.stripe.com https://www.google-analytics.com; 
style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; 
img-src 'self' data: https://mopsus-test.altum.ai; 
connect-src 'self' https://api.mopsus.altum.ai https://api.stripe.com https://www.google-analytics.com; 
font-src 'self' https://fonts.gstatic.com data:; 
object-src 'none'; 
media-src 'self'; 
frame-src 'self' https://js.stripe.com
```

While this configuration explicitly defines several directives, it is important to list **all** the resource types we wish to control. Any directives that are omitted or not falling back to `default-src` may allow unintentional resource loading from uncontrolled sources.

## Directives to Explicitly Define

To prevent unintentional wildcard behavior, we must include explicit settings for resource types that are otherwise not covered by `default-src`. Below is a list of common directives along with recommended values:

1. **default-src**  
   - Recommended: `default-src 'self';`

2. **script-src**  
   - Recommended: `script-src 'self' 'unsafe-inline' https://www.googletagmanager.com https://js.stripe.com https://www.google-analytics.com;`

3. **style-src**  
   - Recommended: `style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;`

4. **img-src**  
   - Recommended: `img-src 'self' data: https://mopsus-test.altum.ai;`

5. **connect-src**  
   - Recommended: `connect-src 'self' https://api.mopsus.altum.ai https://api.stripe.com https://www.google-analytics.com;`

6. **font-src**  
   - Recommended: `font-src 'self' https://fonts.gstatic.com data:;`

7. **object-src**  
   - Recommended: `object-src 'none';`

8. **media-src**  
   - Recommended: `media-src 'self';`

9. **frame-src**  
   - Recommended: `frame-src 'self' https://js.stripe.com;`

10. **worker-src** (for Web Workers)  
    - Recommended: `worker-src 'self';`  
    - *Note: This directive is not included in the evidence; add it explicitly if workers are used.*

11. **manifest-src**  
    - Recommended: `manifest-src 'self';`

12. **form-action**  
    - Recommended: `form-action 'self';`

13. **base-uri**  
    - Recommended: `base-uri 'self';`

14. **frame-ancestors**  
    - Recommended: `frame-ancestors 'none';`

15. **upgrade-insecure-requests**  
    - Recommended: `upgrade-insecure-requests;`

## Implementation Approach

### Step 1: Review Current CSP Configuration

- Identify current locations where CSP headers are set:
  - **Express/Node.js Server Configuration**: Check middleware and helmet configurations.
  - **Nginx Configuration**: CSP set via `add_header` directive.
  - **Meta Tag in HTML**: Located in `client/public/index.html`.

### Step 2: Update CSP Header in Server and Proxy Configurations

- **Node.js/Express**:
  - Use the Helmet middleware or set headers manually to include all directives.
  - Example:
    ```javascript
    app.use((req, res, next) => {
      res.setHeader("Content-Security-Policy", 
        "default-src 'self'; " +
        "script-src 'self' 'unsafe-inline' https://www.googletagmanager.com https://js.stripe.com https://www.google-analytics.com; " +
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; " +
        "img-src 'self' data: https://mopsus-test.altum.ai; " +
        "connect-src 'self' https://api.mopsus.altum.ai https://api.stripe.com https://www.google-analytics.com; " +
        "font-src 'self' https://fonts.gstatic.com data:; " +
        "object-src 'none'; " +
        "media-src 'self'; " +
        "frame-src 'self' https://js.stripe.com; " +
        "worker-src 'self'; " +
        "manifest-src 'self'; " +
        "form-action 'self'; " +
        "base-uri 'self'; " +
        "frame-ancestors 'none'; " +
        "upgrade-insecure-requests;"
      );
      next();
    });
    ```

- **Nginx**:
  - Modify your existing configuration to explicitly add all directives.
  - Example:
    ```nginx
    add_header Content-Security-Policy "
      default-src 'self';
      script-src 'self' 'unsafe-inline' https://www.googletagmanager.com https://js.stripe.com https://www.google-analytics.com;
      style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
      img-src 'self' data: https://mopsus-test.altum.ai;
      connect-src 'self' https://api.mopsus.altum.ai https://api.stripe.com https://www.google-analytics.com;
      font-src 'self' https://fonts.gstatic.com data:;
      object-src 'none';
      media-src 'self';
      frame-src 'self' https://js.stripe.com;
      worker-src 'self';
      manifest-src 'self';
      form-action 'self';
      base-uri 'self';
      frame-ancestors 'none';
      upgrade-insecure-requests;
    " always;
    ```

- **HTML Meta Tag** (if used):
  - Ensure that the meta tag in `client/public/index.html` reflects an updated CSP, or remove it in favor of server-managed headers:
    ```html
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' https://www.googletagmanager.com https://js.stripe.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: https://mopsus-test.altum.ai; connect-src 'self' https://api.mopsus.altum.ai https://api.stripe.com https://www.google-analytics.com; font-src 'self' https://fonts.gstatic.com data:; object-src 'none'; media-src 'self'; frame-src 'self' https://js.stripe.com; worker-src 'self'; manifest-src 'self'; form-action 'self'; base-uri 'self'; frame-ancestors 'none'; upgrade-insecure-requests;">
    ```

### Step 3: Test and Validate

- **Browser Console**: Use browser developer tools to inspect CSP headers.
- **Security Tools**: Re-run automated scans (e.g., OWASP ZAP) to ensure no “missing directive” findings.
- **Manual Testing**: Check that all resource types (scripts, images, workers, etc.) load only from allowed sources.

## Mermaid Diagram of the Implementation Flow

```mermaid
flowchart TD
    A[Start: Review Existing CSP Configurations]
    B[Identify Locations: Express, Nginx, HTML Meta Tag]
    C[Define All Required Directives Explicitly]
    D[Update Express Middleware with CSP Header]
    E[Update Nginx Configuration with add_header Directive]
    F[Optionally Update HTML Meta Tag]
    G[Test via Developer Tools and Security Scans]
    H[Validate that No Directive Falls Back to 'Allow All']
    
    A --> B
    B --> C
    C --> D
    C --> E
    C --> F
    D --> G
    E --> G
    F --> G
    G --> H
```

## Next Steps

1. **Review**: Please review this plan and let me know if it meets your requirements.
2. **Clarification on Next.js Migration**: Additionally, you mentioned moving the client to Next.js instead of React. Would you like a separate migration plan for Next.js, or would you prefer to integrate these CSP updates into a broader migration project?

Once you confirm or provide further specifications, we can write this plan to a markdown file (as above) and then proceed to switch modes to implement the solution.

Are you pleased with this plan or would you like to adjust anything?