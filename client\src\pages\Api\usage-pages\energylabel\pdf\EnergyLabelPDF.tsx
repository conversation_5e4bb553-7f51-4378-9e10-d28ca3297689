import React, { FC } from "react";
import { Document } from "@react-pdf/renderer";
import WelcomePage from "./WelcomePage";
import PropertyOverviewPage from "./PropertyOverviewPage";
import EnergyLabelPage from "./EnergyLabelPage";
import SustainabilityMeasuresPage from "./SustainabilityMeasuresPage";
import GlossaryPage from "./GlossaryPage";
import CoverPage from "./CoverPage";
import { EnergyLabel, EnergyLabelData, isValidEnergyLabel } from "../types";

interface PDFReportProps {
  data: EnergyLabelData;
  buildingPhoto: string;
  map: string;
}

const EnergyLabelPDF: FC<PDFReportProps> = ({ data, buildingPhoto, map }) => {
  // Validate energy labels
  if (!isValidEnergyLabel(data.current_estimated_energy_label)) {
    console.error(
      `Invalid current energy label: ${data.current_estimated_energy_label}`,
    );
  }
  if (!isValidEnergyLabel(data.definitive_energy_label)) {
    console.error(
      `Invalid definitive energy label: ${data.definitive_energy_label}`,
    );
  }

  const validatedData = {
    ...data,
    // Ensure energy labels are valid, fallback to 'G' if invalid
    current_estimated_energy_label: isValidEnergyLabel(
      data.current_estimated_energy_label,
    )
      ? data.current_estimated_energy_label
      : EnergyLabel.G,
    definitive_energy_label: isValidEnergyLabel(data.definitive_energy_label)
      ? data.definitive_energy_label
      : EnergyLabel.G,
  } as const;

  return (
    <Document>
      <CoverPage data={validatedData} />
      <WelcomePage />
      <PropertyOverviewPage
        data={validatedData}
        buildingPhoto={buildingPhoto}
        map={map}
      />
      <EnergyLabelPage data={validatedData} />
      <SustainabilityMeasuresPage data={validatedData} />
      <GlossaryPage />
    </Document>
  );
};

export default EnergyLabelPDF;
