export const getMapUrl = async (lon: number, lat: number): Promise<string> => {
  try {
    const response = await fetch(
      `https://maps.googleapis.com/maps/api/staticmap?key=YOUR_GOOGLE_MAPS_API_KEY&center=${lat},${lon}&markers=color:red%7Clabel:S%7C${lat},${lon}&zoom=14&size=800x200&language=nl`,
    );
    return response.url;
  } catch (error) {
    console.error("Error fetching map:", error);
    return "";
  }
};
