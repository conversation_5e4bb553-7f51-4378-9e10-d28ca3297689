import { config } from "dotenv";
import path from "path";
import fs from "fs";
import pool from "../db";
import { AnalyticsService } from "../services/admin/analyticsService";

config();

async function setupAnalytics() {
  try {
    console.log("Setting up analytics...");

    // Read and execute the analytics tables migration SQL
    const analyticsMigrationPath = path.join(
      __dirname,
      "../utils/migrations/update_analytics_tables.sql",
    );
    const analyticsLoggerMigrationPath = path.join(
      __dirname,
      "../utils/migrations/update_analytics_logger.sql",
    );

    // Execute analytics_events table migration
    console.log("Creating analytics_events table and functions...");
    const analyticsMigrationSQL = fs.readFileSync(
      analyticsMigrationPath,
      "utf8",
    );
    const analyticsStatements = analyticsMigrationSQL
      .split(";")
      .map((s) => s.trim())
      .filter((s) => s.length > 0);

    for (const statement of analyticsStatements) {
      await pool.query(statement);
    }

    // Execute analytics_logger updates
    console.log("Updating analytics_logger table...");
    const loggerMigrationSQL = fs.readFileSync(
      analyticsLoggerMigrationPath,
      "utf8",
    );
    const loggerStatements = loggerMigrationSQL
      .split(";")
      .map((s) => s.trim())
      .filter((s) => s.length > 0);

    for (const statement of loggerStatements) {
      await pool.query(statement);
    }

    // Migrate legacy analytics data
    console.log("Migrating legacy analytics data...");
    await pool.query("SELECT migrate_legacy_analytics()");

    // Backfill user-related events
    console.log("Backfilling user events...");
    await pool.query(`
      INSERT INTO analytics_events (event_type, user_id, timestamp)
      SELECT 
        'user_signup',
        user_id,
        created_at
      FROM users
      WHERE NOT EXISTS (
        SELECT 1 
        FROM analytics_events 
        WHERE event_type = 'user_signup' 
        AND analytics_events.user_id = users.user_id
      );
    `);

    await pool.query(`
      INSERT INTO analytics_events (event_type, user_id, timestamp)
      SELECT 
        'email_verified',
        user_id,
        COALESCE(email_verified_at, created_at)
      FROM users
      WHERE active = true
      AND NOT EXISTS (
        SELECT 1 
        FROM analytics_events 
        WHERE event_type = 'email_verified' 
        AND analytics_events.user_id = users.user_id
      );
    `);

    // Backfill onboarding data
    console.log("Backfilling onboarding events...");
    await pool.query(`
      WITH completed_onboarding AS (
        SELECT 
          user_id,
          MIN(created_at) as completion_time
        FROM user_onboarding_state
        WHERE is_complete = true
        GROUP BY user_id
        HAVING COUNT(*) >= 5
      )
      INSERT INTO analytics_events (event_type, user_id, timestamp)
      SELECT 
        'onboarding_complete',
        user_id,
        completion_time
      FROM completed_onboarding co
      WHERE NOT EXISTS (
        SELECT 1 
        FROM analytics_events 
        WHERE event_type = 'onboarding_complete' 
        AND analytics_events.user_id = co.user_id
      );
    `);

    // Backfill property generation data
    console.log("Backfilling property generation events...");
    await pool.query(`
      INSERT INTO analytics_events (event_type, user_id, timestamp)
      SELECT 
        'property_generation',
        user_id,
        created_at
      FROM chat_history
      WHERE NOT EXISTS (
        SELECT 1 
        FROM analytics_events 
        WHERE event_type = 'property_generation' 
        AND analytics_events.user_id = chat_history.user_id
        AND analytics_events.timestamp = chat_history.created_at
      );
    `);

    // Refresh materialized views
    console.log("Refreshing materialized views...");
    await pool.query(
      "REFRESH MATERIALIZED VIEW CONCURRENTLY analytics_events_daily;",
    );

    console.log("Analytics setup completed successfully");

    // Log some statistics
    const stats = await pool.query(`
      SELECT 
        COUNT(*) as total_events,
        COUNT(DISTINCT event_type) as unique_event_types,
        COUNT(DISTINCT user_id) as unique_users,
        MIN(timestamp) as earliest_event,
        MAX(timestamp) as latest_event
      FROM analytics_events;
    `);

    console.log("Analytics Statistics:");
    console.log(stats.rows[0]);
  } catch (error) {
    console.error("Error setting up analytics:", error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

setupAnalytics().catch(console.error);
