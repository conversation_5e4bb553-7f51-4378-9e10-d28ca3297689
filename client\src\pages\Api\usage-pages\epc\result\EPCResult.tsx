import { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "../../../../../redux/hooks";
import { useFormContext } from "../../components/FormContext";
import { Redirect, useHistory } from "react-router-dom";
import RedoButton from "../../components/RedoButtons";
import ResultSkeleton from "../../sustainability/components/ResultSkeleton";
import Result from "../components/Result";
import { ResultSummary } from "../components/ResultSummary";
import {
  clearEPCResults,
  modifyEPCQueries,
} from "../../../../../redux/actions/epcActions";
import { EPCResult as EPCResultType } from "../types";
import { ApiInitState } from "../../../../../helpers/createApiSlice";

const EPCResult = () => {
  const { result, loading } = useAppSelector(
    (state) => state.epc as ApiInitState<EPCResultType>,
  );
  const { buildingPhoto, map, setPostalAddress } = useFormContext();
  const history = useHistory();
  const dispatch = useAppDispatch();

  const clearResults = () => {
    dispatch(clearEPCResults());
    history.push("/epc");
  };

  const modifyResults = () => {
    dispatch(modifyEPCQueries());
    history.push("/epc");
  };

  useEffect(() => {
    if (Object.keys(result).length > 0) {
      setPostalAddress(
        `${result.post_code}-${result.house_number}-${
          result.house_addition || ""
        }`,
      );
    }
  }, [result, setPostalAddress]);

  if (Object.keys(result).length === 0 && !loading) {
    return <Redirect to="/epc" />;
  }

  return (
    <>
      {loading ? (
        <ResultSkeleton />
      ) : (
        <>
          <ResultSummary
            property={result}
            buildingPhoto={buildingPhoto}
            map={map}
          />
          <Result property={result} buildingPhoto={buildingPhoto} map={map} />
          <RedoButton modify={modifyResults} clear={clearResults} />
        </>
      )}
    </>
  );
};

export default EPCResult;
