#!/bin/bash

# Security Scanning Script for Mopsus Data Platform
# This script runs comprehensive security scans including SAST, dependency checks, and container scanning

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REPORT_DIR="security-reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
SCAN_RESULTS_FILE="$REPORT_DIR/security-scan-summary-$TIMESTAMP.txt"

# Create reports directory
mkdir -p $REPORT_DIR

echo -e "${BLUE}🔒 Starting Security Scan for Mopsus Data Platform${NC}"
echo "Timestamp: $(date)"
echo "Report Directory: $REPORT_DIR"
echo ""
echo -e "${YELLOW}📋 Note: Security reports are NOT committed to git for security reasons${NC}"
echo -e "${YELLOW}   See docs/security-reports-management.md for details${NC}"
echo "=========================================="

# Function to log results
log_result() {
    echo "$1" | tee -a "$SCAN_RESULTS_FILE"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Install required tools if not present
install_tools() {
    echo -e "${YELLOW}📦 Installing security scanning tools...${NC}"

    # Check if Docker is available for Semgrep
    if command_exists docker; then
        echo "✅ Docker found - will use Docker for Semgrep"
        # Pull Semgrep Docker image
        docker pull returntocorp/semgrep:latest || echo "⚠️  Could not pull latest Semgrep image, will try to use existing"
    else
        echo "⚠️  Docker not found - will try to install Semgrep locally"
        if ! command_exists semgrep; then
            echo "Installing Semgrep..."
            pip3 install semgrep || npm install -g semgrep
        fi
    fi

    # Install npm packages locally if not present globally
    if ! command_exists retire; then
        echo "Installing retire.js..."
        npm install -g retire || npx retire --version > /dev/null 2>&1 || npm install retire
    fi

    if ! command_exists audit-ci; then
        echo "Installing audit-ci..."
        npm install -g audit-ci || npx audit-ci --version > /dev/null 2>&1 || npm install audit-ci
    fi

    echo -e "${GREEN}✅ Tools installation completed${NC}"
}

# Dependency vulnerability scanning
scan_dependencies() {
    echo -e "${YELLOW}🔍 Scanning dependencies for vulnerabilities...${NC}"

    # Client dependencies
    echo "Scanning client dependencies..."
    cd client
    npm audit --audit-level=moderate > "../$REPORT_DIR/client-npm-audit-$TIMESTAMP.txt" 2>&1 || true

    # Try retire with different approaches
    if command_exists retire; then
        retire --outputformat json --outputpath "../$REPORT_DIR/client-retire-$TIMESTAMP.json" . || true
    else
        npx retire --outputformat json --outputpath "../$REPORT_DIR/client-retire-$TIMESTAMP.json" . || true
    fi

    # Try audit-ci with different approaches
    if command_exists audit-ci; then
        audit-ci --moderate --output-format json > "../$REPORT_DIR/client-audit-ci-$TIMESTAMP.json" || true
    else
        npx audit-ci --moderate --output-format json > "../$REPORT_DIR/client-audit-ci-$TIMESTAMP.json" || true
    fi
    cd ..

    # Server dependencies
    echo "Scanning server dependencies..."
    cd server
    npm audit --audit-level=moderate > "../$REPORT_DIR/server-npm-audit-$TIMESTAMP.txt" 2>&1 || true

    # Try retire with different approaches
    if command_exists retire; then
        retire --outputformat json --outputpath "../$REPORT_DIR/server-retire-$TIMESTAMP.json" . || true
    else
        npx retire --outputformat json --outputpath "../$REPORT_DIR/server-retire-$TIMESTAMP.json" . || true
    fi

    # Try audit-ci with different approaches
    if command_exists audit-ci; then
        audit-ci --moderate --output-format json > "../$REPORT_DIR/server-audit-ci-$TIMESTAMP.json" || true
    else
        npx audit-ci --moderate --output-format json > "../$REPORT_DIR/server-audit-ci-$TIMESTAMP.json" || true
    fi
    cd ..

    log_result "✅ Dependency vulnerability scanning completed"
}

# SAST scanning with Semgrep
scan_sast() {
    echo -e "${YELLOW}🔍 Running SAST scanning with Semgrep...${NC}"

    # Choose between Docker and local Semgrep
    if command_exists docker && docker images returntocorp/semgrep:latest --format "table" | grep -q "returntocorp/semgrep"; then
        echo "Using Docker for Semgrep scanning..."

        # Get current directory for Windows compatibility
        CURRENT_DIR=$(pwd)

        # Run Semgrep with custom config using Docker
        echo "Running custom Semgrep rules..."
        docker run --rm -v "$CURRENT_DIR:/src" returntocorp/semgrep:latest \
            semgrep --config=/src/.semgrep.yml --json --output=/src/"$REPORT_DIR/semgrep-custom-$TIMESTAMP.json" /src || true

        # Run Semgrep with auto config (includes OWASP Top 10) using Docker
        echo "Running Semgrep auto rules..."
        docker run --rm -v "$CURRENT_DIR:/src" returntocorp/semgrep:latest \
            semgrep --config=auto --json --output=/src/"$REPORT_DIR/semgrep-auto-$TIMESTAMP.json" /src || true

        # Run specific OWASP Top 10 rules using Docker
        echo "Running OWASP Top 10 rules..."
        docker run --rm -v "$CURRENT_DIR:/src" returntocorp/semgrep:latest \
            semgrep --config=p/owasp-top-ten --json --output=/src/"$REPORT_DIR/semgrep-owasp-$TIMESTAMP.json" /src || true

    elif command_exists semgrep; then
        echo "Using local Semgrep installation..."

        # Run Semgrep with custom config
        semgrep --config=.semgrep.yml --json --output="$REPORT_DIR/semgrep-custom-$TIMESTAMP.json" . || true

        # Run Semgrep with auto config (includes OWASP Top 10)
        semgrep --config=auto --json --output="$REPORT_DIR/semgrep-auto-$TIMESTAMP.json" . || true

        # Run specific OWASP Top 10 rules
        semgrep --config=p/owasp-top-ten --json --output="$REPORT_DIR/semgrep-owasp-$TIMESTAMP.json" . || true

    else
        echo "⚠️  Semgrep not available (neither Docker nor local installation found)"
        echo "Skipping SAST scanning..."
        log_result "⚠️  SAST scanning skipped (Semgrep not available)"
        return
    fi

    log_result "✅ SAST scanning completed"
}

# ESLint security scanning
scan_eslint() {
    echo -e "${YELLOW}🔍 Running ESLint security scanning...${NC}"

    # Client ESLint scan with security config
    echo "Scanning client code with security rules..."
    cd client
    npx eslint . --ext .ts,.tsx --config ../.eslintrc.security.js --format json --output-file "../$REPORT_DIR/eslint-client-security-$TIMESTAMP.json" || true
    cd ..

    # Server ESLint scan with server-specific config (avoids parser issues)
    echo "Scanning server code with basic security rules..."
    cd server
    npx eslint . --ext .ts --format json --output-file "../$REPORT_DIR/eslint-server-security-$TIMESTAMP.json" || true
    cd ..

    log_result "✅ ESLint security scanning completed"
}

# Container security scanning (if Docker is available)
scan_container() {
    if command_exists docker; then
        echo -e "${YELLOW}🔍 Running container security scanning...${NC}"
        
        # Build image for scanning
        docker build -t mopsus-security-scan -f Dockerfile . || true
        
        # Run Trivy if available
        if command_exists trivy; then
            trivy image --format json --output "$REPORT_DIR/trivy-$TIMESTAMP.json" mopsus-security-scan || true
        else
            echo "Trivy not available, skipping container vulnerability scan"
        fi
        
        log_result "✅ Container security scanning completed"
    else
        echo "Docker not available, skipping container security scan"
        log_result "⚠️  Container security scanning skipped (Docker not available)"
    fi
}

# Generate summary report
generate_summary() {
    echo -e "${YELLOW}📊 Generating security scan summary...${NC}"
    
    {
        echo "=========================================="
        echo "SECURITY SCAN SUMMARY"
        echo "=========================================="
        echo "Scan Date: $(date)"
        echo "Project: Mopsus Data Platform"
        echo ""
        echo "SCANS PERFORMED:"
        echo "- Dependency vulnerability scanning (npm audit, retire.js, audit-ci)"
        echo "- SAST scanning (Semgrep with OWASP Top 10 rules)"
        echo "- Code quality and security (ESLint with security plugins)"
        if command_exists docker; then
            echo "- Container security scanning (Trivy)"
        fi
        echo ""
        echo "REPORT FILES GENERATED:"
        ls -la "$REPORT_DIR"/*$TIMESTAMP* 2>/dev/null || echo "No report files found"
        echo ""
        echo "RECOMMENDATIONS:"
        echo "1. Review all HIGH and CRITICAL severity findings"
        echo "2. Update vulnerable dependencies identified in npm audit"
        echo "3. Address SAST findings related to OWASP Top 10"
        echo "4. Fix ESLint security rule violations"
        echo "5. Regularly run these scans in CI/CD pipeline"
        echo ""
        echo "For detailed findings, check individual report files in $REPORT_DIR/"
        echo "=========================================="
    } > "$SCAN_RESULTS_FILE"
    
    log_result "✅ Security scan summary generated: $SCAN_RESULTS_FILE"
}

# Main execution
main() {
    # Check if we're in the right directory
    if [[ ! -f "package.json" ]] || [[ ! -d "client" ]] || [[ ! -d "server" ]]; then
        echo -e "${RED}❌ Error: This script must be run from the project root directory${NC}"
        exit 1
    fi
    
    # Install dependencies first
    echo -e "${YELLOW}📦 Installing project dependencies...${NC}"
    cd client && npm install --legacy-peer-deps && cd ..
    cd server && npm install && cd ..
    
    # Install scanning tools
    install_tools
    
    # Run all scans
    scan_dependencies
    scan_sast
    scan_eslint
    scan_container
    
    # Generate summary
    generate_summary
    
    echo -e "${GREEN}🎉 Security scanning completed successfully!${NC}"
    echo -e "${BLUE}📋 Summary report: $SCAN_RESULTS_FILE${NC}"
    echo -e "${BLUE}📁 Detailed reports: $REPORT_DIR/${NC}"
}

# Run main function
main "$@"
