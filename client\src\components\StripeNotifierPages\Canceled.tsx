import React, { useState, useEffect } from 'react';
import { Link, Redirect } from 'react-router-dom';
import './StripeNotifer.css';

function Canceled() {
  const [timer, setTimer] = useState(5);

  useEffect(() => {
    const interval = setInterval(() => {
      if (timer > 0) {
        setTimer(timer - 1);
      }
    }, 1000);

    return () => {
      clearInterval(interval);
    };
  });

  if (timer === 0) {
    return <Redirect to="/" />;
  }

  return (
    <div className="container">
      <div className="payment-container">
        <h1 className="payment-title">Betaling niet geslaagd</h1>
        <Link to="/dashboard/abonnement" className="payment-message redirect-link">
          Er is een fout opgetreden bij het verwerken van uw betaling. Als er
          een bedrag van uw rekening is afgeschreven, neem dan contact met ons
          op via het ondersteuningsformulier.
        </Link>
        <p>
          U wordt omgeleid in
          {timer}
          {' '}
          seconden naar uw Dashboard.
        </p>
        <Link to="/dashboard/startpagina" className="redirect-link">
          Ga direct naar het Dashboard
        </Link>
      </div>
    </div>
  );
}

export default Canceled;
