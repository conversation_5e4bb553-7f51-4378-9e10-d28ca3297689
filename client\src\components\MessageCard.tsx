import styled from "styled-components";
import React, { FC, useState } from "react";
import { TrashDeleteBin } from "react-basicons";
import { User } from "../@types";
import convertToDutchMonth from "../helpers/convertToDutchMonth";
import {
  setCurrentMessage,
  removeSelectedMessages,
  addSelectedMessages,
  deleteSelectedMessages,
} from "../redux/actions/messageActions";
import { MessageState } from "../redux/features/messageSlice";
import { useAppDispatch } from "../redux/hooks";
import { Colors, device } from "../styles/Theme";

interface MessageCardProps {
  messages: MessageState["messages"];
  setMessageActive: (value: boolean) => void;
  user: User | null;
  selectedMessages: MessageState["selectedMessages"];
  setDeleteMultiple: (value: boolean) => void;
  deleteMultiple: boolean;
}
const MessageCard: FC<MessageCardProps> = ({
  messages,
  setMessageActive,
  user,
  selectedMessages,
  deleteMultiple,
  setDeleteMultiple,
}) => {
  const dispatch = useAppDispatch();
  const [showDelete, setShowDelete] = useState<string | null>(null);
  const [selectAll, setSelectAll] = useState(false);
  const clickActions = (message: MessageState["currentMessage"]) => {
    message && dispatch(setCurrentMessage(message));
    setMessageActive(true);
  };

  const singleDelete = (message: MessageState["currentMessage"]) => {
    message && setShowDelete(message.id);
    message && dispatch(setCurrentMessage(message));
  };

  const handleSelectAll = () => {
    setSelectAll(!selectAll);
    setDeleteMultiple(!deleteMultiple);
    if (selectedMessages.length === messages.length) {
      messages.forEach((message) => {
        dispatch(removeSelectedMessages(message.id));
      });
    } else {
      messages.forEach((message) => {
        if (selectedMessages.includes(message.id)) {
        } else {
          dispatch(addSelectedMessages(message.id));
        }
      });
    }
  };

  const handleDeleteSelected = () => {
    const userId = user?.user_id;
    userId &&
      dispatch(deleteSelectedMessages({ selectedMessages, userId, dispatch }));
  };

  return (
    <>
      {messages.length > 0 &&
        messages.map((message) => (
          <Body key={message.id}>
            <Container
              onMouseEnter={() => singleDelete(message)}
              onMouseLeave={() => setShowDelete(null)}
            >
              <input
                id={message.id}
                type="checkbox"
                checked={selectAll}
                onChange={handleSelectAll}
              />
              <Textcontainer>
                <Titlestyle
                  onClick={() => clickActions(message)}
                  style={{ cursor: "pointer" }}
                >
                  {message.title}
                </Titlestyle>
                <Textstyles
                  dangerouslySetInnerHTML={{
                    __html: message.message,
                  }}
                />
              </Textcontainer>

              {showDelete === message.id && (
                <DeleteContainer onClick={handleDeleteSelected}>
                  <TrashDeleteBin />
                </DeleteContainer>
              )}
            </Container>
            <DateContainer>
              {convertToDutchMonth(message.created_at)}
            </DateContainer>
          </Body>
        ))}
    </>
  );
};

export default MessageCard;

const Body = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const Container = styled.div`
  display: flex;
  align-items: center;
  gap: 30px;
  background-color: ${Colors.main.white};
  width: 100%;
  height: 100px;
  padding: 2rem;
  box-shadow: 0px 3px 30px rgba(0, 0, 0, 0.161);
  border-radius: 10px;
  margin-top: -20px;
  @media ${device.mobileL} {
    height: 140px;
    padding: 1.5rem;
    gap: 20px;
  }
`;

const Titlestyle = styled.div`
  color: ${Colors.main.green};
  font-size: 20px;
  font-weight: 500;

  @media ${device.laptop} {
    font-size: 16px;
    font-weight: 400;
  }
`;

const DateContainer = styled.div`
  margin-left: auto;
  font-style: italic;
  font-size: 14px;
`;

const Textcontainer = styled.div`
  display: flex;
  flex-direction: column;
`;

const Textstyles = styled.div`
  overflow: hidden;
  -webkit-line-clamp: 1;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  margin-top: 10px;
  font-size: 16px;

  @media ${device.mobileL} {
    font-size: 12px;
  }

  a {
    color: ${Colors.main.green};
  }
`;

const DeleteContainer = styled.div`
  margin-left: auto;
`;
