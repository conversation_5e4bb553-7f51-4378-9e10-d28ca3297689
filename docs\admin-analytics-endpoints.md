# Admin Analytics Endpoints Documentation

## Overview

These endpoints provide analytics data for the admin dashboard charts and metrics. The analytics data is presented in different chart formats based on the type:
- Active Users: Time-series data for line charts
- New Users: Daily counts for bar charts
- Property Generations: Daily counts for bar charts

Each chart endpoint includes comparison metrics with the previous period.

## Base URL

```
http://localhost:5000/api/v1/admin
```

## Endpoints

### 1. Get Mopsus Analytics Overview

```http
GET /analytics/mopsus?timeframe={timeframe}
```

Returns combined summary analytics metrics for active users, new users, property generations, and onboarding funnel.

#### Query Parameters

- `timeframe` (optional): "24h" | "7d" | "30d" | "90d" (default: "30d")

#### Response Format

```typescript
{
  status: "success",
  activeUsers: {
    count: number,       // Total active users (today)
    growth: number       // Percentage growth compared to previous period
  },
  newUsers: {
    count: number,       // Total new users in the period
    growth: number       // Percentage growth compared to previous period
  },
  propertyGenerations: {
    count: number,       // Total property generations in the period
    growth: number       // Percentage growth compared to previous period
  },
  onboardingFunnel: {
    signups: number,
    emailVerifications: {
      count: number,
      rate: number       // Percentage rate
    },
    onboardingCompletes: {
      count: number,
      rate: number       // Percentage rate
    },
    firstApiCalls: {
      count: number,
      rate: number       // Percentage rate
    }
  }
}
```

### 2. Get Active Users Line Chart Data

```http
GET /analytics/mopsus/chart/active-users?start={start}&end={end}
```

Returns time-series data for active users over the specified date range, formatted for line chart visualization.

#### Query Parameters

- `start`: ISO date string (e.g., "2025-01-01")
- `end`: ISO date string (e.g., "2025-02-01")

#### Response Format

```typescript
{
  status: "success",
  data: {
    dates: string[],  // Array of dates in YYYY-MM-DD format
    values: number[], // Array of daily counts
    total: number,    // Total count for the current day
    change: {
      value: number,      // Absolute change compared to previous day (can be negative)
      percentage: number  // Percentage change compared to previous day (negative for decrease)
    }
  }
}
```

#### Sample Response

```json
{
  "status": "success",
  "data": {
    "dates": ["2025-03-04", "2025-03-05"],
    "values": [1, 0],
    "total": 0,
    "change": {
      "value": -1,
      "percentage": -100
    }
  }
}
```

### 3. Get New Users Bar Chart Data

```http
GET /analytics/mopsus/chart/new-users?start={start}&end={end}
```

Returns daily new user counts for the specified date range, formatted for bar chart visualization.

#### Query Parameters

- `start`: ISO date string (e.g., "2025-01-01")
- `end`: ISO date string (e.g., "2025-02-01")

#### Response Format

```typescript
{
  status: "success",
  data: {
    dates: string[],  // Array of dates in YYYY-MM-DD format
    values: number[], // Array of daily counts
    total: number,    // Total count for the current day
    change: {
      value: number,      // Absolute change compared to previous day (can be negative)
      percentage: number  // Percentage change compared to previous day (negative for decrease)
    }
  }
}
```

### 4. Get Property Generations Bar Chart Data

```http
GET /analytics/mopsus/chart/property-generations?start={start}&end={end}
```

Returns daily property generation counts for the specified date range, formatted for bar chart visualization.

#### Query Parameters

- `start`: ISO date string (e.g., "2025-01-01")
- `end`: ISO date string (e.g., "2025-02-01")

#### Response Format

```typescript
{
  status: "success",
  data: {
    dates: string[],  // Array of dates in YYYY-MM-DD format
    values: number[], // Array of daily counts
    total: number,    // Total count for the current day
    change: {
      value: number,      // Absolute change compared to previous day (can be negative)
      percentage: number  // Percentage change compared to previous day (negative for decrease)
    }
  }
}
```

## Example Usage

```typescript
// Get overview metrics for last 7 days
const overviewResponse = await api.get("/admin/analytics/mopsus?timeframe=7d");

// Get active users line chart data for day-over-day comparison
const activeUsersChart = await api.get("/admin/analytics/mopsus/chart/active-users?start=2025-03-05&end=2025-03-06");

// Get new users bar chart data for day-over-day comparison
const newUsersChart = await api.get("/admin/analytics/mopsus/chart/new-users?start=2025-03-05&end=2025-03-06");

// Get property generations bar chart data for day-over-day comparison
const propertyChart = await api.get("/admin/analytics/mopsus/chart/property-generations?start=2025-03-05&end=2025-03-06");
```

## Authentication

All endpoints require:

1. A valid session (protected routes)
2. Admin user role (restricted access)

```typescript
// Routes are protected with middleware
router.use(protect);
router.use(restrictTo("admin"));
```

## Implementation Notes

1. **Change Calculations:**
   - For day-over-day comparison:
     ```
     change = current_day_total - previous_day_total
     percentage = (change / previous_day_total) * 100
     ```
   - If previous_day_total is 0:
     - If current_day_total is also 0: percentage = 0%
     - If current_day_total > 0: percentage = 100%
   - Negative percentages indicate a decrease in activity
   - Percentages are rounded to 2 decimal places

2. **Chart Data Types:**
   - Active Users: Line chart data with daily unique user counts
   - New Users: Bar chart data with daily signup counts
   - Property Generations: Bar chart data with daily generation counts

3. **Period Comparisons:**
   - Each chart response includes total count for the current day
   - Comparison with previous day shows both:
     - Absolute change (can be negative)
     - Percentage change (can be negative)

4. **Data Source:**
   - Table: `analytics_logger`
   - Columns: id (INT), event (TEXT), category (TEXT), date (DATE), time (TIME)
   - No dependency on legacy analytics_events table

5. **Event Categories:**
   - Authentication: Signup, EmailVerified, Signin
   - API: ApiCall, FirstApiCall
   - Property: PropertyGenerated
   - Onboarding: OnboardingComplete

6. **Default Behaviors:**
   - Invalid date ranges default to last 30 days
   - Missing dates in ranges are filled with zero values
   - Daily counts use UTC timezone
   - Day-over-day comparison shows decrease as negative percentage
