# Active User Line Chart Implementation Plan

## Overview

The goal is to use the active user analytics data to power a line chart on the admin dashboard. Currently, the analytics system returns summary metrics for active users through the endpoint that uses `AnalyticsLoggingService.getActiveUsersMetrics`. However, the active user response should instead be provided on a per-day basis to enable line chart visualization.

This document outlines the plan to update the analytics endpoints so that the active user data is returned in a format suitable for a line chart.

## Current Issues

1. **Column Name Mismatch:**  
   The `analytics_logger` table schema uses an `id` column instead of `uuid`. Some queries currently reference `uuid`, causing errors.

2. **Timestamp and Date Columns:**  
   The database table has separate `date` and `time` columns along with a `created_at` column. Our queries should use the `date` column for date-based aggregation.

3. **Line Chart Data Format:**  
   The current line chart endpoint (`getMopsusLineChartAnalytics`) must return daily active user counts in a consistent date range, filling in days with zero counts where no data exists.

4. **Error in Active User Response:**  
   There are reported errors regarding invalid timestamp formats and references to columns that do not exist. We need to ensure that:
   - All date manipulations properly use the `date` column.
   - The recursive CTE in the line chart query correctly generates a series of dates.
   - The input parameters (start and end dates) are correctly parsed and formatted.

## Proposed Changes

### 1. Update Active User Metrics Query

- **Summary Query Update:**  
  Modify the query in `AnalyticsLoggingService.getActiveUsersMetrics` to count distinct active users using the `id` field. Use the `date` column to aggregate daily counts.

- **Line Chart Query Update:**  
  Create or update the `getMopsusLineChartAnalytics` method within `AnalyticsService` so that it:
  - Uses a recursive common table expression (CTE) to generate a date series between the provided start and end dates.
  - Aggregates daily active user counts from the `analytics_logger` table.
  - Joins the generated date series with the daily counts to ensure that missing days are filled with zero counts.
  - Returns the result in an object with two arrays: one for dates and one for corresponding counts.

### 2. Data Conversion and Validation

- **Date Handling:**  
  Ensure that the input dates (`start` and `end`) are validated. If invalid, default to a 30-day range.
  
- **Type Casting:**  
  Use proper type casting in SQL (`::date`) to avoid timestamp conversion errors.

### 3. Testing and Verification

- Update the tests in `server/tests/analytics.test.ts` to ensure:
  - The active users line chart endpoint returns valid dates and counts.
  - The data aligns with the expected schema.
  
- Verify that the error “column 'uuid' does not exist” is resolved by substituting `id` where appropriate.

### 4. Sample Response of the Proposed Implementation

Assuming the date range from `2025-02-04` to `2025-02-10`, a sample JSON response from the active user line chart endpoint might look like:

```json
{
  "status": "success",
  "data": {
    "dates": [
      "2025-02-04",
      "2025-02-05",
      "2025-02-06",
      "2025-02-07",
      "2025-02-08",
      "2025-02-09",
      "2025-02-10"
    ],
    "values": [
      150,
      175,
      160,
      180,
      170,
      165,
      155
    ]
  }
}
```

In this sample:
- The `dates` array represents each day in the specified range.
- The `values` array contains the active user counts (e.g., users who generated an API call or signed in) for each corresponding day.

## Next Steps

1. **Implementation:**  
   Update the code in `AnalyticsService.getMopsusLineChartAnalytics` and `AnalyticsLoggingService.getActiveUsersMetrics` to match the changes outlined above.

2. **Testing:**  
   Run existing unit tests and add new tests to cover the line chart data endpoint.

3. **Deployment:**  
   Deploy the changes to a development environment and verify that the admin dashboard displays the active user line chart correctly without errors.

4. **Review:**  
   Once verified, prepare a release note detailing the improvements in analytics data handling.

## Conclusion

This plan ensures that the active user analytics data is provided in a format suitable for a line chart on the admin dashboard, resolving current errors and enhancing data quality. The sample response illustrates the expected output, enabling frontend developers to correctly integrate this data into their visualizations.

Please review this plan and let me know if any adjustments are necessary before proceeding to implementation.