import React, { useEffect, useRef } from 'react';
import styled from 'styled-components';

const SVG = styled.svg`
  .success-rect,
  .success-tick,
  .success-checkmark,
  .success-text {
    transition: all 0.5s ease-in-out;
  }

  .success-rect.active {
    transform: scale(1.1);
  }

  .success-tick.active {
    transform: rotate(45deg);
  }

  .success-checkmark.active {
    transform: rotate(135deg);
  }

  .success-text.active {
    transform: translate(0, -20px);
  }
`;

function PaymentSuccessSVG() {
  const rectRef = useRef<SVGRectElement>(null);
  const tickRef = useRef<SVGPolygonElement>(null);
  const checkmarkRef = useRef<SVGPolygonElement>(null);
  const textRef = useRef<SVGTextElement>(null);

  useAnimation([rectRef, tickRef, checkmarkRef, textRef], 500);

  return (
    <SVG width="200" height="200" xmlns="http://www.w3.org/2000/svg">
      <rect x="25" y="25" width="150" height="150" fill="#00FF00" ref={rectRef} />
      <polygon points="90 50 110 50 120 60 110 70 90 70" fill="#FFFFFF" ref={tickRef} />
      <polygon points="100 80 120 90 120 110 100 120 80 110 80 90" fill="#FFFFFF" ref={checkmarkRef} />
      <text x="65" y="135" fontSize="20" fill="#000000" ref={textRef}>
        Success!
      </text>
    </SVG>
  );
}

const useAnimation = (
  refs: Array<React.RefObject<SVGRectElement | SVGPolygonElement | SVGTextElement>>,
  delay: number,
) => {
  useEffect(() => {
    setTimeout(() => {
      refs.forEach((ref) => ref.current?.classList.add('active'));
    }, delay);
  }, [refs, delay]);
};

export default PaymentSuccessSVG;
