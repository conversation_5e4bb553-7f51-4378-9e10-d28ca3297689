# Build stage
FROM node:18-alpine3.19 as build

# Build arguments with no default values (to be provided at build time)
ARG EC2_STAGE
ARG TEST
ARG X_API_KEY
ARG PK_TEST
ARG GOOGLE_STREETVIEW_APIKEY
ARG GOOGLE_TRACKING_ID

# Runtime environment variables
ENV REACT_APP_EC2_IP=${EC2_STAGE}
ENV REACT_APP_STAGE=${TEST}
ENV REACT_APP_TEST_X_API_KEY=${X_API_KEY}
ENV REACT_APP_STRIPE_PK_TEST=${PK_TEST}
ENV REACT_APP_GOOGLE_STREETVIEW_APIKEY=${GOOGLE_STREETVIEW_APIKEY}
ENV REACT_APP_GOOGLE_TRACKING_ID=${GOOGLE_TRACKING_ID}
ENV SKIP_PREFLIGHT_CHECK=true
ENV NODE_ENV=production

# Set work directory
WORKDIR /app

# Install dependencies
COPY client/package*.json ./
RUN npm ci --legacy-peer-deps --production=false

# Copy source code
COPY client/ ./

# Build app with increased memory
RUN NODE_OPTIONS="--max_old_space_size=4096" npm run build

# Production stage
FROM node:18-alpine3.19

# Set production environment
ENV NODE_ENV=production

# Create non-root user
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# Set work directory
WORKDIR /app

# Install production dependencies for server
COPY server/package*.json ./
COPY server/tsconfig.json ./
RUN npm ci --production

# Copy server source and built client
COPY server/ .
RUN npm run build
COPY --from=build /app/build dist/build

# Set proper permissions
RUN chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 5000

# Start command
CMD ["npm", "run", "start"]