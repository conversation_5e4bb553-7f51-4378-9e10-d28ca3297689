# Build stage
FROM node:18-alpine3.19 as build

# Install security updates and required packages
RUN apk update && apk upgrade && \
    apk add --no-cache \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Build arguments with no default values (to be provided at build time)
ARG EC2_STAGE
ARG TEST
ARG X_API_KEY
ARG PK_TEST
ARG GOOGLE_STREETVIEW_APIKEY
ARG GOOGLE_TRACKING_ID

# Runtime environment variables
ENV REACT_APP_EC2_IP=${EC2_STAGE}
ENV REACT_APP_STAGE=${TEST}
ENV REACT_APP_TEST_X_API_KEY=${X_API_KEY}
ENV REACT_APP_STRIPE_PK_TEST=${PK_TEST}
ENV REACT_APP_GOOGLE_STREETVIEW_APIKEY=${GOOGLE_STREETVIEW_APIKEY}
ENV REACT_APP_GOOGLE_TRACKING_ID=${GOOGLE_TRACKING_ID}
ENV SKIP_PREFLIGHT_CHECK=true
ENV NODE_ENV=production
ENV CI=true

# Set work directory
WORKDIR /app

# Create non-root user for build stage
RUN addgroup -S buildgroup && adduser -S builduser -G buildgroup

# Copy package files and install dependencies
COPY --chown=builduser:buildgroup client/package*.json ./
USER builduser

# Install dependencies with optimizations
RUN npm ci --legacy-peer-deps --production=false --no-audit --no-fund --prefer-offline

# Copy source code
COPY --chown=builduser:buildgroup client/ ./

# Build app with increased memory and optimizations
RUN NODE_OPTIONS="--max_old_space_size=4096" \
    GENERATE_SOURCEMAP=false \
    INLINE_RUNTIME_CHUNK=false \
    npm run build

# Production stage
FROM node:18-alpine3.19

# Add metadata labels
LABEL maintainer="Mopsus Team" \
      version="1.0" \
      description="Mopsus Data Platform - Staging Environment" \
      environment="staging"

# Install security updates and required packages
RUN apk update && apk upgrade && \
    apk add --no-cache \
    dumb-init \
    tini \
    curl \
    && rm -rf /var/cache/apk/*

# Set production environment
ENV NODE_ENV=production
ENV NODE_OPTIONS="--max-old-space-size=2048"

# Create non-root user
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# Set work directory
WORKDIR /app

# Install production dependencies for server
COPY --chown=appuser:appgroup server/package*.json ./
COPY --chown=appuser:appgroup server/tsconfig.json ./

# Switch to non-root user for dependency installation
USER appuser
RUN npm ci --production --no-audit --no-fund

# Copy server source and built client
USER root
COPY --chown=appuser:appgroup server/ .
COPY --chown=appuser:appgroup --from=build /app/build dist/build

# Build server
USER appuser
RUN npm run build

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:5000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })" || exit 1

# Expose port
EXPOSE 5000

# Use tini as init system for proper signal handling
ENTRYPOINT ["tini", "--"]

# Start command
CMD ["npm", "run", "start"]