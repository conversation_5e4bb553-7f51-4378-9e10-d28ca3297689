FROM node:18-alpine3.19 as build

ARG EC2_STAGE
ARG TEST
ARG X_API_KEY
ARG PK_TEST
ARG GOOGLE_STREETVIEW_APIKEY
ARG GOOGLE_TRACKING_ID

ENV REACT_APP_EC2_IP $EC2_STAGE
ENV REACT_APP_STAGE $TEST
ENV REACT_APP_TEST_X_API_KEY $X_API_KEY
ENV REACT_APP_STRIPE_PK_TEST $PK_TEST
ENV REACT_APP_GOOGLE_STREETVIEW_APIKEY $GOOGLE_STREETVIEW_APIKEY
ENV REACT_APP_GOOGLE_TRACKING_ID $GOOGLE_TRACKING_ID
ENV SKIP_PREFLIGHT_CHECK=true

WORKDIR '/app'
COPY client/package.json .
RUN npm install --legacy-peer-deps
COPY client .
RUN NODE_OPTIONS="--max_old_space_size=4096" npm run build

FROM node:18-alpine3.19
WORKDIR '/app'
COPY server/package.json ./
COPY server/tsconfig.json ./
RUN npm install
COPY server .
RUN npm run build
COPY --from=build /app/build  dist/build

EXPOSE 5000

CMD ["npm", "run", "start"]