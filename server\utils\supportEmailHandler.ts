import nodemailer from "nodemailer";
import pug from "pug";
import htmlToText from "html-to-text";

export default class SupportEmail {
  to: string;
  from: any;
  constructor(email: string) {
    this.to = "<EMAIL>";
    this.from = "<EMAIL>";
  }

  newTransport() {
    return nodemailer.createTransport({
      service: "SendGrid",
      auth: {
        user: process.env.SENDGRID_USERNAME,
        pass: process.env.SENDGRID_PASSWORD,
      },
    });
  }

  async send(template: string, subject: string, messageText: string) {
    // Render template based on PUG
    const html = pug.renderFile(`${__dirname}/../views/email/${template}.pug`, {
      messageText,
    });

    // Define email options
    const mailOptions = {
      from: this.from,
      to: this.to,
      subject,
      html,
      text: htmlToText.toString(),
    };

    // Create a transport and send email
    await this.newTransport().sendMail(mailOptions);
  }

  async sendSupportEmail(subject: string, text: string) {
    await this.send("supportEmail", subject, text);
  }
}
