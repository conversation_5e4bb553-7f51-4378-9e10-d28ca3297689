import { AxiosRequestConfig, AxiosRequestHeaders } from "axios";
import { Request } from "express";
import { JwtPayload } from "jsonwebtoken";
import Stripe from "stripe";

interface User {
  usage_tracked_daily: string | number | Date;
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  role: "user" | "admin";
  active: boolean;
  password_reset_token: string;
  password_reset_token_expiry: Date;
  api_key: string;
  api_key_id: string;
  stripe_customer_id: string;
  current_usage_plan: string;
  receive_email: boolean;
  created_at: Date;
  updated_at: Date;
  plan_changed_at: Date;
  usage_tracked_at: Date;
  metered_billing: boolean;
  transaction_api_key: string | null;
  transaction_usage_plan: string | null;
  transaction_api_key_id: string | null;
  company: string;
  otp: string;
  otp_expiration: number;
  is_migrated?: boolean;
  two_factor_enabled: boolean;
  totp_secret?: string;
  two_factor_type?: "email" | "authenticator";
  two_factor_failed_attempts?: number;
  two_factor_lockout_until?: Date;
  last_password_change?: Date;
  usageId?: string;
}

interface AuthUser extends User {
  requires2FA?: boolean;
  status?: string;
  message?: string;
}

interface SubscriptionPlan {
  create: () => Promise<{ status: string; updatedPlan: boolean }>;
  update: () => Promise<{ status: string; message: string }>;
  delete: () => Promise<{ status: string; updatedPlan: boolean }>;
}
interface UserSubscription {
  id: string;
  customer_id: string;
  subscription_id: string;
  subscription_item: string;
  date: Date;
  active: boolean;
}

interface UnlimitedSubscription extends UserSubscription {
  api_key: string;
  api_key_id: string;
  usage_plan: string | null;
  usage_tracked_at: Date;
  plan_changed_at: Date;
  created_at: Date;
  updated_at: Date;
}
interface StripeObject extends Stripe.Event.Data.Object {
  amount_due?: number;
  hosted_invoice_url?: string | null | undefined;
  id: string;
  receipt_url?: string | null;
  items?: any;
  customer: string | Stripe.Customer | Stripe.DeletedCustomer | null;
}
interface StripePaymentData {
  paymentMethodId: string;
  customerId: string;
}
type AuthRequest = {
  user: User;
} & Request;
interface Header extends AxiosRequestHeaders {
  "x-api-key": string;
}
interface Config extends AxiosRequestConfig {
  headers: Header;
}
interface JWTPayload extends JwtPayload {
  id: string;
}
interface SendGridContact {
  list_ids: string[];
  status: number;
  id: string;
}

interface Statistics {
  day: string;
  calls: number;
  AmenitiesAPI: number;
  AutoSuggestAPI: number;
  AVMAPI: number;
  EcoValue: number;
  LocationAPI: number;
  MoveDataAPI: number;
  WOZAPI: number;
  BagAPI: number;
  ReferenceAPI: number;
  SustainabilityAPI: number;
  "InteractiveReference-API": number;
  LabellingAPI: number;
  TransactionAPI: number;
}
interface UserNotification {
  notification_id: string;
  user_id: string;
  error_403: boolean;
  error_429: boolean;
  error_500: boolean;
  error_422: boolean;
  invoice_successful: boolean;
  credit_depleted: boolean;
  analytics_email_usage: boolean;
  created_at: Date;
}
interface Message {
  title: string;
  message: string;
  type: string;
  created_at: string;
  opened: string;
  id: string;
}
interface StripeBilling {
  customer_id: string;
  payment_method_id: string;
  last_four_digits: string;
}

export interface PropertyDetails {
  yearOfConstruction: string;
  location: string;
  area: string;
  propertyType: string;
  facilities: string;
}

export interface ConfigureOutput {
  tone: string;
  targetAudience: string;
  languagePreference: string;
  descriptionLength: string;
}

interface GetUsersResponse {
  status: string;
  data: PaginatedResponse<User[]>;
}
