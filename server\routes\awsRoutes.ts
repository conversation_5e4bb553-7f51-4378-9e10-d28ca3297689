import express from "express";
import { protect } from "../controllers/authController";
import {
  apiLogs,
  getCallsAnalytics,
  getWeeklyUsageData,
  getTotalApiCalls,
  getUsagePlanDetails,
} from "../controllers/awsController";

const router = express.Router();
// Get user api usage
router.get("/usage", protect, getUsagePlanDetails);

router.get("/total-calls", getTotalApiCalls);

router.get("/usage-analytics/:days", getCallsAnalytics);
router.get("/usage-logs", protect, apiLogs);
router.get("/weekly-usage", protect, getWeeklyUsageData);

export default router;
