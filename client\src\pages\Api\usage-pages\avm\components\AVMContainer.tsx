import React, { ReactNode } from "react";
import ApiFormContainer from "../../components/ApiFormContainer";
import { AVM } from "../../../../../assets/images/api/APIimages";

const progress = ["Woning identificatie", "Resultaat"];

const AVMContainer: React.FC<{
  children: ReactNode;
  page: number;
}> = ({ children, page }) => {
  return (
    <ApiFormContainer
      page={page}
      title="Woningwaarde API"
      subtitle="Waardeer een woning accuraat volgens de regels van NRVT - De Woningwaarde API genereert de marktwaarde van woningen in Nederland en is getraind op Kadaster transacties en een rijke, actuele woningkenmerken database volgens de spelregels van NRVT."
      progress={progress}
      resultSelector={(state) => state.avm.result}
      link="https://docs.altum.ai/apis/woningwaarde-api"
      image={AVM}
    >
      {children}
    </ApiFormContainer>
  );
};

export default AVMContainer;
