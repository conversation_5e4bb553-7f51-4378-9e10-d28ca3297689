FROM node:18-alpine3.19 as build 

ARG EC2_PROD
ARG STAGE_PROD
ARG X_API_KEY
ARG PK_LIVE
ARG GOOGLE_STREETVIEW_APIKEY
ARG GOOGLE_TRACKING_ID

ENV REACT_APP_EC2_IP $EC2_PROD
ENV REACT_APP_STAGE $STAGE_PROD
ENV REACT_APP_TEST_X_API_KEY $X_API_KEY
ENV REACT_APP_STRIPE_PK_LIVE $PK_LIVE
ENV REACT_APP_GOOGLE_STREETVIEW_APIKEY $GOOGLE_STREETVIEW_APIKEY
ENV REACT_APP_GOOGLE_TRACKING_ID $GOOGLE_TRACKING_ID
ENV SKIP_PREFLIGHT_CHECK=true

WORKDIR '/app'
COPY client/package*.json ./
RUN npm ci --legacy-peer-deps --production=false
COPY client .
RUN NODE_OPTIONS="--max_old_space_size=4096" npm run build

FROM nginx:alpine as production
RUN apk add --no-cache nodejs npm

WORKDIR '/app'
COPY server/package*.json ./
COPY server/tsconfig.json ./
RUN npm ci --production
COPY server .
RUN npm run build
COPY --from=build /app/build dist/build

# Configure Nginx
COPY nginx.conf /etc/nginx/nginx.conf
RUN chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    touch /var/run/nginx.pid && \
    chown -R nginx:nginx /var/run/nginx.pid

EXPOSE 80

# Start Nginx and Node.js
CMD nginx && npm run start