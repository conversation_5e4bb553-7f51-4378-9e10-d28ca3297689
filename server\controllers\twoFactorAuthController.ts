import { NextFunction, Request, Response } from "express";
import { User } from "../@types";
import AppError from "../utils/appError";
import { createTokenResponse } from "../utils/authUtil";
import { twoFactorAuthService } from "../services/twoFactorAuthService";

// Enable 2FA for a user (Email or Authenticator app)
export const enable2FAHandler = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { type } = req.body as { type: "email" | "authenticator" };
    const user = req.user as User;

    if (!user) {
      return next(new AppError("User not found", 404, true));
    }

    const result = await twoFactorAuthService.enableTwoFactor(
      user.user_id,
      type,
    );

    res.status(200).json({
      status: "success",
      message: `2FA enabled with ${type}`,
      data: result,
    });
  } catch (error: any) {
    console.error("Error enabling 2FA:", error);
    next(new AppError(error.message || "Error enabling 2FA", 500, false));
  }
};

// Disable 2FA for a user
export const disable2FAHandler = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { password } = req.body;
    const user = req.user as User;

    if (!user) {
      return next(new AppError("User not found", 404, true));
    }

    if (!password) {
      return next(
        new AppError("Password is required to disable 2FA", 400, true),
      );
    }

    await twoFactorAuthService.disableTwoFactor(user.user_id, password);

    res.status(200).json({
      status: "success",
      message: "2FA disabled",
    });
  } catch (error: any) {
    console.error("Error disabling 2FA:", error);
    next(new AppError(error.message || "Error disabling 2FA", 500, false));
  }
};

// Verify 2FA code during login
export const verify2FALoginHandler = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { userId, code } = req.body;

    if (!userId || !code) {
      return next(new AppError("userId and code are required", 400, true));
    }

    const user = await twoFactorAuthService.verifyTwoFactorLogin(userId, code);

    // Create and send JWT token
    createTokenResponse(user, res);
  } catch (error: any) {
    console.error("Error verifying 2FA:", error);
    return next(
      new AppError(error.message || "Error verifying 2FA", 500, false),
    );
  }
};

export const request2FACodeHandler = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return next(new AppError("userId is required", 400, true));
    }

    await twoFactorAuthService.requestNewCode(userId);

    res.status(200).json({
      status: "success",
      message: "2FA code sent",
    });
  } catch (error: any) {
    console.error("Error requesting 2FA code:", error);
    next(
      new AppError(error.message || "Error requesting 2FA code", 500, false),
    );
  }
};
