import express from "express";
import { protect } from "../controllers/authController";
import { restrictTo } from "../middlewares/authMiddleware";
import {
  editUser,
  getMopsusAnalytics,
  getMopsusActiveUsersChart,
  getMopsusNewUsersChart,
  getMopsusPropertyGenerationsChart,
  getUserById,
  getUsers,
  createUser,
  deleteUser,
  migrateLegacyUser,
  getUsagePlans,
  getUsagePlan,
  createUsagePlan,
  updateUsagePlan,
  deleteUsagePlan,
  getApiUsageOverview,
  addTestCredit,
  getUserApiUsage,
  blockEmail,
  unblockEmail,
  getBlockedEmails,
  getDetailedAnalytics,
  getAllApiUsageAnalytics,
  logAnalyticsEvent,
} from "../controllers/adminController";

const router = express.Router();

// Protect all routes and restrict to admin users
router.use(protect);
router.use(restrictTo("admin"));

// Analytics routes
router.get("/analytics/mopsus", getMopsusAnalytics);
router.get("/analytics/mopsus/chart/active-users", getMopsusActiveUsersChart);
router.get("/analytics/mopsus/chart/new-users", getMopsusNewUsersChart);
router.get(
  "/analytics/mopsus/chart/property-generations",
  getMopsusPropertyGenerationsChart,
);
router.get("/analytics/api-usage/all", getAllApiUsageAnalytics);
router.get("/analytics/detailed", getDetailedAnalytics);
router.post("/analytics/log", logAnalyticsEvent);

// User management routes
router.get("/users", getUsers);
router.get("/user/:userId", getUserById);
router.patch("/user/:userId", editUser);
router.post("/user", createUser);
router.delete("/user/:userId", deleteUser);
router.post("/user/migrate", migrateLegacyUser);
router.post("/user/:userId/test-credit", addTestCredit);
router.get("/user/:userId/api-usage", getUserApiUsage);

// Usage plan routes
router.get("/usage-plans", getUsagePlans);
router.get("/usage-plan/:planId", getUsagePlan);
router.post("/usage-plan", createUsagePlan);
router.patch("/usage-plan/:planId", updateUsagePlan);
router.delete("/usage-plan/:planId", deleteUsagePlan);
router.get("/api-usage", getApiUsageOverview);

// Blocked users routes
router.post("/block-email", blockEmail);
router.delete("/unblock-email/:email", unblockEmail);
router.get("/blocked-emails", getBlockedEmails);

export default router;
