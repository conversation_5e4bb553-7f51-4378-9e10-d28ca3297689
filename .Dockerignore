# Version control
.git
.gitignore
.hg
.hgignore
.svn
.bzr
.cvs

# Environment & secrets
.env
.env.*
*.env
.env.local
.env.*.local
.env.development
.env.test
.env.production

# IDE files
.idea/
.vscode/
*.swp
*.swo
*.swn
*.bak

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Dependencies
node_modules/
.pnp/
.pnp.js

# Testing
coverage/
.nyc_output/

# Build outputs
dist/
build/
*.tsbuildinfo

# System files
.DS_Store
Thumbs.db

# Backup files
*.bak
*.backup
*~

# Config files that might contain sensitive data
*.config.js
*.config.ts
config.js
config.ts

# Documentation
docs/
*.md

# Temporary files
tmp/
temp/

# Other VCS directories
.svn/
.cvs/
.bzr/
