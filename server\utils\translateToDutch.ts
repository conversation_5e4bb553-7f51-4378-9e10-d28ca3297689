import OpenAI from "openai";

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});
export async function translateEnglishToDutch(text: string): Promise<string> {
  try {
    const response = await openai.chat.completions.create({
      messages: [
        {
          role: "system",
          content:
            "you are an error translator, interpret the error and send the message to the user in dutch. The message should be short and exactly the the translation of the error sent in english",
        },
        {
          role: "user",
          content: text,
        },
      ],
      model: "gpt-3.5-turbo",
    });

    const translatedText =
      response.choices[0].message.content || "something went wrong";
    return translatedText;
  } catch (error) {
    console.log(error);
    return "Netwerkfout";
  }
}
