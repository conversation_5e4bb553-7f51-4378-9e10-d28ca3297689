import { GraphQLClient } from "graphql-request";
import AppError from "./appError";

export default async (apikey: string, timeSince: number) => {
  try {
    if (timeSince < 1) {
      return;
    }
    const keyLength = apikey.length;
    const last6 = apikey.slice(keyLength - 6);
    const hidden = "*".repeat(keyLength - 6);
    const hiddenApiKey = `${hidden}${last6}`;

    let query = `
    {
      actor {
        account(id: $id) {
          nrql(
            query: "FROM Log SELECT api_name, requestTime, status WHERE apiKey='$1' SINCE $2 minutes ago LIMIT 10"
          ) {
            results
          }
        }
      }
    }
  `;
    query = query.replace("$id", process.env.GRAPHQL_ID!);
    query = query.replace("$1", hiddenApiKey);
    query = query.replace("$2", timeSince.toString());

    const client = new GraphQLClient("https://api.eu.newrelic.com/graphql", {
      headers: { "api-key": `${process.env.GRAPHQL_APIKEY}` },
    });

    return await client
      .request(query)
      .then((data) => data.actor.account.nrql.results)
      .catch((error) => {
        console.log(error);
        new AppError("unable to fetch usage from new relic", 400);
      });
  } catch (err) {
    console.log(err);
    return new AppError("unable to usage fetch from new relic", 400);
  }
};
