import { createSlice } from '@reduxjs/toolkit';
import {
  addSelectedMessages,
  clearCurrentMessage,
  deleteCurrentMessage,
  deleteSelectedMessages,
  getAllMessagesUser,
  getAllUnreadMessagesUser,
  markAllAsRead,
  markMessageAsRead,
  removeSelectedMessages,
  setCurrentMessage,
  setCurrentPage,
} from '../actions/messageActions';
import { Message } from '../../@types';

export interface MessageState {
  messages: Message[];
  messagesTotal: number;
  currentPage: number;
  loading: boolean;
  error: any;
  currentMessage: Message | null;
  unread: number;
  selectedMessages: string[];
}
const initialState: MessageState = {
  messages: [],
  messagesTotal: 0,
  currentPage: 1,
  loading: false,
  error: null,
  currentMessage: null,
  unread: 0,
  selectedMessages: [],
};

const messageSlice = createSlice({
  name: 'messages',
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(getAllMessagesUser.pending, (state) => {
        state.loading = true;
      })
      .addCase(getAllMessagesUser.fulfilled, (state, action) => {
        state.loading = false;
        state.messages = action.payload.messages;
        state.messagesTotal = action.payload.messagesTotal;
        state.currentPage = action.payload.page;
      })
      .addCase(getAllMessagesUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error;
      })
      .addCase(getAllUnreadMessagesUser.fulfilled, (state, action) => {
        state.unread = action.payload.unread;
      })
      .addCase(setCurrentPage.fulfilled, (state, action) => {
        state.currentPage = action.payload;
      })
      .addCase(setCurrentMessage.fulfilled, (state, action) => {
        state.currentMessage = action.payload;
      })
      .addCase(clearCurrentMessage.fulfilled, (state) => {
        state.currentMessage = null;
      })
      .addCase(markAllAsRead.fulfilled, (state, action) => {
        state.unread = 0;
      })
      .addCase(markMessageAsRead.fulfilled, (state, action) => {
        state.unread = action.payload;
      })
      .addCase(deleteCurrentMessage.fulfilled, (state, action) => {
        state.currentMessage = null;
      })
      .addCase(addSelectedMessages.fulfilled, (state, action) => {
        state.selectedMessages.push(action.payload);
      })
      .addCase(removeSelectedMessages.fulfilled, (state, action) => {
        state.selectedMessages = state.selectedMessages.filter(
          (messageId) => messageId !== action.payload,
        );
      })
      .addCase(deleteSelectedMessages.fulfilled, (state) => {
        state.selectedMessages = [];
      });
  },
});

export default messageSlice.reducer;
