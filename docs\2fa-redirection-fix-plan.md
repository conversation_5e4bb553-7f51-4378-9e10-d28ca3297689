# 2FA Dashboard Redirection Fix Plan

## Problem
After 2FA verification on the signin page, the application fails to redirect to the dashboard despite successful authentication.

## Plan Outline

### 1. Review 2FA Flow in TwoFactorVerify.tsx
- Confirm that upon successful 2FA verification, valid authentication data (token and user) is returned.
- Verify that dispatch(loadUser) updates the state (especially isAuthenticated and requires2FA flags).
- Consider adjusting the setTimeout delay (currently 50 ms) to ensure state updates propagate before redirection.

### 2. Examine Parent Component Logic in SigninRightSection.tsx
- Check that the useEffect correctly evaluates isAuthenticated, requires2FA, and loading flags.
- Ensure the "dashboardRedirected" flag in localStorage isn't inadvertently preventing redirection.
- Confirm no conflicts between TwoFactorVerify and the useEffect-based redirection logic.

### 3. Review State Update Timing
- Verify that after 2FA success, the auth state updates promptly before any redirection is triggered.
- Adjust timing (or explicitly set requires2FA to false) to avoid race conditions.

### 4. Proposed Adjustments
- **Option A:** Increase setTimeout delay in TwoFactorVerify.
- **Option B:** Remove explicit timeout in TwoFactorVerify and rely solely on the parent useEffect.
- **Option C:** Explicitly dispatch an action to set requires2FA to false after a successful 2FA.

### 5. Testing and Verification
- Add debug logs at critical points (after verify2FA, after loadUser, before onVerifySuccess, and before redirection).
- Test both email and authenticator flows to ensure correct redirection.

## Flow Diagram

```mermaid
sequenceDiagram
    participant U as User
    participant TFA as TwoFactorVerify Component
    participant AUTH as AuthActions (verify2FA)
    participant LOAD as loadUser Dispatch
    participant SSR as SigninRightSection (useEffect)
    participant ROUTE as Browser Routing

    U->>TFA: Enter OTP and submit
    TFA->>AUTH: Call verify2FA(userId, code)
    AUTH-->>TFA: Return token & user (Success)
    TFA->>LOAD: Dispatch loadUser action for fresh auth state
    LOAD-->>TFA: Auth state updated (isAuthenticated true, requires2FA false)
    TFA->>SSR: Call onVerifySuccess callback
    SSR->>ROUTE: history.replace("/dashboard/startpagina")