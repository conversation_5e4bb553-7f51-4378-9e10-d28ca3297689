import { Request, Response, NextFunction } from "express";
import AppError from "./appError";
import logger from "./logger";

interface ErrorWithStatus {
  statusCode: number;
  status?: string;
  isOperational?: boolean;
  message?: string;
  stack?: string;
}

type ErrorMiddlewareFunction = (
  err: ErrorWithStatus,
  req: Request,
  res: Response,
  next: NextFunction,
) => void;

// handles error message for JWT expiry
const handleJWTExpired = (err: ErrorWithStatus): AppError =>
  new AppError("De inlog is verlopen. Log opnieuw in", 401, true);

// Returns JSON error response in development
const sendErrorDev: ErrorMiddlewareFunction = (err, req, res) => {
  logger.error(
    `${err.statusCode} - ${req.method} - ${req.originalUrl} - ${err.message}: ${err.stack}`,
  );

  return res.status(err.statusCode || 500).json({
    status: err.status,
    error: err,
    message: err.message,
    stack: err.stack,
  });
};

// Returns JSON error response in production
const sendErrorProd: ErrorMiddlewareFunction = (err, req, res, next) => {
  logger.error(
    `${err.statusCode} - ${req.method} - ${req.originalUrl} - ${err.message}: ${err.stack}`,
  );

  // if error is operational error, send to client
  if (err.isOperational) {
    return res.status(err.statusCode).json({
      status: err.status,
      message: err.message,
    });
  }

  // if error is programming error, send generic message to client
  return res.status(500).json({
    status: "error",
    message: "Er is iets fout gegaan",
  });
};

// Returns JSON error response in test
const sendErrorTest: ErrorMiddlewareFunction = (err, req, res) => {
  // if error is operational error, send to client
  if (err.isOperational) {
    console.log("Operational Error", err);
    return res.status(err.statusCode).json({
      status: err.status,
      message: err.message,
    });
  }

  // send generic message
  console.error("Programming Error", err);
  return res.status(500).json({
    status: "error",
    message: "Er is iets fout gegaan",
  });
};

const errorHandler: ErrorMiddlewareFunction = (err, req, res, next) => {
  // if no status/statusCode mentioned, set to error/500;
  err.statusCode = err.statusCode || 500;
  err.status = err.status || "error";

  // send error logs based on environment
  if (process.env.NODE_ENV === "development") {
    sendErrorDev(err, req, res, next);
  } else if (process.env.NODE_ENV === "production") {
    let error = { ...err };
    error.message = err.message;
    error.stack = err.stack;

    // Custom error handling based on message
    if (error.message === "jwt expired") error = handleJWTExpired(error);

    sendErrorProd(error, req, res, next);
  } else if (process.env.NODE_ENV === "test") {
    let error = { ...err };
    error.message = err.message;

    // Custom error handling based on message
    if (error.message === "jwt expired") error = handleJWTExpired(error);
    sendErrorTest(error, req, res, next);
  }
};

export default errorHandler;
