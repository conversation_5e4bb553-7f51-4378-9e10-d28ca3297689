# Security Scanning Setup for Mopsus Data Platform

## Overview

This document describes the automated security scanning implementation for the Mopsus Data Platform, designed to catch OWASP Top 10 vulnerabilities early in the development lifecycle through CI/CD integration.

## Security Scanning Tools Implemented

### 1. Static Application Security Testing (SAST)

#### Semgrep

- **Purpose**: Advanced SAST tool for detecting security vulnerabilities
- **Configuration**: `.semgrep.yml` with custom rules for OWASP Top 10
- **Coverage**:
  - SQL Injection (A03:2021)
  - XSS vulnerabilities (A03:2021)
  - Path traversal (A01:2021)
  - Weak cryptography (A02:2021)
  - Hardcoded secrets (A07:2021)
  - Command injection (A03:2021)
  - Prototype pollution (A06:2021)

### 2. Dynamic Application Security Testing (DAST)

#### OWASP ZAP Integration

- **Purpose**: Runtime security vulnerability detection
- **Configuration**: `scripts/dast-scan.sh` for automated scanning
- **Recent Findings**: ZAP scan identified 17 security issues (June 2025)
- **Key Issues Addressed**:
  - **CSP Violations**: Fixed wildcard directives and unsafe-inline/unsafe-eval
  - **Missing Security Headers**: Added HSTS, X-Content-Type-Options, X-Frame-Options
  - **Information Disclosure**: Removed X-Powered-By and Server version headers
  - **Clickjacking Protection**: Implemented proper frame protection
- **Coverage**:
  - Content Security Policy compliance
  - HTTP security headers validation
  - SSL/TLS configuration analysis
  - Cross-site scripting detection
  - Injection vulnerability testing

#### ESLint Security Rules

- **Purpose**: Code quality and security rule enforcement
- **Configuration**: `.eslintrc.security.js`
- **Plugins**:
  - `@microsoft/eslint-plugin-sdl`: Microsoft Security Development Lifecycle rules
  - `eslint-plugin-security`: General security rules
  - `eslint-plugin-no-secrets`: Prevents hardcoded secrets

### 2. Dependency Vulnerability Scanning

#### npm audit

- **Purpose**: Identifies known vulnerabilities in npm dependencies
- **Threshold**: Moderate and above severity levels
- **Coverage**: Both client and server dependencies

#### retire.js

- **Purpose**: Detects use of vulnerable JavaScript libraries
- **Output**: JSON reports for automated processing

#### audit-ci

- **Purpose**: Fails CI builds on vulnerable dependencies
- **Configuration**: Moderate severity threshold

### 3. Container Security Scanning

#### Trivy

- **Purpose**: Comprehensive container vulnerability scanner
- **Coverage**:
  - OS package vulnerabilities
  - Application dependencies
  - Configuration issues
  - Secrets detection

#### Docker Bench Security

- **Purpose**: Checks Docker daemon and container configurations
- **Coverage**: CIS Docker Benchmark compliance

## CI/CD Pipeline Integration

### Bitbucket Pipelines Configuration

The security scanning is integrated into three pipeline stages:

1. **Pull Request Pipeline**

   - Runs on every pull request
   - Performs SAST and dependency scanning
   - Provides early feedback to developers

2. **Staging Branch Pipeline**

   - Runs SAST and dependency scanning
   - Executes before deployment to staging environment

3. **Master Branch Pipeline**
   - Comprehensive security scanning including container analysis
   - Runs before production deployment
   - Includes all security tools

### Pipeline Steps

```yaml
# Example pipeline step
- step:
    name: "Security Scanning - SAST"
    script:
      - npm install -g semgrep retire audit-ci
      - semgrep --config=auto --json --output=semgrep-report.json .
      - npm audit --audit-level=moderate
      - retire --outputformat json .
```

## Local Development Usage

### Running Security Scans Locally

```bash
# Run comprehensive security scan
./scripts/security-scan.sh

# Run individual tools
semgrep --config=.semgrep.yml .
npm audit
retire .
```

### Installing Required Tools

```bash
# Install Semgrep
pip3 install semgrep

# Install Node.js security tools
npm install -g retire audit-ci

# Install ESLint security plugins
npm install --save-dev @microsoft/eslint-plugin-sdl eslint-plugin-security eslint-plugin-no-secrets
```

## OWASP Top 10 Coverage

| OWASP Category                   | Detection Method                              | Tools Used                  |
| -------------------------------- | --------------------------------------------- | --------------------------- |
| A01: Broken Access Control       | Path traversal detection, Missing auth checks | Semgrep, ESLint             |
| A02: Cryptographic Failures      | Weak crypto algorithms, Insecure HTTP         | Semgrep, ESLint             |
| A03: Injection                   | SQL injection, XSS, Command injection         | Semgrep, ESLint             |
| A04: Insecure Design             | Code review patterns                          | Semgrep                     |
| A05: Security Misconfiguration   | Container security, Docker bench              | Trivy, Docker Bench         |
| A06: Vulnerable Components       | Dependency scanning                           | npm audit, retire.js, Trivy |
| A07: Authentication Failures     | Hardcoded secrets, Weak auth                  | Semgrep, ESLint             |
| A08: Software Integrity Failures | Dependency integrity                          | npm audit, Trivy            |
| A09: Logging Failures            | Logging patterns                              | Semgrep                     |
| A10: Server-Side Request Forgery | SSRF patterns                                 | Semgrep                     |

## Report Generation

### Automated Reports

All scans generate JSON reports for automated processing:

- `semgrep-report.json`: SAST findings
- `client-retire-report.json`: Client dependency vulnerabilities
- `server-retire-report.json`: Server dependency vulnerabilities
- `trivy-report.json`: Container vulnerabilities
- `eslint-*-report.json`: Code quality and security issues

### Report Artifacts

Bitbucket Pipelines automatically stores scan reports as artifacts, accessible through the pipeline interface.

## Security Thresholds

### Fail Conditions

The pipeline will fail if:

- High or critical severity vulnerabilities are found in dependencies
- SAST tools detect critical security issues
- Container images contain high-severity vulnerabilities

### Warning Conditions

The pipeline will warn but continue if:

- Medium severity vulnerabilities are detected
- Code quality issues are found
- Non-critical security patterns are identified

## Maintenance and Updates

### Regular Tasks

1. **Update Security Rules**: Review and update `.semgrep.yml` monthly
2. **Tool Updates**: Keep scanning tools updated to latest versions
3. **Threshold Review**: Regularly review and adjust security thresholds
4. **False Positive Management**: Maintain allowlists for known false positives

### Monitoring

- Monitor pipeline execution times
- Track security finding trends
- Review scan coverage and effectiveness

## Integration with Development Workflow

### Pre-commit Hooks

Security scanning is integrated with Husky pre-commit hooks:

```bash
# .husky/pre-commit
npm run lint
./scripts/security-scan.sh --quick
```

### IDE Integration

Developers can install ESLint and Semgrep extensions for real-time security feedback.

## Troubleshooting

### Common Issues

1. **Tool Installation Failures**: Ensure proper Node.js and Python versions
2. **False Positives**: Add exceptions to tool configurations
3. **Performance Issues**: Optimize scan scope and caching

### Support

For issues with security scanning:

1. Check tool documentation
2. Review pipeline logs
3. Contact the security team for assistance

## 🔧 Security Fixes Implemented (June 2025)

### ZAP Scan Findings Resolution

Based on the OWASP ZAP security scan that identified 17 security issues, the following fixes have been implemented:

#### **Medium Risk Issues Fixed:**

1. **CSP: Wildcard Directive** ✅

   - **Issue**: CSP contained wildcard (\*) directives allowing any source
   - **Fix**: Removed wildcard directives, specified explicit allowed sources
   - **Location**: `server/app.ts` lines 98-136

2. **CSP: script-src unsafe-inline/unsafe-eval** ✅

   - **Issue**: CSP allowed unsafe-inline and unsafe-eval for scripts
   - **Fix**: Removed unsafe directives, added specific trusted domains
   - **Location**: `server/app.ts` lines 104-110

3. **CSP: style-src unsafe-inline** ✅

   - **Issue**: CSP allowed unsafe-inline styles
   - **Fix**: Maintained minimal unsafe-inline with TODO for nonce-based approach
   - **Location**: `server/app.ts` lines 111-116

4. **Content Security Policy Header Not Set** ✅

   - **Issue**: Missing CSP header on some routes
   - **Fix**: Removed conflicting CSP override in catch-all route
   - **Location**: `server/app.ts` lines 368-372

5. **Missing Anti-clickjacking Header** ✅
   - **Issue**: Missing X-Frame-Options header
   - **Fix**: Added frameguard with "deny" action
   - **Location**: `server/app.ts` lines 84-86

#### **Low Risk Issues Fixed:**

6. **Strict-Transport-Security Header Not Set** ✅

   - **Issue**: Missing HSTS header
   - **Fix**: Added HSTS with 1-year max-age and includeSubDomains
   - **Location**: `server/app.ts` lines 88-92

7. **X-Content-Type-Options Header Missing** ✅

   - **Issue**: Missing X-Content-Type-Options: nosniff
   - **Fix**: Added contentTypeOptions with nosniff
   - **Location**: `server/app.ts` lines 80-82

8. **Server Leaks Information via X-Powered-By** ✅
   - **Issue**: X-Powered-By header revealing server technology
   - **Fix**: Enhanced hidePoweredBy configuration
   - **Location**: `server/app.ts` lines 77-78

#### **Additional Security Enhancements:**

- **Enhanced CSP Directives**: Added base-uri and form-action restrictions
- **Referrer Policy**: Added strict-origin-when-cross-origin policy
- **XSS Protection**: Enhanced XSS filtering configuration
- **DAST Integration**: Created `scripts/dast-scan.sh` for ongoing ZAP scanning

### Running Security Scans

```bash
# Run comprehensive security scan (SAST + dependencies)
npm run security-scan

# Run DAST scan with OWASP ZAP
./scripts/dast-scan.sh https://mopsus-test.altum.ai

# Run quick security test
./scripts/security-test.sh
```

### Next Steps

1. **Monitor CSP Reports**: Implement CSP reporting endpoint for violation monitoring
2. **Nonce-based CSP**: Replace unsafe-inline with nonce-based approach for styles
3. **Regular DAST Scans**: Integrate ZAP scanning into CI/CD pipeline
4. **Security Headers Testing**: Add automated tests for security headers

## Future Enhancements

### Planned Improvements

1. **DAST Integration**: ✅ **COMPLETED** - Added OWASP ZAP scanning capability
2. **Security Dashboards**: Implement centralized security metrics
3. **Automated Remediation**: Auto-create PRs for dependency updates
4. **Advanced Reporting**: Enhanced security reporting and analytics
5. **CSP Reporting**: Implement CSP violation reporting endpoint
6. **Security Headers Testing**: Add automated tests for security headers presence
