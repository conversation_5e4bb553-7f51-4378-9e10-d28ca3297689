# API Implementation Guide

## Authentication

All endpoints except login/signup are protected and require an authentication token in the header:

```http
Authorization: Bearer <token>
```

## Endpoints

### User Management

#### Create User

```http
POST /api/v1/admin/create-user
```

Request body:

```json
{
  "email": "<EMAIL>", // required
  "usagePlanId": "plan123", // required
  "firstName": "John", // optional
  "lastName": "Doe", // optional
  "company": "Example Corp", // optional
  "kvk": "12345" // optional
}
```

Response:

```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "user123",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "company": "Example Corp",
      "kvk": "12345",
      "apiKey": "api-key-value",
      "apiKeyId": "key123",
      "usagePlanId": "plan123",
      "createdAt": "2025-01-31T08:51:43.248Z"
    }
  }
}
```

#### Get Users

```http
GET /api/v1/admin/get-users
```

Response:

```json
{
  "status": "success",
  "data": {
    "users": [
      {
        "id": "user123",
        "email": "<EMAIL>",
        "firstName": "John",
        "lastName": "Doe",
        "company": "Example Corp",
        "kvk": "12345",
        "apiKey": "api-key-value",
        "apiKeyId": "key123",
        "usagePlanId": "plan123",
        "createdAt": "2025-01-31T08:51:43.248Z"
      }
    ]
  }
}
```

#### Get User by ID

```http
GET /api/v1/admin/get-user/:userId
```

Response:

```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "user123",
      "email": "<EMAIL>",
      "firstName": "John",
      "lastName": "Doe",
      "company": "Example Corp",
      "kvk": "12345",
      "apiKey": "api-key-value",
      "apiKeyId": "key123",
      "usagePlanId": "plan123",
      "createdAt": "2025-01-31T08:51:43.248Z"
    }
  }
}
```

#### Delete User

```http
DELETE /api/v1/admin/delete-user/:userId
```

Response:

```json
{
  "status": "success",
  "message": "User deleted successfully"
}
```

#### Migrate Legacy User

```http
POST /api/v1/admin/migrate-user
```

Request body:

```json
{
  "externalApiKey": "existing-api-key", // required
  "email": "<EMAIL>", // required
  "company": "Example Corp", // optional
  "kvk": "12345" // optional
}
```

Response:

```json
{
  "status": "success",
  "data": {
    "user": {
      "id": "user123",
      "email": "<EMAIL>",
      "company": "Example Corp",
      "kvk": "12345",
      "apiKey": "existing-api-key",
      "apiKeyId": "key123",
      "usagePlanId": "plan123",
      "createdAt": "2025-01-31T08:51:43.248Z"
    }
  }
}
```

### Usage Plan Management

#### Get All Usage Plans

```http
GET /api/v1/admin/usage-plans
```

Response:

```json
{
  "status": "success",
  "data": {
    "plans": [
      {
        "id": "plan123",
        "name": "Basic Plan",
        "description": "Basic usage plan",
        "quota": {
          "limit": 1000,
          "period": "MONTH"
        },
        "throttle": {
          "burstLimit": 100,
          "rateLimit": 50
        }
      }
    ]
  }
}
```

#### Get Usage Plan by ID

```http
GET /api/v1/admin/usage-plans/:planId
```

Response:

```json
{
  "status": "success",
  "data": {
    "plan": {
      "id": "plan123",
      "name": "Basic Plan",
      "description": "Basic usage plan",
      "quota": {
        "limit": 1000,
        "period": "MONTH"
      },
      "throttle": {
        "burstLimit": 100,
        "rateLimit": 50
      }
    }
  }
}
```

#### Create Usage Plan

```http
POST /api/v1/admin/usage-plans
```

Request body:

```json
{
  "name": "Basic Plan", // required
  "description": "Basic usage plan", // optional
  "quota": {
    // optional
    "limit": 1000,
    "period": "MONTH" // "DAY" | "WEEK" | "MONTH"
  },
  "throttle": {
    // optional
    "burstLimit": 100,
    "rateLimit": 50
  }
}
```

Response:

```json
{
  "status": "success",
  "data": {
    "plan": {
      "id": "plan123",
      "name": "Basic Plan",
      "description": "Basic usage plan",
      "quota": {
        "limit": 1000,
        "period": "MONTH"
      },
      "throttle": {
        "burstLimit": 100,
        "rateLimit": 50
      }
    }
  }
}
```

#### Update Usage Plan

```http
PATCH /api/v1/admin/usage-plans/:planId
```

Request body:

```json
{
  "name": "Updated Plan", // optional
  "description": "Updated plan", // optional
  "quota": {
    // optional
    "limit": 2000,
    "period": "MONTH" // "DAY" | "WEEK" | "MONTH"
  },
  "throttle": {
    // optional
    "burstLimit": 200,
    "rateLimit": 100
  }
}
```

Response:

```json
{
  "status": "success",
  "data": {
    "plan": {
      "id": "plan123",
      "name": "Updated Plan",
      "description": "Updated plan",
      "quota": {
        "limit": 2000,
        "period": "MONTH"
      },
      "throttle": {
        "burstLimit": 200,
        "rateLimit": 100
      }
    }
  }
}
```

#### Delete Usage Plan

```http
DELETE /api/v1/admin/usage-plans/:planId
```

Response:

```json
{
  "status": "success",
  "message": "Usage plan deleted successfully"
}
```

#### Add Test Credit to User

```http
POST /api/v1/admin/user/:userId/test-credit
```

Adds test credit to a specific user by adjusting their API key's usage record for the current month. The credit is applied only to the specified user's API key and does not affect other users sharing the same usage plan.

Request body:

```json
{
  "testCredit": 1000 // required, positive integer
}
```

Response:

```json
{
  "status": "success",
  "message": "Successfully added 1000 test credits to user user123"
}
```

### Analytics

#### Get API Usage Overview

```http
GET /api/v1/admin/api-usage/overview?apiKey=<apiKey>&startDate=<startDate>&endDate=<endDate>
```

Query parameters:

- apiKey: The API key to get usage for
- startDate: Start date in ISO format (YYYY-MM-DD)
- endDate: End date in ISO format (YYYY-MM-DD)

Response:

```json
{
  "status": "success",
  "data": {
    "usage": {
      "totalRequests": 1000,
      "successfulRequests": 950,
      "failedRequests": 50,
      "averageLatency": 200,
      "period": {
        "start": "2025-01-01",
        "end": "2025-01-31"
      },
      "byEndpoint": [
        {
          "path": "/api/v1/endpoint",
          "requests": 500,
          "successRate": 95
        }
      ]
    }
  }
}
```

#### Get Mopsus Analytics

```http
GET /api/v1/admin/analytics/:date
```

Response:

```json
{
  "status": "success",
  "data": {
    "analytics": {
      "totalApiCalls": 1000,
      "uniqueUsers": 50,
      "averageResponseTime": 200,
      "date": "2025-01-31",
      "byEndpoint": [
        {
          "endpoint": "/api/v1/endpoint",
          "calls": 500
        }
      ]
    }
  }
}
```

#### Get Line Chart Analytics

```http
GET /api/v1/admin/analytics/line/:start/:end
```

Response:

```json
{
  "status": "success",
  "data": {
    "analytics": {
      "labels": ["2025-01-01", "2025-01-02"],
      "datasets": [
        {
          "label": "API Calls",
          "data": [1000, 1200]
        },
        {
          "label": "Unique Users",
          "data": [50, 55]
        }
      ]
    }
  }
}
```

## Error Responses

All endpoints follow the same error response format:

```json
{
  "status": "error",
  "message": "Error description",
  "code": 400 // HTTP status code
}
```

Common error codes:

- 400: Bad Request - Invalid input data
- 401: Unauthorized - Authentication required
- 403: Forbidden - Insufficient permissions
- 404: Not Found - Resource not found
- 409: Conflict - Resource already exists
- 500: Internal Server Error - Server-side error

## Frontend Implementation Guide

### Authentication

1. Store the JWT token securely after login
2. Include the token in all API requests:

```typescript
const headers = {
  Authorization: `Bearer ${token}`,
  "Content-Type": "application/json",
};
```

### User Management

1. User creation form should validate:

   - Email (required)
   - Usage plan ID (required)
   - Other fields optional

2. Display user information in a table/grid with:

   - Filtering capabilities
   - Sorting options
   - Pagination
   - Actions (delete, edit)

3. User deletion should show confirmation dialog

### Usage Plan Management

1. Usage plan form should include:

   - Name field (required)
   - Optional description
   - Quota settings (optional)
     - Limit input
     - Period selector (DAY/WEEK/MONTH)
   - Throttle settings (optional)
     - Burst limit input
     - Rate limit input

2. Display usage plans in a table/grid with:
   - Filtering
   - Sorting
   - Actions (edit, delete)

### Analytics Dashboard

1. Overview section showing:

   - Total API calls
   - Unique users
   - Average response time
   - Success/failure rates

2. Line charts for:

   - API usage trends
   - User growth
   - Response times

3. Date range selector for:
   - Predefined ranges (today, week, month)
   - Custom date range picker

### Error Handling

1. Display error messages from API responses
2. Implement retry logic for failed requests
3. Show loading states during API calls
4. Handle network errors gracefully

### State Management

1. Store user session data
2. Cache frequently accessed data
3. Implement optimistic updates
4. Handle loading and error states

### Responsive Design

1. Support mobile and desktop layouts
2. Implement responsive tables/grids
3. Optimize forms for mobile input

### Performance Considerations

1. Implement pagination for large datasets
2. Cache API responses where appropriate
3. Use debouncing for search/filter inputs
4. Lazy load components and routes

### Security Best Practices

1. Sanitize user inputs
2. Implement CSRF protection
3. Use secure storage for sensitive data
4. Implement session timeout handling
