import React from "react";
import { Redirect } from "react-router";
import { useAppSelector } from "../../../redux/hooks";
import MoveForm from "./MoveForm";
import Loading from "../../Loading";
import { FormProvider } from "../../../pages/Api/usage-pages/components/FormContext";
const Move = () => {
  const { loading, result } = useAppSelector((state) => state.moveData);

  if (loading) {
    return <Loading />;
  }

  return (
    <FormProvider>
      {Object.keys(result).length > 0 ? (
        <Redirect
          to={{
            pathname: "/verhuisdata-result",
          }}
        />
      ) : (
        <MoveForm />
      )}
    </FormProvider>
  );
};

export default Move;
