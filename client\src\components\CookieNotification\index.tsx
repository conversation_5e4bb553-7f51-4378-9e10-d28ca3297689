import React, { useState, useEffect } from "react";
import styled from "styled-components";

const <PERSON>ieWrapper = styled.div`
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #f8f9fa;
  padding: 1rem;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  border-top: 1px solid #dee2e6;
`;

const <PERSON>ieContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;

  @media (max-width: 768px) {
    flex-direction: column;
    text-align: center;
  }
`;

const CookieText = styled.p`
  margin: 0;
  font-size: 0.9rem;
  color: #495057;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 1rem;

  @media (max-width: 768px) {
    flex-direction: column;
    width: 100%;
  }
`;

const Button = styled.button<{ primary?: boolean }>`
  padding: 0.5rem 1rem;
  border-radius: 4px;
  border: 1px solid ${(props) => (props.primary ? "#007bff" : "#6c757d")};
  background-color: ${(props) => (props.primary ? "#007bff" : "transparent")};
  color: ${(props) => (props.primary ? "#fff" : "#6c757d")};
  cursor: pointer;
  transition: all 0.2s;
  min-width: 120px;

  &:hover {
    background-color: ${(props) => (props.primary ? "#0056b3" : "#e9ecef")};
  }

  @media (max-width: 768px) {
    width: 100%;
  }
`;

const Link = styled.a`
  color: #007bff;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
`;

const CookieNotification: React.FC = () => {
  const [show, setShow] = useState(false);

  useEffect(() => {
    const hasAccepted = localStorage.getItem("cookieConsent");
    if (!hasAccepted) {
      setShow(true);
    }
  }, []);

  const acceptCookies = () => {
    localStorage.setItem("cookieConsent", "true");
    setShow(false);
  };

  const declineCookies = () => {
    localStorage.setItem("cookieConsent", "false");
    setShow(false);
    // Here you could add logic to disable non-essential cookies
  };

  if (!show) return null;

  return (
    <CookieWrapper>
      <CookieContent>
        <CookieText>
          Wij gebruiken cookies om je de beste ervaring op onze website te
          bieden. Lees meer over ons cookiegebruik in ons{" "}
          <Link href="/privacy-policy">privacybeleid</Link>.
        </CookieText>
        <ButtonGroup>
          <Button onClick={declineCookies}>Afwijzen</Button>
          <Button primary onClick={acceptCookies}>
            Accepteren
          </Button>
        </ButtonGroup>
      </CookieContent>
    </CookieWrapper>
  );
};

export default CookieNotification;
