import React from "react";
import { Link } from "react-router-dom";
import Subtitle from "../../../../components/Subtitle";
import Text from "../../../../components/Text";
import { <PERSON><PERSON>ey, FiBarChart2, FiFileText, FiHelpCircle } from "react-icons/fi";

const QuickActions: React.FC = () => {
  const actions = [
    {
      icon: <FiKey className="text-yellow-500" />,
      title: "Toegang API key",
      description: "Vind en beheer jouw API key",
      link: "/dashboard/account",
      isExternal: false,
    },
    {
      icon: <FiBarChart2 className="text-blue-500" />,
      title: "View usage stats",
      description: "<PERSON><PERSON> meer over jouw gebruik",
      link: "/dashboard/analytics",
      isExternal: false,
    },
    {
      icon: <FiFileText className="text-green-500" />,
      title: "Ga to documentatie",
      description: "Leer hoe te starten met API",
      link: "https://docs.altum.ai/",
      isExternal: true,
    },
    // {
    //   icon: <FiHelpCircle className="text-purple-500" />,
    //   title: "Need help?",
    //   description: "Neem contact op met support",
    //   link: "/dashboard/support",
    //   isExternal: false,
    // },
  ];

  return (
    <div className="w-full flex flex-col items-start gap-4 shadow-[0px_0px_4px_0px_#********] rounded-lg p-4">
      <Subtitle className="text-base">Quick Actions</Subtitle>

      <div className="w-full space-y-2">
        {actions.map((action, index) =>
          action.isExternal ? (
            <a
              key={index}
              href={action.link}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors w-full"
            >
              <div className="bg-gray-100 rounded-full p-2 mr-3">
                {action.icon}
              </div>
              <div className="flex-1">
                <Text className="font-medium text-gray-800">
                  {action.title}
                </Text>
                <Text className="text-sm text-gray-500">
                  {action.description}
                </Text>
              </div>
              <div className="text-gray-400">
                <svg
                  width="6"
                  height="10"
                  viewBox="0 0 6 10"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M1 9L5 5L1 1"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            </a>
          ) : (
            <Link
              key={index}
              to={action.link}
              className="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors w-full"
            >
              <div className="bg-gray-100 rounded-full p-2 mr-3">
                {action.icon}
              </div>
              <div className="flex-1">
                <Text className="font-medium text-gray-800">
                  {action.title}
                </Text>
                <Text className="text-sm text-gray-500">
                  {action.description}
                </Text>
              </div>
              <div className="text-gray-400">
                <svg
                  width="6"
                  height="10"
                  viewBox="0 0 6 10"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M1 9L5 5L1 1"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            </Link>
          ),
        )}
      </div>
    </div>
  );
};

export default QuickActions;
