# Analytics Implementation Setup

This document explains how to set up and use the new analytics features.

## Overview

The analytics implementation provides detailed tracking and metrics for:
- Active Users & Trends
- New User Signups
- Property AI Generations
- Onboarding Funnel Analysis

## Database Setup

1. Run the analytics migration:
```bash
npm run migrate -- --file=update_analytics_tables
```

This will:
- Create the analytics_events table
- Set up necessary indexes for performance
- Create materialized views for efficient querying
- Create database functions for analytics calculations

## Initializing Analytics

Run the analytics setup script to initialize the system and backfill historical data:

```bash
npm run setup-analytics
```

This will:
- Backfill user signup events
- Backfill email verification events
- Backfill onboarding completion events
- Backfill property generation events
- Initialize materialized views

## Event Tracking

Events are automatically tracked through analytics hooks in various controllers:

### User Events
- User Signup: When a new user registers
- Email Verification: When user verifies their email
- User Login: When user signs in
- User Activity: Various user actions

### Onboarding Events
- Onboarding Response: When user answers onboarding questions
- Onboarding Step Completion: When user completes an onboarding step
- Onboarding Complete: When full onboarding is completed

### Property Generation Events
- Property Generation: When AI generates property descriptions

## Accessing Analytics

Analytics data can be accessed through these endpoints:

### General Analytics
```
GET /api/v1/admin/analytics/mopsus
```
Returns combined metrics for active users, new users, property generations, and onboarding funnel.

### Detailed Analytics
```
GET /api/v1/admin/analytics/detailed?type=<metric-type>&timeframe=<timeframe>
```
- metric-type: active-users | new-users | property-generations | onboarding-funnel
- timeframe: 24h | 7d | 30d | 90d

### Line Chart Analytics
```
GET /api/v1/admin/analytics/mopsus/chart
```
Returns time-series data for visualization.

## Testing

Run the analytics tests:
```bash
npm test tests/analytics.test.ts
```

## Monitoring

The materialized view `analytics_events_daily` is automatically refreshed when new events are added. You can manually refresh it using:

```sql
REFRESH MATERIALIZED VIEW CONCURRENTLY analytics_events_daily;
```

## Adding New Analytics

To add tracking for new events:

1. Use the AnalyticsHooks service:
```typescript
await AnalyticsHooks.onUserActivity(userId, "your_event_type");
```

2. Or directly log events:
```typescript
await AnalyticsLoggingService.logEvent({
  event_type: "your_event_type",
  user_id: userId,
  metadata: { your: "metadata" }
});
```

## Maintenance

The analytics_events table is indexed for efficient querying, but you may want to:
- Monitor table size and implement partitioning if needed
- Set up data retention policies for old events
- Schedule regular materialized view refreshes

## Troubleshooting

Common issues:

1. Missing events:
   - Check the controller has proper analytics hooks
   - Verify event logging is not failing silently

2. Incorrect metrics:
   - Ensure timeframes are correctly specified
   - Check for timezone mismatches
   - Verify materialized view is up to date

3. Performance issues:
   - Check index usage
   - Verify materialized view refresh frequency
   - Monitor query execution plans