# AVM Service Separation Plan

## Overview
This document outlines the plan for separating and clearly distinguishing between AVM and AVM+ services in our platform.

## Changes Required

### 1. Update the Existing AVM+ Service

**File Affected:**  
`client/src/pages/Api/usage-pages/avmplus/data.ts`

**Changes:**
- Rename API Title from "Geautomatiseerde modelwaardering" to "Woningwaarde+ API"
- (Optional) Update description to reference AVM+ endpoint usage

### 2. Create a New AVM Service

**New Folder Structure:**  
`client/src/pages/Api/usage-pages/avm/`

**Files to Create:**
- `data.ts` - Main configuration file for AVM service

**Content Requirements:**

#### API Title and Description
```typescript
title: "Woningwaarde API"
description: "Authentisatie, invoer en resultaat\nPlaats objectgegevens om een nauwkeurige geautomatiseerde modelwaardering te ontvangen.\nPOST https://api.altum.ai/avm\n\nMet dit eindpunt kunt u antwoord ontvangen van het Altum AI AVM-model."
```

#### Method and URL
```typescript
method: "POST"
url: "https://api.altum.ai/avm"
```

#### Headers Configuration
- Content-Type: application/json
- x-api-key: Unique API key from Mopsus

#### Request Body Fields
- postcode* (string)
- housenumber* (number)
- houseaddition (string)
- valuationdate (string)
- image (boolean)
- buildyear (integer)
- innersurfacearea (integer)
- outersurfacearea (integer)
- energylabel (string)
- housetype (string)

#### Response Codes
- 200: OK Succesvolle reactie
- 400: Bad Request Mislukte reactie
- 401: Unauthorized Geen toegang
- 403: Forbidden Verboden
- 500: Internal Server Error Service is niet beschikbaar en/of niet beschikbaar
- 422: Unprocessable Entity Verkeerd invoerformaat
- 429: Too Many Requests API-sleutellimiet overschreden

## Implementation Flow

```mermaid
flowchart TD
    A[Update AVM+ Data File]
    B[Change title to "Woningwaarde+ API"]
    C[(Optional) Update description]
    D[Create New Folder: client/src/pages/Api/usage-pages/avm/]
    E[Create new data.ts file for AVM]
    F[Define API Title "Woningwaarde API"]
    G[Set URL to https://api.altum.ai/avm]
    H[Configure Headers, Request Body, Responses]

    A --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
```

## Implementation Steps

1. **Modify AVM+ configuration**
   - Update the `avmEndpoint` object in the existing file
   - Change title to reflect AVM+ service
   - Review and update description if needed

2. **Create AVM Configuration**
   - Create new directory structure
   - Implement new `data.ts` file with AVM-specific configuration
   - Configure all necessary fields and responses as documented
   - Ensure proper error handling and response codes

## Expected Outcome
- Clear distinction between AVM and AVM+ services
- Properly named and documented API endpoints
- Consistent structure across both services
- Accurate representation of each service's capabilities