# Security Remediation Plan for mopsus.altum.ai

This document outlines the recommended changes to mitigate the vulnerabilities identified by the ZAP report. The primary focus is on strengthening the security headers in the Express application (in `server/app.ts`) and in the Nginx configuration (`nginx.conf`).

---

## 1. Express Application (`server/app.ts`)

### Recommendations

1. **Helmet Configuration:**
   - **Enhance Inline Style Protection:**  
     Replace the current use of `'unsafe-inline'` for `styleSrc` with a more secure, nonce-based approach.  
     **Current Configuration:**
     ```javascript
     styleSrc: [
       "'self'",
       "'unsafe-inline'", // TODO: Replace with nonce-based approach
       "https://fonts.googleapis.com",
     ],
     ```
     **Recommendation:**  
     Generate a nonce for each request and use it in your inline styles. Update your CSP to reference this nonce.

2. **Header Removals:**
   - Ensure that sensitive headers such as `X-Powered-By` and any server-identifying headers are removed. This is partly handled by <PERSON><PERSON>et's `hidePoweredBy` option.
   - Optionally add custom middleware to remove any additional headers if needed.

3. **Error Handling:**
   - Review error handling to ensure stack traces or debug information are not exposed to clients.

### Implementation Steps

1. **Implement Nonce Generation:**
   - Generate a unique nonce for each request.
   - Attach it to `res.locals` (for example: `res.locals.nonce`).

2. **Update Helmet CSP Configuration:**
   - In the Helmet configuration, set `styleSrc` to include the nonce:
     ```javascript
     app.use(
       helmet.contentSecurityPolicy({
         directives: {
           defaultSrc: ["'self'"],
           scriptSrc: [
             "'self'",
             "https://js.stripe.com",
             "https://www.googletagmanager.com",
             "https://www.google-analytics.com",
           ],
           styleSrc: [
             "'self'",
             (req, res) => `'nonce-${res.locals.nonce}'`,
             "https://fonts.googleapis.com",
           ],
           imgSrc: ["'self'", "data:", "https:"],
           connectSrc: [
             "'self'",
             "https://api.mopsus.altum.ai",
             "https://api.stripe.com",
             "https://www.google-analytics.com",
           ],
           fontSrc: ["'self'", "https://fonts.gstatic.com", "data:"],
           objectSrc: ["'none'"],
           mediaSrc: ["'self'"],
           frameSrc: ["'self'", "https://js.stripe.com"],
           baseUri: ["'self'"],
           formAction: ["'self'"],
         },
         reportOnly: false,
       })
     );
     ```
3. **Validate Other Helmet Settings:**
   - Confirm settings for HSTS, referrer policy, and XSS protection remain intact.

---

## 2. Nginx Configuration (`nginx.conf`)

### Recommendations

1. **Security Headers:**
   - Ensure that headers added in the Nginx configuration align with those in the Express app.
   - Key headers include:
     - `X-Frame-Options` set to `DENY`
     - `X-Content-Type-Options` set to `nosniff`
     - `Strict-Transport-Security` with a max-age of 31536000 seconds, including subdomains and preload enabled
     - Updated Content Security Policy that mirrors the Express app configuration

2. **Server Tokens:**
   - Continue using `server_tokens off;` to hide server version information.

### Implementation Steps

1. **Review and Update the CSP Header if Needed:**
   - Validate that the CSP header in the Nginx block remains up-to-date with the Express configuration, especially if a nonce-based policy is implemented on the app side. Note that nonce values are generated in the application and cannot be set in a static Nginx config.
   - If needed, use the Nginx configuration to provide a baseline CSP for static content served by Nginx.

2. **Maintain Rate Limiting and Logging Settings:**
   - Keep the existing rate limiting zones and logging settings as they do not conflict with the new security headers.

---

## Next Steps

1. **Review This Document:**
   - Confirm that the plan meets your requirements.
   - Provide any additional instructions or modifications if required.

2. **Implement Changes:**
   - Transition to Code mode for making the necessary changes in `server/app.ts` and review the `nginx.conf` if further adjustments are needed.
   - Re-run the ZAP scan after implementing these changes to verify that the vulnerabilities have been mitigated.

---

*This document has been generated as part of the security remediation planning process for mopsus.altum.ai. Please switch to code mode when ready to implement these changes.*