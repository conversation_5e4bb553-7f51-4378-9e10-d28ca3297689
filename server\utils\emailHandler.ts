import sgMail, { <PERSON><PERSON><PERSON>Required } from "@sendgrid/mail";
import {
  SEND<PERSON><PERSON>_RENEW_PPU_ID,
  SENDGRID_RENEW_FREE_TIER_ID,
  SENDGRID_ACC_VERIFIED_ID,
  SENDGRID_CONFIRM_SIGNUP_ID,
  SENDGRID_SUB_BOUGHT_ID,
  SENDGRID_SUB_CANCELLED_ID,
  SENDGRID_CREDIT_DEPLETED,
  SENDGRID_CREDIT_DEPLETED_YEARLY,
  SENDGRID_CHANGE_PASSWORD_ID,
  SEND<PERSON>ID_FAILED_INVOICE_ID,
  SENDGRID_INVOICE_ID,
  SENDGRID_ANALYTICS_ID,
  SENDGRID_LOGIN_CREDENTIALS_ID,
  SENDGRID_OTP,
  SENDGRID_2FA,
} from "./constants";
import { User } from "../@types";
import { generateUnsubscribeToken } from "./unsubscribeUtils";
import ChartJsImage from "chartjs-to-image";

export default class Email {
  to: string;
  firstName: string;
  lastName: string;
  url: string;
  from: string;
  unsubscribe: Promise<string>;
  constructor(user: User, url?: string) {
    this.to = user.email;
    this.firstName = user.first_name;
    this.lastName = user.last_name;
    this.url = url || "";
    this.from = process.env.SENDGRID_EMAIL!;
    this.newTransport();
    this.unsubscribe = this.generateUnsubscribeUrl(user.email);
  }

  private async generateUnsubscribeUrl(email: string): Promise<string> {
    const token = await generateUnsubscribeToken(email);
    return `https://mopsus.altum.ai/unsubscribe?email=${email}&token=${token}`;
  }

  newTransport() {
    sgMail.setApiKey(process.env.SENDGRID_APIKEY!);
  }

  async send(
    templateId: string,
    dynamicTemplateData?: Record<string, string | any>,
  ) {
    // Define email options
    const mailOptions: MailDataRequired = {
      from: { name: "Altum AI", email: this.from },
      templateId,
      personalizations: [{ to: { email: this.to }, dynamicTemplateData }],
    };

    // Create a transport and send email
    await sgMail.send(mailOptions);
  }

  async changePassword() {
    await this.send(SENDGRID_CHANGE_PASSWORD_ID, {
      url: this.url,
    });
  }

  async verifyAccount() {
    await this.send(SENDGRID_CONFIRM_SIGNUP_ID, {
      url: this.url,
      unsubscribe: await this.unsubscribe,
    });
  }

  async verifyEmail(code: string) {
    await this.send(SENDGRID_OTP, {
      code,
      unsubscribe: await this.unsubscribe,
    });
  }

  async send2FACode(code: string) {
    await this.send(SENDGRID_2FA, {
      code,
      unsubscribe: await this.unsubscribe,
      message: "Your two-factor authentication code",
    });
  }

  async accountVerified() {
    await this.send(SENDGRID_ACC_VERIFIED_ID, {
      url: this.url,
      unsubscribe: await this.unsubscribe,
    });
  }

  async sendApiUsageReached(
    usagePlan: string,
    credits: number,
    remainingCredits: number,
  ) {
    await this.send(SENDGRID_CREDIT_DEPLETED, {
      usagePlan,
      credits,
      remainingCredits,
      unsubscribe: await this.unsubscribe,
    });
  }

  async sendApiUsageYearlyReached(
    usagePlan: string,
    credits: number,
    remainingCredits: number,
  ) {
    await this.send(SENDGRID_CREDIT_DEPLETED_YEARLY, {
      usagePlan,
      credits,
      remainingCredits,
      unsubscribe: await this.unsubscribe,
    });
  }

  async sendMonthlyResetApiUsage(usagePlan: string, count: number) {
    await this.send(SENDGRID_RENEW_PPU_ID, {
      firstname: this.firstName,
      usagePlan,
      count,
      unsubscribe: await this.unsubscribe,
    });
  }

  async sendMonthlyResetApiUsageFreeTier() {
    await this.send(SENDGRID_RENEW_FREE_TIER_ID, {
      unsubscribe: await this.unsubscribe,
    });
  }

  async subscriptionBought(usagePlan: string) {
    await this.send(SENDGRID_SUB_BOUGHT_ID, {
      firstname: this.firstName,
      usagePlan,
      unsubscribe: await this.unsubscribe,
    });
  }

  async subscriptionCancelled(usagePlan: string) {
    await this.send(SENDGRID_SUB_CANCELLED_ID, {
      firstname: this.firstName,
      usagePlan,
      unsubscribe: await this.unsubscribe,
    });
  }

  async send4xxEmail(id: string) {
    await this.send(id);
  }

  async sendPaymentReceipt() {
    await this.send(SENDGRID_INVOICE_ID, {
      url: this.url,
      unsubscribe: await this.unsubscribe,
    });
  }

  async sendFailedPayment() {
    await this.send(SENDGRID_FAILED_INVOICE_ID, {
      url: this.url,
      unsubscribe: await this.unsubscribe,
    });
  }

  async sendLoginCredentials(email: string, password: string) {
    await this.send(SENDGRID_LOGIN_CREDENTIALS_ID, {
      email,
      password,
      url: "https://mopsus.altum.ai/signin",
      unsubscribe: await this.unsubscribe,
    });
  }

  async sendAnalytics(image: any) {
    await this.send(SENDGRID_ANALYTICS_ID, {
      image: image,
      unsubscribe: await this.unsubscribe,
    });
  }
}
