import React from "react";
import { FiAlertCircle, FiX } from "react-icons/fi";
import { Link } from "react-router-dom";
import { useAppSelector } from "../../../../redux/hooks";
import { getCredits, getPpu } from "../../../../helpers/stripeHelper";

interface UsageQuotaBannerProps {
  onClose: () => void;
}

const UsageQuotaBanner: React.FC<UsageQuotaBannerProps> = ({ onClose }) => {
  const { usage } = useAppSelector((state) => state.apiUsage);
  const { comingInvoice } = useAppSelector((state) => state.portal);

  // Get plan name from portal state
  const planName = comingInvoice?.product?.name || usage.plan || "";

  // Get total credits from the plan
  const totalCredits = getCredits(planName) || 0;

  // Calculate usage percentage
  const usedCredits = Number(usage.used) || 0;
  const usagePercentage =
    Number(totalCredits) > 0
      ? Math.round((usedCredits / Number(totalCredits)) * 100)
      : 0;

  // Calculate days left in the month
  const today = new Date();
  const daysInMonth = new Date(
    today.getFullYear(),
    today.getMonth() + 1,
    0,
  ).getDate();
  const daysLeft = daysInMonth - today.getDate();

  // Get cost per extra call from the plan
  const costPerExtraCall = getPpu(planName) || 0.47;

  // Only show the banner if usage is over 80%
  if (usagePercentage < 80) {
    return null;
  }

  return (
    <div className="w-full bg-orange-50 border border-orange-100 rounded-lg p-4 mb-6 flex items-center justify-between">
      <div className="flex items-center">
        <div className="bg-orange-100 rounded-full p-2 mr-3">
          <FiAlertCircle className="text-orange-600" size={16} />
        </div>
        <div>
          <div className="text-gray-800 font-medium">
            Huidig verbruik: {usedCredits}/{totalCredits} API calls (
            {usagePercentage}% quota gebruikt)
          </div>
          <div className="text-gray-600 text-sm">
            Extra calls na quota kosten €{costPerExtraCall.toFixed(2)} per stuk.
            Tijd om op te schalen?
          </div>
        </div>
      </div>

      <div className="flex items-center">
        <Link
          to="/dashboard/abbonement"
          className="text-orange-600 hover:text-orange-700 text-sm font-medium mr-4 hover:underline"
        >
          Bekijk plannen
        </Link>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-500"
          aria-label="Melding sluiten"
        >
          <FiX size={20} />
        </button>
      </div>
    </div>
  );
};

export default UsageQuotaBanner;
