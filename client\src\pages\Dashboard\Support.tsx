import { useEffect, useState, ChangeEvent, FormEvent, FC } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faTty } from "@fortawesome/free-solid-svg-icons";
import { Helmet } from "react-helmet";
import { toast } from "react-toastify";
import { useAppSelector } from "../../redux/hooks";
import sendSupportEmail from "../../helpers/sendSupportEmail";

interface SupportFields {
  email: string;
  subject: string;
  text: string;
}

const Support: FC = () => {
  const user = useAppSelector((state) => state.auth.user);
  const [supportFields, setSupportFields] = useState<SupportFields>({
    email: "",
    subject: "",
    text: "",
  });

  const [emailSent, setEmailSent] = useState(false);

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    setSupportFields({ ...supportFields, [e.target.name]: e.target.value });
  };

  const handleTextAreaChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setSupportFields({ ...supportFields, text: e.target.value });
  };

  const handleSupportFormSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const { email, subject, text } = supportFields;
    if (
      email?.trim().length === 0 ||
      subject.trim().length === 0 ||
      text.trim().length === 0
    ) {
      toast.error("Vul alle velden in om het bericht te versturen");
    } else {
      const data = { email, subject, text };
      sendSupportEmail(data).then((sentEmail) => {
        sentEmail && setEmailSent(sentEmail);
        user &&
          setSupportFields({
            email: user.email,
            subject: "",
            text: "",
          });
      });
    }
  };

  return (
    <>
      <Helmet>
        <title> Support - Altum AI</title>
      </Helmet>

      <div className="dashboard-container">
        <div className="dashboard-title-box" />
        <div className="dashboard-sections-box">
          <div className="dashboard-content-section">
            <div className="dashboard-content-title-box">
              <h2 id="dashboard-content-title">
                <FontAwesomeIcon
                  icon={faTty}
                  className="dashboard-content-intro-title-svg"
                />
                <span>Support</span>
              </h2>
            </div>

            <div className="dashboard-content-container">
              <div className="support-div">
                {!emailSent ? (
                  <form
                    className="support-form-div"
                    onSubmit={handleSupportFormSubmit}
                  >
                    <div className="form-group">
                      <label>Email</label>
                      <input
                        type="email"
                        name="email"
                        placeholder="email"
                        disabled
                        value={supportFields.email}
                      />
                    </div>
                    <div className="form-group">
                      <label>Onderwerp</label>
                      <input
                        type="text"
                        name="subject"
                        placeholder="ABC-Issue"
                        autoComplete="off"
                        value={supportFields.subject}
                        onChange={handleChange}
                      />
                    </div>
                    <div className="form-group">
                      <label>Bericht</label>
                      <textarea
                        name="text"
                        value={supportFields.text}
                        onChange={handleTextAreaChange}
                      />
                    </div>
                    <input
                      type="submit"
                      value="Versturen"
                      className="btn btn-primary"
                    />
                  </form>
                ) : (
                  <p className="support-email-sent-text">
                    Uw e-mail is verzonden. We nemen zo snel mogelijk contact
                    met u op.
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Support;
