# ZAP Scanning Rules for DAST
# Format: RULE_ID	THRESHOLD	[COMMENT]

# Ignore false positives for development/staging environments
10021	OFF	# X-Content-Type-Options Header Missing
10020	OFF	# X-Frame-Options Header Not Set (handled by <PERSON><PERSON><PERSON>)
10016	OFF	# Web Browser XSS Protection Not Enabled (handled by <PERSON><PERSON><PERSON>)

# Enable important security checks
10023	MEDIUM	# Information Disclosure - Debug Error Messages
10024	MEDIUM	# Information Disclosure - Sensitive Information in URL
10025	HIGH	# Information Disclosure - Sensitive Information in HTTP Referrer Header
10026	HIGH	# HTTP Parameter Override
10027	MEDIUM	# Information Disclosure - Suspicious Comments
10028	MEDIUM	# Open Redirect
10029	HIGH	# Cookie Poisoning
10030	HIGH	# User Controllable Charset
10031	HIGH	# User Controllable HTML Element Attribute (Potential XSS)
10032	HIGH	# Viewstate Scanner
10033	MEDIUM	# Directory Browsing
10034	HIGH	# Heartbleed OpenSSL Vulnerability
10035	HIGH	# Strict-Transport-Security Header Not Set
10036	MEDIUM	# HTTP Server Response Header
10037	MEDIUM	# Server Leaks Information via "X-Powered-By" HTTP Response Header Field(s)
10038	MEDIUM	# Content Security Policy (CSP) Header Not Set
10039	MEDIUM	# X-Backend-Server Header Information Leak
10040	MEDIUM	# Secure Pages Include Mixed Content
10041	MEDIUM	# HTTP to HTTPS Insecure Transition in Form Post
10042	MEDIUM	# HTTPS to HTTP Insecure Transition in Form Post
10043	MEDIUM	# User Controllable JavaScript Event (XSS)
10044	HIGH	# Big Redirect Detected (Potential Sensitive Information Leak)
10045	HIGH	# Source Code Disclosure - /WEB-INF folder
10046	HIGH	# Source Code Disclosure - /META-INF folder
10047	HIGH	# Source Code Disclosure - Java
10048	HIGH	# Remote Code Execution - Shell Shock
10049	HIGH	# Content Cacheability
10050	MEDIUM	# Retrieved from Cache
10051	MEDIUM	# Relative Path Confusion
10052	MEDIUM	# X-ChromeLogger-Data (XCOLD) Header Information Leak
10053	MEDIUM	# Apache Range Header DoS (CVE-2011-3192)
10054	HIGH	# Cookie Without Secure Flag
10055	HIGH	# CSP Scanner
10056	MEDIUM	# X-Debug-Token Information Leak
10057	MEDIUM	# Username Hash Found
10058	MEDIUM	# GET for POST
10059	MEDIUM	# X-AspNet-Version Response Header
10060	MEDIUM	# X-AspNetMvc-Version Response Header
10061	HIGH	# X-Powered-By Response Header
10062	HIGH	# PII Disclosure
10063	MEDIUM	# Feature Policy Header Not Set
10064	MEDIUM	# Timestamp Disclosure
10065	MEDIUM	# WSDL File Passive Scanner
10066	MEDIUM	# HTTP Parameter Pollution
10067	MEDIUM	# Httpoxy - Proxy Header Misuse
10068	MEDIUM	# SSI Injection
10069	MEDIUM	# Advanced SQL Injection
10070	HIGH	# CRLF Injection
10071	HIGH	# LDAP Injection
10072	HIGH	# NoSQL Injection
10073	HIGH	# Path Traversal
10074	HIGH	# Remote File Inclusion
10075	HIGH	# Server Side Include
10076	HIGH	# SQL Injection
10077	HIGH	# XML External Entity Attack
10078	HIGH	# XPath Injection
10079	HIGH	# XSS (Cross Site Scripting)
10080	HIGH	# XSS (Persistent)
10081	HIGH	# XSS (Reflected)
