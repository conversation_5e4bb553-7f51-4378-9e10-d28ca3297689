## Sample `analytics_logger` Table

This document provides a sample of the `analytics_logger` table, showcasing the structure and data it contains.

| id  | uuid                                 | event             | category        | date       | time          | created_at         |
| --- | ------------------------------------ | ----------------- | --------------- | ---------- | ------------- | ----------------- |
| 1   | 550e8400-e29b-41d4-a716-************ | Signin            | Authentication  | 2024-01-23 | 09:18:23.973 | 2024-01-23 09:18:23.973 |
| 2   | 550e8400-e29b-41d4-a716-************ | Signup            | Authentication  | 2024-01-22 | 14:33:30.627 | 2024-01-22 14:33:30.627 |
| 3   | 550e8400-e29b-41d4-a716-************ | PropertyGenerated | Property        | 2024-01-21 | 10:22:11.555 | 2024-01-21 10:22:11.555 |
| 4   | 550e8400-e29b-41d4-a716-************ | OnboardingStep    | Onboarding      | 2024-01-20 | 15:08:39.438 | 2024-01-20 15:08:39.438 |
| 5   | 550e8400-e29b-41d4-a716-************ | APIRequest        | API             | 2024-01-19 | 07:10:22.662 | 2024-01-19 07:10:22.662 |

**Column Descriptions:**

*   **id:** (INT, Primary Key) - Unique identifier for the log entry.
*   **uuid:** (UUID, Auto-generated) - Universally unique identifier automatically generated by the database for each event.
*   **event:** (TEXT) - The specific event that was logged (e.g., "Signin", "Signup", "PropertyGenerated").
*   **category:** (TEXT) - The category the event belongs to (e.g., "Authentication", "Property", "Onboarding").
*   **date:** (DATE) - The date the event occurred.
*   **time:** (TIME) - The time the event occurred.
*   **created_at:** (TIMESTAMP, Auto-generated) - The timestamp automatically generated by the database when the event is created.

This table is used for legacy analytics and is maintained for backward compatibility. New analytics data is primarily stored in the `analytics_events` table.