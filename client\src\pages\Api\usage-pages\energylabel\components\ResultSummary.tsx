import { FC, useState } from "react";
import Text from "../../../../../components/Text";
import Subtitle from "../../../../../components/Subtitle";
import Button from "../../../../../components/Button";
import Modal from "../../../../../components/Modal";
import PDFPreview from "../pdf/PDFPreview";
import { EnergyLabelResult } from "../pdf";
import { FaHome } from "react-icons/fa";
import { IoMdEye } from "react-icons/io";
import EnergyLabelComponent from "../../../../../components/apiForms/EnergyLabel";
import { EnergyLabel, EnergyLabelData } from "../types";

type Props = {
  property: EnergyLabelData;
  buildingPhoto?: string;
  map?: string;
};

// Renamed to avoid name collision with the enum
const EnergyLabelDisplay = EnergyLabelComponent;

export const ResultSummary: FC<Props> = ({
  property,
  buildingPhoto = "",
  map = "",
}) => {
  const [isPreviewModalVisible, setIsPreviewModalVisible] = useState(false);

  const desc = [
    {
      icon: <FaHome size={24} />,
      title: "Ingeschat energielabel",
      result: (
        <EnergyLabelDisplay label={property?.current_estimated_energy_label} />
      ),
    },
    {
      icon: null,
      title: "Definitief energielabel",
      result: <EnergyLabelDisplay label={property?.definitive_energy_label} />,
    },
  ];

  return (
    <>
      <div className="bg-white shadow-[0px_2px_8px_0px_#00000026] rounded-md md:flex md:flex-wrap justify-between p-4 md:p-6 absolute lg:top-[320px] lg:left-[250px] lg:w-[496px] md:top-[200px] md:left-[110px] md:w-[409px] top-[200px] left-[40px] w-[300px] grid grid-cols-2 gap-2">
        {desc.map(({ icon, title, result }, index) => (
          <div key={index} className="flex flex-col items-center gap-2">
            <Text className="">{title}</Text>
            <Subtitle className="flex text-base md:text-xl">{result}</Subtitle>
          </div>
        ))}
      </div>

      <div className="flex absolute lg:top-[390px] lg:left-[300px] md:top-[250px] md:left-[230px] top-[250px] left-[100px] gap-2">
        <Button
          type="button"
          onClick={() => setIsPreviewModalVisible(true)}
          className="ml-2 bg-white text-primary border border-primary flex items-center"
          size="xl"
        >
          <IoMdEye size={20} className="text-primary" />
          <span className="hidden lg:inline ml-2">Preview PDF</span>
        </Button>

        <EnergyLabelResult
          data={property}
          buildingPhoto={buildingPhoto}
          map={map}
        />
      </div>

      <Modal
        trigger={isPreviewModalVisible}
        setTrigger={setIsPreviewModalVisible}
      >
        <PDFPreview data={property} buildingPhoto={buildingPhoto} map={map} />
      </Modal>
    </>
  );
};
