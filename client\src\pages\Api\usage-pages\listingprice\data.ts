import { InputProps } from "../../../../@types";

export const ListingPriceFields: InputProps[] = [
  {
    label: "Postcode*",
    name: "postcode",
    type: "text",
    placeholder: "1234AB",
    tooltip: "",
  },
  {
    label: "Huisnummer*",
    name: "housenumber",
    type: "text",
    placeholder: "1",
    tooltip: "",
  },
  {
    label: "Huisnummer toevoeging",
    name: "houseaddition",
    type: "text",
    placeholder: "A",
    tooltip: "",
  },
  {
    label: "Waarderingsdatum",
    name: "valuation_date",
    type: "date",
    placeholder: "JJJJMMDD",
    tooltip: "De standaard waarderingsdatum is vandaag",
  },
  {
    label: "Huidig energielabel",
    name: "energy_label",
    type: "slider",
    tooltip: "Het energielabel van de woning",
  },
  {
    label: "Woonoppervlakte (m²)",
    name: "inner_surface_area",
    type: "number",
    placeholder: "100",
    tooltip: "Het woonopperv<PERSON> de woning",
  },
  {
    label: "Perceeloppervlakte (m²)",
    name: "outer_surface_area",
    type: "number",
    placeholder: "150",
    tooltip: "Het perceeloppervlak van de woning",
  },
];

export const listingPriceEndpoint = {
  title: "Vraagprijs API",
  description:
    "Met dit eindpunt kunt u antwoord ontvangen van het Altum AI Listing Price-model.",
  method: "POST",
  url: "https://api.altum.ai/listingprice",
  headers: [
    {
      name: "x-api-key",
      type: "string",
      required: true,
      description: "Unieke API-sleutel van Altum",
    },
    {
      name: "Content-Type",
      type: "string",
      required: false,
      description: "application/json",
    },
  ],
  responses: [
    { status: 200, description: "Succesvolle reactie" },
    { status: 400, description: "Mislukte reactie" },
    { status: 401, description: "Geen toegang" },
    { status: 403, description: "Verboden" },
    { status: 500, description: "Service is niet beschikbaar en/of offline" },
  ],
};
