import pool from "../db";
import bcrypt from "bcryptjs";
import { v4 as uuidv4 } from "uuid";

async function createAdmin() {
  try {
    // Hash password
    const salt = await bcrypt.genSalt(10);
    const password = await bcrypt.hash("admin123", salt);
    console.log("Password hash:", password);

    // Create admin user
    const result = await pool.query(
      `INSERT INTO users (
        user_id,
        email,
        password,
        role,
        active,
        first_name,
        last_name,
        created_at,
        api_key,
        api_key_id,
        current_usage_plan,
        company,
        kvk,
        stripe_customer_id
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14) RETURNING *`,
      [
        uuidv4(),
        "<EMAIL>",
        password,
        "admin",
        true,
        "Admin",
        "User",
        new Date(),
        "test-api-key",
        "test-api-key-id",
        "test-usage-plan",
        "Mopsus Admin",
        "12345678",
        "cus_test_" + uuidv4(),
      ],
    );

    console.log("Admin user created:", result.rows[0]);
    process.exit(0);
  } catch (error) {
    console.error("Error:", error);
    process.exit(1);
  }
}

createAdmin();
