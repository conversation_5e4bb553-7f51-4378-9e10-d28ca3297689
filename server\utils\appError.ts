class AppError extends Error {
  statusCode: number;
  status: string;
  isOperational?: boolean;
  constructor(message: string, statusCode: number, operational?: boolean) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith("4") ? "fail" : "error";
    this.isOperational = operational;

    Error.captureStackTrace(this, this.constructor);
  }
}

export default AppError;
