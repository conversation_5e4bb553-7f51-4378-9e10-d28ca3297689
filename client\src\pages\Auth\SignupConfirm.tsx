import { useEffect, useState } from "react";
import { Helmet } from "react-helmet";
import { Link, useParams } from "react-router-dom";
import "./Signup.css";
import { allowSignupConfirmation } from "../../redux/actions/authActions";
import { useAppDispatch } from "../../redux/hooks";

interface MatchParams {
  id: string;
}

function SignupConfirm() {
  const [loading, setLoading] = useState(true);
  const [allowConfirmation, setAllowConfirmation] = useState(false);
  const dispatch = useAppDispatch();
  const { id } = useParams<MatchParams>();

  useEffect(() => {
    dispatch(allowSignupConfirmation(id)).then((allow) => {
      if (allow.payload) {
        setLoading(false);
        setAllowConfirmation(true);
      } else {
        setLoading(false);
        setAllowConfirmation(false);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (loading) {
    return (
      <div className="lds-ring">
        <div />
        <div />
        <div />
        <div />
      </div>
    );
  }
  return (
    <>
      <Helmet>
        <title>Account bevestigd - Altum AI </title>
      </Helmet>
      <div className="signup-confirm__container">
        {allowConfirmation ? (
          <div>
            <h1 className="signup-confirm__container--title">
              Account bevestigd
            </h1>
            <p>
              U heeft met succes een account aangemaakt bij Altum AI Plateform.
              Je hebt ook ontvang een e-mail om het e-mailadres te bevestigen.
              Ga naar jou inbox om te bevestigen om in te loggen.
            </p>
            <p className="signup-confirm__container--tip">
              <span>Tip</span>: Bekijk voor de zekerheid de spambox van je
              e-mail inbox mocht je de e-mail niet kunnen vinden.
            </p>
            {/* <button
              tabIndex={10}
              type="button"
              onClick={onClickResendEmail}
              className="btn btn-primary"
            > */}
            {/* Verificatie-e-mail opnieuw verzenden
            </button> */}
          </div>
        ) : (
          <div>
            <p className="signup-confirm__container--tip">
              Gebruiker is al geverifieerd.
            </p>
            <Link to="/" className="btn btn-primary redirect-btn">
              Terug naar home
            </Link>
          </div>
        )}
      </div>
    </>
  );
}

export default SignupConfirm;
