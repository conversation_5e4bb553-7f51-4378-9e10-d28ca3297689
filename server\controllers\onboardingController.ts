import { NextFunction, Request, Response } from "express";
import pool from "../db";
import AppError from "../utils/appError";
import { User } from "../@types";
import {
  addOnboardingStateService,
  countUserResponses,
} from "../services/onboardingService";
import { AnalyticsHooks } from "../services/admin/analyticsHooks";

interface OnboardingQuestion {
  question_id: string;
  question_text: string;
  description: string;
  step: number;
  option: string;
  rank: number;
}

interface TransformedData {
  question_id: string;
  question_text: string;
  description: string;
  step: number;
  options: { option: string; rank: number }[];
}

export const addOnboardingQuestionResponse = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const data: {
      question_id: string;
      responses: string[];
    }[] = req.body.data;
    const stepId = req.body.stepId;
    const { user_id } = req.user as User;
    await Promise.all(
      data.map(({ question_id, responses }) => {
        if (responses.length > 0) {
          const answer_text =
            responses.length > 1 ? responses.join(", ") : responses[0];
          return pool.query(
            `INSERT INTO user_onboarding_responses (user_id, question_id, answer_text)
             VALUES ($1, $2, $3)
             ON CONFLICT (user_id, question_id) 
             DO UPDATE SET answer_text = EXCLUDED.answer_text`,
            [user_id, question_id, answer_text],
          );
        }
        return Promise.resolve();
      }),
    );

    const questionCount = await countUserResponses(user_id);

    if (questionCount >= 5) {
      // Log onboarding completion when enough questions are answered
      await AnalyticsHooks.onOnboardingComplete(req.user as User);

      // Log onboarding response
      await AnalyticsHooks.onUserActivity(user_id, "onboarding_complete");
      await addOnboardingStateService(stepId, user_id);
    }
    res.json({
      status: "success",
    });
  } catch (err) {
    next(new AppError("something went wrong adding response: " + err, 500));
  }
};

export const getOnboardingQuestions = async (req: Request, res: Response) => {
  try {
    const questionWithOption = await pool.query<OnboardingQuestion>(
      `SELECT oq.question_id, oq.question_text, oq.description, oq.step, qp.option, qp.rank  FROM onboarding_questions oq INNER JOIN question_options qp ON oq.question_id = qp.question_id`,
    );
    const transformedData = transformQuestionsAndOptions(
      questionWithOption.rows,
    );
    res.json({ status: "success", onboardQuestions: transformedData });
  } catch (err) {
    console.log(err);
    return new AppError(
      "something went fetching onboarding questions: " + err,
      500,
    );
  }
};
const transformQuestionsAndOptions = (
  onboardingQuestions: OnboardingQuestion[],
) => {
  const transformedData: Record<string, TransformedData> = {};

  for (const question of onboardingQuestions) {
    const { question_id, question_text, description, step, option, rank } =
      question;
    if (!transformedData[question_id]) {
      transformedData[question_id] = {
        question_id,
        question_text,
        description,
        step,
        options: [],
      };
    }
    transformedData[question_id].options.push({ rank, option });
  }

  const finalResult = Object.values(transformedData);

  return finalResult;
};
export const addOnboardingState = async (req: Request, res: Response) => {
  try {
    const { step_id } = req.body;
    const { user_id } = req.user as User;

    // Log onboarding step completion
    await AnalyticsHooks.onUserActivity(
      user_id,
      `completed_onboarding_step_${step_id}`,
    );
    await addOnboardingStateService(step_id, user_id);
    res.json({
      status: "success",
    });
  } catch (err) {
    console.log(err);
    return new AppError("something went adding onboarding state: " + err, 500);
  }
};

export const addAllOnboardingState = async (req: Request, res: Response) => {
  try {
    const { user_id } = req.user as User;

    // First, get all the existing step_ids for this user
    const existingStepsResult = await pool.query(
      `SELECT step_id FROM user_onboarding_state WHERE user_id = $1`,
      [user_id],
    );
    const existingSteps = new Set(
      existingStepsResult.rows.map((row) => row.step_id),
    );

    // Get all step_ids from the onboarding_steps table
    const stepsResult = await pool.query(
      `SELECT step_id FROM onboarding_steps`,
    );
    const stepsToAdd = stepsResult.rows.filter(
      (step) => !existingSteps.has(step.step_id),
    );

    // Insert new steps only
    await Promise.all(
      stepsToAdd.map((step) =>
        pool.query(
          `INSERT INTO user_onboarding_state (user_id, step_id, is_complete) VALUES ($1, $2, $3)`,
          [user_id, step.step_id, true],
        ),
      ),
    );

    // Log completion of all onboarding steps
    await AnalyticsHooks.onOnboardingComplete(req.user as User);

    // Maybe send a response back to the client if this is the end of your route's logic
    res.json({ message: "Onboarding state updated successfully" });
  } catch (err) {
    console.error("Error adding onboarding steps:", err);
    res
      .status(500)
      .json(
        new AppError(
          "Something went wrong adding onboarding steps: " + err,
          500,
        ),
      );
  }
};

export const getOnboardingSteps = async (req: Request, res: Response) => {
  try {
    const onboardResponse = await pool.query(
      `SELECT step_id, step_name FROM onboarding_steps`,
    );
    res.json({ status: "success", onboardSteps: onboardResponse.rows });
  } catch (err) {
    return new AppError(
      "something went fetching onboarding steps: " + err,
      500,
    );
  }
};

export const getOnboardingStepId = async (stepName: string) => {
  try {
    const step = await pool.query(
      `SELECT step_id FROM onboarding_steps WHERE step_name = '${stepName}'`,
    );
    return step.rows[0];
  } catch (error) {
    return new AppError(
      "something went fetching onboarding step: " + error,
      500,
    );
  }
};

export const onboardingStepsCompleted = async (req: Request, res: Response) => {
  try {
    const { user_id } = req.user as User;
    const response = await pool.query<{ onboarding_complete: boolean }>(
      `SELECT (COUNT(*) = (SELECT COUNT(*) FROM onboarding_steps)) AS onboarding_complete FROM user_onboarding_state WHERE user_id = $1 AND is_complete = $2;`,
      [user_id, true],
    );
    res.json({
      status: "success",
      isCompleted: response.rows[0].onboarding_complete,
    });
  } catch (err) {
    console.log(err);
    return new AppError(
      "something went fetching onboarding steps status: " + err,
      500,
    );
  }
};

export const getOnboardingState = async (req: Request, res: Response) => {
  try {
    const { user_id } = req.user as User;
    const result = await pool.query(
      `SELECT
           os.step_id AS step_id, 
           os.step_name, 
           os.description, 
           os.rank,
           COALESCE(us.is_complete, false) AS completed
         FROM
           onboarding_steps os
         LEFT JOIN user_onboarding_state us ON os.step_id = us.step_id AND us.user_id = $1;`,
      [user_id],
    );
    res.json(
      result.rows.map((row) => ({
        stepId: row.step_id,
        stepName: row.step_name,
        stepDescription: row.description,
        stepRank: row.rank,
        completed: row.completed,
      })),
    );
  } catch (err) {
    console.log(err);
    return new AppError(
      "something went fetching onboarding steps state: " + err,
      500,
    );
  }
};

export const getUserQuestion5Response = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { userId } = req.params;
    const questionId = await pool.query(
      `SELECT question_id from onboarding_questions WHERE step = 4`,
    );

    // Optional: Add authorization logic if needed, e.g., ensure req.user can access this userId's data.

    const result = await pool.query(
      `SELECT answer_text FROM user_onboarding_responses WHERE user_id = $1 AND question_id = $2;`,
      [userId, questionId.rows[0].question_id],
    );

    if (result.rows.length === 0) {
      return res.status(200).json({
        status: "success",
        data: {
          question_id: questionId.rows[0].question_id,
          user_id: userId,
          answer_text: "", // Return empty array for answer_text
        },
      });
    }

    res.status(200).json({
      status: "success",
      data: {
        question_id: questionId.rows[0].question_id,
        user_id: userId,
        answer_text: result.rows[0].answer_text,
      },
    });
  } catch (err) {
    next(new AppError(`Error fetching question 5 response: ${err}`, 500));
  }
};

/*
ALTER TABLE user_onboarding_responses
ADD CONSTRAINT unique_user_question UNIQUE (user_id, question_id);

ALTER TABLE onboarding_steps ADD COLUMN rank INT

ALTER TABLE onboarding_questions ADD description TEXT;
ALTER TABLE onboarding_questions ADD step INT;
INSERT INTO onboarding_questions (question_text, description, step) VALUES (')
//Create onboarding question options
CREATE TABLE IF NOT EXISTS question_options (
    id uuid NOT NULL PRIMARY KEY DEFAULT uuid_generate_v4(),
    question_id uuid NOT NULL,
    option VARCHAR(255) NOT NULL,
    rank INT NOT NULL,
    FOREIGN KEY (question_id) REFERENCES onboarding_questions(question_id)
    
);

INSERT INTO question_options (question_id, option, rank)
VALUES
  ('13d085fa-694d-4132-88ae-fc2c1aa58480', 'Google-advertenties/zoeken', 1),
  ('13d085fa-694d-4132-88ae-fc2c1aa58480', 'LinkedIn', 2),
  ('13d085fa-694d-4132-88ae-fc2c1aa58480', 'Via bestaande klanten', 3),
  ('13d085fa-694d-4132-88ae-fc2c1aa58480', 'Anders', 4);

  ('5577653d-71e6-48ec-beda-8b40b8240930', 'Makelaardij', 1),
  ('5577653d-71e6-48ec-beda-8b40b8240930', 'Taxateur', 2),
  ('5577653d-71e6-48ec-beda-8b40b8240930', 'Financiën/Bank', 3),
  ('5577653d-71e6-48ec-beda-8b40b8240930', 'Duurzaamheid', 4),
  ('5577653d-71e6-48ec-beda-8b40b8240930', 'Overheid & woningcorporatie', 5),
  ('5577653d-71e6-48ec-beda-8b40b8240930', 'Anders', 6),
  
  ('66ab056a-4ac2-4220-b748-e2770a8068b0', 'Oprichter/eigenaar', 1),
  ('66ab056a-4ac2-4220-b748-e2770a8068b0', 'Manager/beslisser', 2),
  ('66ab056a-4ac2-4220-b748-e2770a8068b0', 'Product eigenaar', 3),
  ('66ab056a-4ac2-4220-b748-e2770a8068b0', 'Hypotheekadviseur', 4),
  ('66ab056a-4ac2-4220-b748-e2770a8068b0', 'Makelaar/taxateur', 5),
  ('66ab056a-4ac2-4220-b748-e2770a8068b0', 'Software ontwikkelaar', 6),
  ('66ab056a-4ac2-4220-b748-e2770a8068b0', 'Anders', 7),
  
  ('07ef1b68-81ca-493c-b2ab-3050bc002136', 'Duurzaamheid', 1),
  ('07ef1b68-81ca-493c-b2ab-3050bc002136', 'Taxaties', 2),
  ('07ef1b68-81ca-493c-b2ab-3050bc002136', 'Woningkenmerken', 3),
  ('07ef1b68-81ca-493c-b2ab-3050bc002136', 'WOZ-waarde', 4),
  ('07ef1b68-81ca-493c-b2ab-3050bc002136', 'Huurgegevens', 5),
  ('07ef1b68-81ca-493c-b2ab-3050bc002136', 'Herbouwwaarde', 6),

  ('e664a772-00f7-42c3-ba60-654d50103e69', 'Rechtstreeks via ons platform', 1),
  ('e664a772-00f7-42c3-ba60-654d50103e69', 'Integratie in een platform, proces of workflow via API''s', 2);
*/
