import logger from "./logger";

const delay = (ms: number): Promise<void> =>
  new Promise((resolve) => setTimeout(resolve, ms));

interface RetryConfig {
  maxRetries?: number;
  baseDelay?: number;
  maxDelay?: number;
}

export async function withExponentialBackoff<T>(
  operation: () => Promise<T>,
  config: RetryConfig = {},
): Promise<T> {
  const {
    maxRetries = 5,
    baseDelay = 1000, // Start with 1 second
    maxDelay = 32000, // Max delay of 32 seconds
  } = config;

  let retryCount = 0;

  while (true) {
    try {
      return await operation();
    } catch (error: any) {
      retryCount++;

      // Check if we've exceeded max retries
      if (retryCount >= maxRetries) {
        throw error;
      }

      // Check if error is retryable
      if (
        error.name === "TooManyRequestsException" ||
        error.statusCode === 429
      ) {
        // Calculate delay with exponential backoff and jitter
        const exponentialDelay = Math.min(
          maxDelay,
          baseDelay * Math.pow(2, retryCount - 1),
        );
        const jitter = Math.random() * 1000; // Add up to 1 second of random jitter
        const retryDelay = exponentialDelay + jitter;

        logger.info(
          `Rate limited by AWS API. Retrying in ${Math.round(
            retryDelay / 1000,
          )} seconds (attempt ${retryCount} of ${maxRetries})`,
        );

        // If AWS provides a retry-after header, use that instead
        const retryAfter = error.retryAfterSeconds;
        if (retryAfter) {
          await delay(retryAfter * 1000);
        } else {
          await delay(retryDelay);
        }
        continue;
      }

      // If error is not retryable, throw immediately
      throw error;
    }
  }
}
