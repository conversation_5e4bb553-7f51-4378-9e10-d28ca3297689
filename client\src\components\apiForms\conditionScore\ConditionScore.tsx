import React from "react";
import { Redirect } from "react-router";
import { useAppSelector } from "../../../redux/hooks";
import ConditionScoreForm from "./ConditionScoreForm";
import Loading from "../../Loading";
import { FormProvider } from "../../../pages/Api/usage-pages/components/FormContext";

const ConditionScore = () => {
  const { result, loading } = useAppSelector((state) => state.conditionScore);

  if (loading) {
    return <Loading />;
  }
  return (
    <FormProvider>
      {Object.keys(result).length > 0 ? (
        <Redirect
          to={{
            pathname: "/conditie-score-result",
          }}
        />
      ) : (
        <ConditionScoreForm />
      )}
    </FormProvider>
  );
};

export default ConditionScore;
