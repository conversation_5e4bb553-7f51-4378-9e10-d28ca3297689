import { User } from "../../@types";
import AppError from "../../utils/appError";
import { AWSService } from "./awsService";
import { UserService } from "./userService";
import crypto from "crypto";
import { createStripeCustomer } from "../../controllers/stripeController";
import Email from "../../utils/emailHandler";
import <PERSON><PERSON> from "stripe";
import bcrypt from "bcryptjs";
import { fetchAnalytics } from "../../utils/fetchAnalytics";
import pool from "../../db";
import {
  PaginationParams,
  PaginatedResponse,
  getPaginationParams,
  createPaginatedResponse,
} from "../../utils/pagination";
import {
  addUserToEmailTable,
  addUserToNotificationTable,
} from "../../controllers/authController";

export class AdminService {
  /**
   * Creates a new user with AWS resources
   */
  static async createUser(userData: {
    firstName?: string;
    lastName?: string;
    email: string;
    company?: string;
    kvk?: string;
    usagePlanId: string;
    password: string;
  }): Promise<{ user: User; message?: string }> {
    try {
      // Validate required fields
      if (!userData.email) {
        throw new AppError("Email is required", 400);
      }
      if (!userData.usagePlanId) {
        throw new AppError("Usage plan ID is required", 400);
      }

      // Generate random password if not provided
      if (!userData.password) {
        userData.password = crypto.randomBytes(4).toString("hex");
      }

      // Check if user exists
      const existingUser = await UserService.getUserByEmailOrApiKey(
        userData.email,
        "",
      );
      if (existingUser) {
        throw new AppError("User with this email already exists", 409);
      }

      // Verify usage plan exists
      try {
        await AWSService.getUsagePlanById(userData.usagePlanId);
      } catch (error: any) {
        if (error.code === "NotFoundException") {
          throw new AppError("Invalid usage plan ID", 400);
        }
        throw error;
      }

      // Create AWS API Key
      let apiKey;
      try {
        apiKey = await AWSService.createApiKey(userData.email);
      } catch (error: any) {
        throw new AppError("Failed to create API key for user", 500, error);
      }

      // Link API key to usage plan
      try {
        await AWSService.linkKeyToUsagePlan(userData.usagePlanId, apiKey.id!);
      } catch (error: any) {
        // Cleanup API key if linking fails
        await AWSService.deleteApiKey(apiKey.id!).catch(() => {});
        throw new AppError("Failed to link API key to usage plan", 500, error);
      }

      // Create user in database
      try {
        // Create Stripe customer
        const stripeCustomerId = await createStripeCustomer(userData.email);

        try {
          // Hash password before storing
          const encryptedPassword = await bcrypt.hash(userData.password, 10);

          const newUser = await UserService.createUser({
            firstName: userData.firstName,
            lastName: userData.lastName,
            email: userData.email,
            company: userData.company,
            kvk: userData.kvk,
            apiKey: apiKey.value!,
            apiKeyId: apiKey.id!,
            usagePlanId: userData.usagePlanId,
            password: encryptedPassword,
            stripeCustomerId,
            active: true,
          });

          // Send login credentials to user
          await new Email(newUser).sendLoginCredentials(
            userData.email,
            userData.password,
          );

          return {
            user: newUser,
            message: `Login credentials have been sent to ${userData.email}`,
          };
        } catch (err: any) {
          console.error("Error creating user:", err);
          throw new AppError(
            `Failed to create user: ${err.message || "Unknown error"}`,
            500,
            err,
          );
        }
      } catch (error: any) {
        // Cleanup AWS resources if database operation fails
        await AWSService.deleteApiKey(apiKey.id!).catch(() => {});
        throw error;
      }
    } catch (error: any) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to create user", 500, error);
    }
  }

  /**
   * Deletes a user and their AWS resources
   */
  static async deleteUser(userIdentifier: {
    userId?: string;
    email?: string;
  }): Promise<void> {
    try {
      let user: User | null;
      console.log();
      // Get user based on provided identifier
      if (userIdentifier.email) {
        user = await UserService.getUserByEmailOrApiKey(
          userIdentifier.email,
          "",
        );
      } else if (userIdentifier.userId) {
        user = await UserService.getUserById(userIdentifier.userId);
      } else {
        throw new AppError("Either userId or email must be provided", 400);
      }

      if (!user) {
        throw new AppError(`User not found`, 404);
      }

      // Delete AWS resources first
      try {
        if (!user.api_key_id) {
          console.log("No API key ID found for user, skipping AWS deletion");
        } else {
          await AWSService.deleteApiKey(user.api_key_id);
        }
      } catch (error: any) {
        // Log error but continue with user deletion
        console.error("Error deleting AWS API key:", error);
        console.log("Continuing with user deletion despite AWS error");
      }

      // Delete user and associated data from database
      if (userIdentifier.email) {
        await UserService.deleteUserByEmail(userIdentifier.email);
      } else {
        await UserService.deleteUser(userIdentifier.userId!);
      }
    } catch (error: any) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to delete user", 500, error);
    }
  }

  /**
   * Migrates a legacy user with existing API key
   */
  static async migrateLegacyUser(userData: {
    externalApiKey: string;
    email: string;
    company?: string;
    kvk?: string;
    firstName?: string;
    lastName?: string;
  }): Promise<{ user: User; message?: string }> {
    // Validate required fields
    if (!userData.email) {
      throw new AppError("Email is required", 400);
    }
    if (!userData.externalApiKey) {
      throw new AppError("External API key is required", 400);
    }

    try {
      // Verify and get AWS resources
      const existingKey = await AWSService.getApiKey(userData.externalApiKey);
      const usagePlans = await AWSService.getUsagePlansForKey(existingKey.id!);

      // Access usagePlanId from the first usage plan key
      let usagePlanId: string | undefined;
      if (usagePlans.items && usagePlans.items.length > 0) {
        usagePlanId = usagePlans.items[0].id;
      }

      // Create new plan if none exists
      if (!usagePlanId) {
        const newPlan = await AWSService.createUsagePlan(
          `migrated-${userData.email}-${Date.now()}`,
        );
        usagePlanId = newPlan.id!;
        await AWSService.linkKeyToUsagePlan(usagePlanId, existingKey.id!);
      }

      // Check if user exists
      const existingUser = await UserService.getUserByEmailOrApiKey(
        userData.email,
        userData.externalApiKey,
      );

      if (existingUser) {
        // Update API key related fields for existing user
        const updatedUser = await this.editUser(existingUser.user_id, {
          api_key: existingKey.value!,
          api_key_id: existingKey.id!,
          current_usage_plan: usagePlanId,
        });
        return {
          user: updatedUser,
          message: "API key fields updated successfully",
        };
      }

      // Create new user in database
      try {
        // Generate random password for new user
        const randomPassword = crypto.randomBytes(12).toString("hex");
        const encryptedPassword = await bcrypt.hash(randomPassword, 10);

        // Create Stripe customer
        const stripeCustomerId = await createStripeCustomer(userData.email);

        // Create user with all required fields
        const newUser = await UserService.createMigratedUser({
          email: userData.email,
          company: userData.company || "",
          kvk: userData.kvk || "",
          apiKey: existingKey.value!,
          apiKeyId: existingKey.id!,
          usagePlanId,
          password: encryptedPassword,
          stripeCustomerId,
        });

        // Add new user to email and notification tables
        await addUserToEmailTable(newUser);
        await addUserToNotificationTable(newUser);

        // Send login credentials to user
        await new Email(newUser).sendLoginCredentials(
          userData.email,
          randomPassword,
        );

        return {
          user: newUser,
          message: `Login credentials have been sent to ${userData.email}`,
        };
      } catch (error: any) {
        throw error;
      }
    } catch (error: any) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to migrate user", 500, error);
    }
  }

  /**
   * Gets all AWS usage plans with pagination
   */
  static async getAllUsagePlans(params: PaginationParams = {}) {
    const plans = await AWSService.getAllUsagePlans();
    const { offset, limit } = getPaginationParams(params);

    const paginatedPlans = plans.items?.slice(offset, offset + limit) || [];
    const totalItems = plans.items?.length || 0;

    return createPaginatedResponse(paginatedPlans, totalItems, params);
  }

  /**
   * Gets a specific usage plan
   */
  static async getUsagePlan(usagePlanId: string) {
    return await AWSService.getUsagePlanById(usagePlanId);
  }

  /**
   * Creates a new usage plan
   */
  static async createUsagePlan(planData: {
    name: string;
    description?: string;
    quota?: {
      limit: number;
      period: "DAY" | "WEEK" | "MONTH";
    };
    throttle?: {
      burstLimit: number;
      rateLimit: number;
    };
  }) {
    return await AWSService.createCustomUsagePlan(planData);
  }

  /**
   * Updates an existing usage plan
   */
  static async updateUsagePlan(
    usagePlanId: string,
    updateData: {
      name?: string;
      description?: string;
      quota?: {
        limit: number;
        period: "DAY" | "WEEK" | "MONTH";
      };
      throttle?: {
        burstLimit: number;
        rateLimit: number;
      };
    },
  ) {
    return await AWSService.updateUsagePlan(usagePlanId, updateData);
  }

  /**
   * Deletes a usage plan
   */
  static async deleteUsagePlan(usagePlanId: string) {
    // Check if any users are using this plan
    const users = await UserService.getUsersByUsagePlan(usagePlanId);

    if (users.length > 0) {
      throw new AppError(
        `Cannot delete usage plan. It is being used by ${users.length} users.`,
        400,
      );
    }

    await AWSService.deleteUsagePlan(usagePlanId);
  }

  /**
   * Updates a user's usage plan
   */
  static async updateUserUsagePlan(
    userId: string,
    usagePlanId: string,
  ): Promise<User> {
    try {
      // Verify user exists
      const user = await UserService.getUserById(userId);
      if (!user) {
        throw new AppError(`User with ID ${userId} not found`, 404);
      }

      // Verify usage plan exists
      try {
        await AWSService.getUsagePlanById(usagePlanId);
      } catch (error: any) {
        if (error.code === "NotFoundException") {
          throw new AppError("Invalid usage plan ID", 400);
        }
        throw error;
      }

      // Update AWS usage plan association
      try {
        await AWSService.linkKeyToUsagePlan(usagePlanId, user.api_key_id);
      } catch (error: any) {
        throw new AppError(
          "Failed to update AWS usage plan association",
          500,
          error,
        );
      }

      // Update user in database
      return await UserService.updateUserUsagePlan(userId, usagePlanId);
    } catch (error: any) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to update user usage plan", 500, error);
    }
  }

  /**
   * Gets an overview of API usage for a specific time period
   * @param apiKey The API key to get usage for
   * @param startDate Start date in ISO format
   * @param endDate End date in ISO format
   * @returns Overview of API usage including statistics and response status
   */
  static async getApiUsageOverview(
    apiKey: string,
    startDate: string,
    endDate: string,
  ): Promise<any> {
    try {
      return await fetchAnalytics(apiKey, null, startDate, endDate);
    } catch (error: any) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to get API usage overview", 500, error);
    }
  }

  /**
   * Gets all users with pagination and search/filter capabilities
   * @param params Pagination parameters
   * @param search Search term for name, email, or API key
   * @param activeFilter Filter by account status (true/false/undefined for all)
   */
  static async getUsers(
    params: PaginationParams = {},
    search?: string,
    activeFilter?: boolean,
  ): Promise<PaginatedResponse<User[]>> {
    const { offset, limit, sortBy, sortOrder } = getPaginationParams(params);

    // Build the WHERE clause
    const conditions: string[] = [];
    const queryParams: any[] = [];
    let paramCounter = 1;

    // Add search condition if provided
    if (search) {
      conditions.push(`(
        LOWER(first_name) LIKE $${paramCounter} OR 
        LOWER(last_name) LIKE $${paramCounter} OR 
        LOWER(email) LIKE $${paramCounter} OR 
        api_key LIKE $${paramCounter}
      )`);
      queryParams.push(`%${search.toLowerCase()}%`);
      paramCounter++;
    }

    // Add active filter if provided
    if (typeof activeFilter === "boolean") {
      conditions.push(`active = $${paramCounter}`);
      queryParams.push(activeFilter);
      paramCounter++;
    }

    const whereClause =
      conditions.length > 0 ? `WHERE ${conditions.join(" AND ")}` : "";

    // Get total count with filters
    const countQuery = `
      SELECT COUNT(*) 
      FROM users 
      ${whereClause}
    `;
    const countResult = await pool.query(countQuery, queryParams);
    const totalItems = parseInt(countResult.rows[0].count);

    // Get paginated users with filters
    const usersQuery = `
      SELECT 
        user_id, first_name, last_name, company, email, kvk,
        created_at, api_key, api_key_id, current_usage_plan,
        stripe_customer_id, active, role
      FROM users
      ${whereClause}
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT $${paramCounter} OFFSET $${paramCounter + 1}
    `;

    const users = await pool.query<User[]>(usersQuery, [
      ...queryParams,
      limit,
      offset,
    ]);

    return createPaginatedResponse(users.rows, totalItems, params);
  }

  /**
   * Gets a user by ID
   */
  static async getUserById(userId: string): Promise<User | null> {
    const users = await pool.query<User>(
      `
      SELECT 
        user_id, first_name, last_name, company, email, kvk,
        created_at, api_key, api_key_id, current_usage_plan,
        stripe_customer_id, active, role
      FROM users where user_id=$1;
      `,
      [userId],
    );

    return users.rows[0] || null;
  }

  /**
   * Get user by email
   */
  static async getUserByEmail(email: string): Promise<User | null> {
    const result = await pool.query<User>(
      `
      SELECT * FROM users WHERE email = $1
      `,
      [email],
    );
    return result.rows[0] || null;
  }

  /**
   * Get usage plan by ID
   */
  static async getUsagePlanById(planId: string) {
    const result = await pool.query(
      `
      SELECT * FROM usage_plans WHERE id = $1
      `,
      [planId],
    );
    return result.rows[0] || null;
  }

  /**
   * Edit user by ID
   * @param userId User ID to edit
   * @param updates Fields to update
   */
  static async editUser(
    userId: string,
    updates: Partial<{
      first_name: string;
      last_name: string;
      email: string;
      company: string;
      kvk: string;
      active: boolean;
      current_usage_plan: string;
      stripe_customer_id: string;
      role: string;
      api_key: string;
      api_key_id: string;
    }>,
  ): Promise<User> {
    // Build the SET clause dynamically
    const setFields: string[] = [];
    const values: any[] = [];
    let paramCounter = 1;

    Object.entries(updates).forEach(([key, value]) => {
      if (value !== undefined) {
        setFields.push(`${key} = $${paramCounter}`);
        values.push(value);
        paramCounter++;
      }
    });

    if (setFields.length === 0) {
      throw new Error("No valid fields to update");
    }

    const query = `
      UPDATE users
      SET ${setFields.join(", ")}
      WHERE user_id = $${paramCounter}
      RETURNING *
    `;

    const result = await pool.query<User>(query, [...values, userId]);

    if (result.rowCount === 0) {
      throw new Error("User not found");
    }

    return result.rows[0];
  }

  /**
   * Updates multiple users' usage plans
   */
  static async batchUpdateUsagePlans(
    updates: Array<{ userId: string; usagePlanId: string }>,
  ): Promise<void> {
    try {
      await pool.query("BEGIN");

      try {
        for (const update of updates) {
          const user = await UserService.getUserById(update.userId);
          if (!user) {
            throw new AppError(`User ${update.userId} not found`, 404);
          }

          // Update AWS usage plan
          await AWSService.deleteApiKey(user.api_key_id);
          await AWSService.linkKeyToUsagePlan(
            update.usagePlanId,
            user.api_key_id,
          );

          // Update database
          await UserService.updateUserUsagePlan(
            update.userId,
            update.usagePlanId,
          );
        }

        await pool.query("COMMIT");
      } catch (error) {
        await pool.query("ROLLBACK");
        throw error;
      }
    } catch (error: any) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError("Failed to batch update usage plans", 500, error);
    }
  }

  /**
   * Gets user statistics by usage plan with pagination
   */
  static async getUserStatsByUsagePlan(params: PaginationParams = {}): Promise<
    PaginatedResponse<{
      usage_plan_id: string;
      usage_plan_name: string;
      total_users: number;
      active_users: number;
    }>
  > {
    const { offset, limit, sortBy, sortOrder } = getPaginationParams(params);

    const query = `
      WITH usage_plan_stats AS (
        SELECT 
          u.current_usage_plan as usage_plan_id,
          COUNT(*) as total_users,
          COUNT(CASE 
          WHEN u.active = true THEN 1 
          END) as active_users
        FROM users u
        GROUP BY u.current_usage_plan
      )
      SELECT 
        ups.usage_plan_id,
        up.name as usage_plan_name,
        COALESCE(ups.total_users, 0) as total_users,
        COALESCE(ups.active_users, 0) as active_users
      FROM usage_plan_stats ups
      LEFT JOIN usage_plans up ON up.id = ups.usage_plan_id
    `;

    // Get total count
    const countResult = await pool.query(
      "SELECT COUNT(DISTINCT current_usage_plan) FROM users",
    );
    const totalItems = parseInt(countResult.rows[0].count);

    // Get paginated results
    const result = await pool.query(
      `
      ${query}
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT $1 OFFSET $2
    `,
      [limit, offset],
    );

    return createPaginatedResponse(result.rows, totalItems, params);
  }

  /**
   * Gets user activity metrics
   */
  static async getUserActivityMetrics(days: number = 30): Promise<{
    total_users: number;
    active_users: number;
    new_users: number;
    usage_by_endpoint: Record<string, number>;
  }> {
    const [totalUsers, newUsers, apiUsage] = await Promise.all([
      // Get total and active users
      pool.query(`
        SELECT 
          COUNT(*) as total,
          COUNT(CASE WHEN active = true THEN 1 END) as active
        FROM users
      `),
      // Get new users in the last n days
      pool.query(
        `SELECT COUNT(*) as new_users 
         FROM users 
         WHERE created_at >= NOW() - INTERVAL '1 day' * $1`,
        [days],
      ),
      // Get API usage by endpoint
      pool.query(
        `SELECT endpoint, COUNT(*) as count
         FROM api_usage_logs
         WHERE timestamp >= NOW() - INTERVAL '1 day' * $1
         GROUP BY endpoint`,
        [days],
      ),
    ]);

    const usageByEndpoint = apiUsage.rows.reduce((acc, row) => {
      acc[row.endpoint] = parseInt(row.count);
      return acc;
    }, {} as Record<string, number>);

    return {
      total_users: parseInt(totalUsers.rows[0].total),
      active_users: parseInt(totalUsers.rows[0].active),
      new_users: parseInt(newUsers.rows[0].new_users),
      usage_by_endpoint: usageByEndpoint,
    };
  }

  /**
   * Gets user retention metrics with pagination
   */
  static async getUserRetentionMetrics(
    months: number = 12,
    params: PaginationParams = {},
  ): Promise<
    PaginatedResponse<{
      month: string;
      total_users: number;
      retained_users: number;
      retention_rate: number;
    }>
  > {
    const { offset, limit, sortBy, sortOrder } = getPaginationParams(params);

    const query = `
      WITH monthly_stats AS (
        SELECT 
          DATE_TRUNC('month', created_at) as month,
          COUNT(*) as total_users,
          COUNT(CASE 
          WHEN last_login_at >= DATE_TRUNC('month', NOW()) - INTERVAL '1 month'
          THEN 1 
          END) as retained_users
        FROM users
        WHERE created_at >= NOW() - INTERVAL '${months} months'
        GROUP BY DATE_TRUNC('month', created_at)
      )
      SELECT 
        TO_CHAR(month, 'YYYY-MM') as month,
        total_users,
        retained_users,
        ROUND((retained_users::float / NULLIF(total_users, 0)) * 100, 2) as retention_rate
      FROM monthly_stats
    `;

    // Get total count
    const countResult = await pool.query(
      `SELECT COUNT(*) FROM (${query}) as subquery`,
    );
    const totalItems = parseInt(countResult.rows[0].count);

    // Get paginated results
    const result = await pool.query(
      `
      ${query}
      ORDER BY ${sortBy} ${sortOrder}
      LIMIT $1 OFFSET $2
    `,
      [limit, offset],
    );

    return createPaginatedResponse(result.rows, totalItems, params);
  }

  /**
   * Adds test credit to a user's usage plan
   */
  static async addTestCredit(
    userId: string,
    additionalCredit: number,
  ): Promise<void> {
    try {
      // Get user details to find their usage plan
      const user = await UserService.getUserById(userId);
      if (!user) {
        throw new AppError("User not found", 404);
      }
      if (!user.current_usage_plan) {
        throw new AppError("User has no active usage plan", 400);
      }

      // Add test credit to their usage plan
      await AWSService.addTestCredit(
        user.current_usage_plan,
        user.api_key_id,
        additionalCredit,
      );
    } catch (error: any) {
      if (error instanceof AppError) throw error;
      throw new AppError("Failed to add test credit", 500, error);
    }
  }
}
