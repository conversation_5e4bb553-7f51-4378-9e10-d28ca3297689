import { MoveFields } from "./movefields";
import FormBody from "../FormBody";
import { FormOptions } from "../../../@types";
import { MoveData } from "../../../assets/images/api/APIimages";
import { FormEvent, useEffect } from "react";
import { postMoveData } from "../../../redux/actions/moveDataActions";
import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import { useFormContext } from "../../../pages/Api/usage-pages/components/FormContext";

const MoveForm = () => {
  const { formValues, setFormValues } = useFormContext();
  const { savedQueries } = useAppSelector((state) => state.moveData);

  useEffect(() => {
    if (Object.keys(savedQueries).length > 0) {
      setFormValues({
        ...(savedQueries as FormOptions),
      });
    }
  }, [savedQueries, setFormValues]);
  const sectionFields = [{ title: "Adres", startIndex: 0, endIndex: 2 }];
  const apiKey = useAppSelector((state) => state.auth.user?.api_key);
  const dispatch = useAppDispatch();

  const handleFormSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    const newFormData: FormOptions = {
      postcode: "",
      housenumber: "",
    };
    formValues.postcode = formValues.postcode.split(" ").join("").toUpperCase();
    Object.entries(formValues).forEach(([key, value]) => {
      if (value !== undefined) {
        newFormData[key as keyof FormOptions] = value;
      }
    });
    apiKey && dispatch(postMoveData({ formData: newFormData, apiKey: apiKey }));
  };

  return (
    <FormBody
      title={"Verhuisdata opvragen"}
      desc={
        "Met de Verhuisdata API haal je data op van objecten actief op de woningmarkt."
      }
      path={"https://docs.altum.ai/apis/move-data"}
      initialFields={MoveFields}
      sectionFields={sectionFields}
      handleSubmit={handleFormSubmit}
      img={MoveData}
    />
  );
};

export default MoveForm;
