import { Request, Response } from "express";
import * as fs from "fs";
import * as path from "path";
import * as os from "os";
import { ConfigureOutput, PropertyDetails, User } from "../@types";
import { generatePropertyDescription } from "../services/descriptionService";
import { processImages } from "../services/imageService";
import {
  saveChatHistory,
  getChatHistory,
  deleteChatHistory,
  updateRateAndComment,
} from "../services/chatHistoryService";
import { sendSlackNotification } from "../services/slackNotificationService";
import { AnalyticsHooks } from "../services/admin/analyticsHooks";

const UPLOAD_DIR = path.join(os.tmpdir(), "property-uploads");

// Ensure upload directory exists
if (!fs.existsSync(UPLOAD_DIR)) {
  fs.mkdirSync(UPLOAD_DIR, { recursive: true });
}

export async function handleChunkedUpload(req: Request, res: Response) {
  const fileId = req.headers["x-file-id"] as string;
  const fileName = decodeURIComponent(req.headers["x-file-name"] as string);
  const fileType = req.headers["x-file-type"] as string;
  const fileSize = parseInt(req.headers["x-file-size"] as string, 10);
  const chunkIndex = parseInt(req.headers["x-chunk-index"] as string, 10);
  const totalChunks = parseInt(req.headers["x-total-chunks"] as string, 10);

  if (!fileId) {
    console.error("Missing x-file-id header");
    return res.status(400).json({ error: "Missing x-file-id header" });
  }

  const filePath = path.join(UPLOAD_DIR, fileId);

  if (!filePath) {
    console.error("Failed to generate file path");
    return res.status(500).json({ error: "Failed to generate file path" });
  }

  const writeStream = fs.createWriteStream(filePath, { flags: "a" });

  req.pipe(writeStream);

  req.on("end", () => {
    if (chunkIndex === totalChunks - 1) {
      const metadata = JSON.stringify({
        originalName: fileName,
        mimeType: fileType,
        size: fileSize,
        path: filePath,
      });
      fs.writeFileSync(filePath + ".meta", metadata);
      res.status(200).json({ message: "File upload completed" });
    } else {
      res.status(200).json({ message: "Chunk uploaded successfully" });
    }
  });

  req.on("error", (error) => {
    console.error("Error during file upload:", error);
    res.status(500).json({ error: "File upload failed" });
  });
}

export async function generateDescription(req: Request, res: Response) {
  try {
    let imageIds: string[] | undefined = req.body.imageIds;
    if (typeof imageIds === "string") {
      imageIds = [imageIds];
    }
    const propertyDetails: PropertyDetails | undefined = req.body
      .propertyDetails
      ? JSON.parse(req.body.propertyDetails)
      : undefined;
    const configureOutput: ConfigureOutput | undefined = req.body
      .configureOutput
      ? JSON.parse(req.body.configureOutput)
      : undefined;
    const additionalNotes: string | undefined = req.body.additionalNotes;

    // Process all uploaded files
    let imageDescriptions: string[] = [];
    if (imageIds && imageIds.length > 0) {
      imageDescriptions = await processImages(imageIds);
    }
    const description = await generatePropertyDescription(
      propertyDetails,
      configureOutput,
      additionalNotes,
      imageDescriptions,
    );

    // Save the generated description and all inputs to the database
    const user = req.user as User;
    const userId = user.user_id;
    const chatHistoryId = await saveChatHistory(
      userId,
      description,
      propertyDetails,
      configureOutput,
      additionalNotes,
      imageDescriptions,
    );

    // Log analytics event
    await AnalyticsHooks.onPropertyGeneration(userId, propertyDetails);

    // Send Slack notification
    if (!user.email.match(/@altum\.ai/) && !user.email.includes("kelvin")) {
      await sendSlackNotification(user.email, description);
    }

    res.json({ description, chatHistoryId });
  } catch (error) {
    console.error("Error generating description:", error);
    res.status(500).json({ error: "Failed to generate description" });
  }
}

export async function getChatHistoryController(req: Request, res: Response) {
  try {
    const user = req.user as User;
    const userId = user.user_id;
    const page = Number(req.query.page);
    const { chatHistory, pagination } = await getChatHistory(userId, page);

    // Log user activity
    await AnalyticsHooks.onUserActivity(userId, "view_chat_history");

    res.json({ chatHistory, pagination });
  } catch (error) {
    console.error("Error fetching chat history:", error);
    res.status(500).json({ error: "Failed to fetch chat history" });
  }
}

export async function deleteChatHistoryController(req: Request, res: Response) {
  try {
    const user = req.user as User;
    const userId = user.user_id;
    const id = req.params.id;
    await deleteChatHistory(userId, id);

    // Log user activity
    await AnalyticsHooks.onUserActivity(userId, "delete_chat_history");

    res.json({ message: "Chat history deleted successfully" });
  } catch (error) {
    console.error("Error deleting chat history:", error);
    res.status(500).json({ error: "Failed to delete chat history" });
  }
}

export async function updateChatHistoryRateAndComment(
  req: Request,
  res: Response,
) {
  try {
    const { id, rate, comment } = req.body;
    const user = req.user as User;

    if (!id || rate === undefined) {
      return res.status(400).json({ error: "Missing required fields" });
    }

    await updateRateAndComment(id, rate, comment);

    // Log user activity
    await AnalyticsHooks.onUserActivity(user.user_id, "rate_chat_history");

    res.json({ message: "Rate and comment updated successfully" });
  } catch (error) {
    console.error("Error updating rate and comment:", error);
    res.status(500).json({ error: "Failed to update rate and comment" });
  }
}
