{"scripts": {"lint": "eslint --fix ./client && npx lint-staged", "prepare": "husky install"}, "devDependencies": {"@babel/eslint-parser": "^7.22.5", "@babel/plugin-syntax-jsx": "^7.22.5", "@babel/preset-react": "^7.22.5", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "autoprefixer": "^10.4.14", "babel-eslint": "^10.1.0", "eslint": "^8.57.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "14.2.5", "eslint-config-prettier": "^8.8.0", "eslint-config-standard-with-typescript": "^36.0.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-react-perf": "^3.3.2", "husky": "^8.0.0", "lint-staged": "^13.2.2", "postcss": "^8.4.21", "prettier": "^2.8.8", "tailwindcss": "^3.3.1", "typescript": "^5.5.4"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts,jsx,tsx,json,css,html": ["prettier --double-quote --write", "git add"]}, "dependencies": {"qrcode": "^1.5.4", "speakeasy": "^2.0.0"}}