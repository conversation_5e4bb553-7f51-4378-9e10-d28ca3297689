export interface ListingPriceResult {
  bag_id: string;
  postcode: string;
  housenumber: number;
  houseaddition: string;
  city: string;
  street: string;
  house_type: string;
  build_year: number;
  inner_surface_area: number;
  outer_surface_area: number;
  volume: number;
  energy_label: string;
  longitude: number;
  latitude: number;
  valuation_date: number;
  price_estimation: number;
  confidence: string;
}

export interface ListingPriceRequest {
  postcode: string;
  housenumber: string;
  houseaddition?: string;
  valuation_date?: string;
  energy_label?: string;
  inner_surface_area?: number;
  outer_surface_area?: number;
}
