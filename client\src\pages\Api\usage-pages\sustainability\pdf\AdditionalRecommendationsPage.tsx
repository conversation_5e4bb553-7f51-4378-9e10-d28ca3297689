import React from "react";
import { Page, Text, View, Image } from "@react-pdf/renderer";
import styles from "./styles";
import altumLogo from "../../../../../assets/images/Logo-AltumAI.png";
import {
  WallInsulation,
  HeatPump,
  Shower,
  Ventilation,
  WindowGlass,
} from "../../../../../assets/images/api/sustainability/sustainabilityImages";

interface AdditionalRecommendationsPageProps {
  data: any;
}

const RecommendationItem: React.FC<{
  icon: string;
  title: string;
  description: string;
  co2Reduction: number;
  monthlySavings: number;
  installationCost: number;
}> = ({
  icon,
  title,
  description,
  co2Reduction,
  monthlySavings,
  installationCost,
}) => (
  <View style={styles.recommendationContainer}>
    <View style={styles.recommendationHeader}>
      <Image src={icon} style={styles.recommendationIcon} />
      <Text style={styles.recommendationTitle}>{title}</Text>
    </View>

    <Text style={styles.recommendationText}>{description}</Text>

    <View style={styles.recommendationPoints}>
      <Text style={styles.recommendationPoint}>
        - Significant reduction in energy consumption
      </Text>
      <Text style={styles.recommendationPoint}>
        - Decrease in CO2 emissions by {co2Reduction} kg annually
      </Text>
      <Text style={styles.recommendationPoint}>
        - Monthly savings of €{monthlySavings.toFixed(2)}
      </Text>
    </View>

    <Text style={styles.installationCost}>
      Installation cost: €{installationCost.toFixed(2)}
    </Text>
  </View>
);

const AdditionalRecommendationsPage: React.FC<
  AdditionalRecommendationsPageProps
> = ({ data }) => (
  <>
    <Page size="A4" style={styles.page}>
      <RecommendationItem
        icon={WindowGlass}
        title="Glas slaapkamers"
        description={`Wij raden aan om ${data.measures.bedroom_windows?.after.desc} triple glas te plaatsen op uw woning. Hieronder vindt u de voordelen:`}
        co2Reduction={data.measures.bedroom_windows.co2_reduce}
        monthlySavings={data.measures.bedroom_windows.saving}
        installationCost={data.measures.bedroom_windows.investment}
      />

      <RecommendationItem
        icon={WallInsulation}
        title="Gevelisolatie"
        description={`Wij raden aan om ${data.measures.wall_insulation.after.desc} gevelisolatie te plaatsen op uw woning. Hieronder vindt u de voordelen:`}
        co2Reduction={data.measures.wall_insulation.co2_reduce}
        monthlySavings={data.measures.wall_insulation.saving}
        installationCost={data.measures.wall_insulation.investment}
      />

      <RecommendationItem
        icon={HeatPump}
        title="CV-ketel of warmtepomp"
        description={`Wij raden aan om ${data.measures.instalation.after.desc} HR combi + zonneboiler te plaatsen op uw woning. Hieronder vindt u de voordelen:`}
        co2Reduction={data.measures.instalation.co2_reduce}
        monthlySavings={data.measures.instalation.saving}
        installationCost={data.measures.instalation.investment}
      />

      <View style={styles.footer}>
        <Text style={styles.footerText}>PRODUCT VAN ALTUM AI | © 2024</Text>
        <Image src={altumLogo} style={styles.footerLogo} />
      </View>
    </Page>
    <Page size="A4" style={styles.page}>
      <RecommendationItem
        icon={Shower}
        title="Huidige douche WTW"
        description={`Wij raden aan om ${data.measures.shower.after.desc} douche warmte terugwinning te plaatsen op uw woning. Hieronder vindt u de voordelen:`}
        co2Reduction={data.measures.shower.co2_reduce}
        monthlySavings={data.measures.shower.saving}
        installationCost={data.measures.shower.investment}
      />

      <RecommendationItem
        icon={Ventilation}
        title="Ventilatie"
        description={`Wij raden aan om ${data.measures.ventilation.after.desc} decentraliseerde mechanische ventilatie te plaatsen op uw woning. Hieronder vindt u de voordelen:`}
        co2Reduction={data.measures.ventilation.co2_reduce}
        monthlySavings={data.measures.ventilation.saving}
        installationCost={data.measures.ventilation.investment}
      />

      <View style={styles.footer}>
        <Text style={styles.footerText}>PRODUCT VAN ALTUM AI | © 2024</Text>
        <Image src={altumLogo} style={styles.footerLogo} />
      </View>
    </Page>
  </>
);

export default AdditionalRecommendationsPage;
