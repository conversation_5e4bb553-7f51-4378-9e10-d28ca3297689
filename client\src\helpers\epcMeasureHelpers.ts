/**
 * Helper functions for mapping EPC measure numeric values to descriptive text
 * Similar to sustainability measure helpers but specific to EPC data
 */

export type EPCMeasureType =
  | "installation"
  | "wall_insulation"
  | "roof_insulation"
  | "floor_insulation"
  | "living_room_windows"
  | "bedroom_windows"
  | "shower"
  | "ventilation"
  | "solar_panels";

/**
 * Maps EPC measure numeric values to Dutch descriptions
 */
export const getEPCMeasureDescription = (
  type: EPCMeasureType,
  value: number,
): string => {
  // Base insulation descriptions (for wall, roof, floor)
  const baseInsulationDescriptions = {
    0: "Geen isolatie",
    1: "Matige isolatie",
    2: "Goede isolatie",
    3: "Zeer goede isolatie",
    4: "Uitstekende isolatie",
  };

  // Installation/heating system descriptions
  const installationDescriptions = {
    0: "Geen verwarming",
    1: "Conventionele ketel",
    2: "HR-ketel",
    3: "HR+ ketel",
    4: "HR-combi ketel",
    5: "Warmtepomp",
    6: "Hybride warmtepomp",
    7: "Stadsverwarming",
    8: "Elektrische verwarming",
  };

  // Window glass descriptions
  const windowDescriptions = {
    0: "Enkel glas",
    1: "Dubbel glas",
    2: "HR++ glas",
    3: "Drievoudig glas",
    4: "Hoogrendement glas",
  };

  // Ventilation system descriptions
  const ventilationDescriptions = {
    0: "Natuurlijke ventilatie",
    1: "Mechanische afzuigventilatie",
    2: "Gebalanceerde ventilatie",
    3: "Gebalanceerde ventilatie met WTW",
    4: "Vraaggestuurde ventilatie",
  };

  // Shower heat recovery descriptions
  const showerDescriptions = {
    0: "Geen douche WTW",
    1: "Douche warmteterugwinning",
  };

  // Solar panels descriptions
  const solarPanelDescriptions = {
    0: "Geen zonnepanelen",
    1: "Beperkt aantal zonnepanelen",
    2: "Gemiddeld aantal zonnepanelen",
    3: "Veel zonnepanelen",
    4: "Optimaal aantal zonnepanelen",
  };

  switch (type) {
    case "installation":
      return (
        installationDescriptions[
          value as keyof typeof installationDescriptions
        ] || "Onbekend verwarmingssysteem"
      );

    case "wall_insulation":
    case "roof_insulation":
    case "floor_insulation":
      return (
        baseInsulationDescriptions[
          value as keyof typeof baseInsulationDescriptions
        ] || "Onbekende isolatie"
      );

    case "living_room_windows":
    case "bedroom_windows":
      return (
        windowDescriptions[value as keyof typeof windowDescriptions] ||
        "Onbekend glastype"
      );

    case "ventilation":
      return (
        ventilationDescriptions[
          value as keyof typeof ventilationDescriptions
        ] || "Onbekend ventilatiesysteem"
      );

    case "shower":
      return (
        showerDescriptions[value as keyof typeof showerDescriptions] ||
        "Onbekend"
      );

    case "solar_panels":
      return (
        solarPanelDescriptions[value as keyof typeof solarPanelDescriptions] ||
        "Onbekend"
      );

    default:
      return "Onbekend";
  }
};

/**
 * Translates EPC measure field names to Dutch
 */
export const translateEPCMeasure = (key: string): string => {
  const translations: { [key: string]: string } = {
    installation: "Verwarmingsinstallatie",
    wall_insulation: "Gevelisolatie",
    roof_insulation: "Dakisolatie",
    floor_insulation: "Vloerisolatie",
    living_room_windows: "Glas woonkamer",
    bedroom_windows: "Glas slaapkamers",
    shower: "Douche WTW",
    ventilation: "Ventilatiesysteem",
    solar_panels: "Zonnepanelen",
    solarpanel_watt_peak: "Zonnepaneel vermogen",
    inner_surface_area: "Woonoppervlakte",
    build_year: "Bouwjaar",
    house_type: "Woningtype",
    CO2: "CO2 uitstoot",
    current_estimated_energy_label: "Huidig energielabel",
    definitive_energy_label: "Definitief energielabel",
    current_estimated_BENG1_score: "BENG1 score",
    current_estimated_BENG2_score: "BENG2 score",
  };
  return translations[key] || key;
};

/**
 * Gets additional context/explanation for EPC measures
 */
export const getEPCMeasureContext = (type: EPCMeasureType): string => {
  const contexts: { [key: string]: string } = {
    installation:
      "Het type verwarmingssysteem bepaalt de energie-efficiëntie van uw woning",
    wall_insulation: "Gevelisolatie voorkomt warmteverlies door de buitenmuren",
    roof_insulation: "Dakisolatie voorkomt warmteverlies door het dak",
    floor_insulation: "Vloerisolatie voorkomt warmteverlies door de vloer",
    living_room_windows: "Het glastype bepaalt de isolatiewaarde van ramen",
    bedroom_windows: "Het glastype bepaalt de isolatiewaarde van ramen",
    shower: "Douche WTW hergebruikt warmte uit afvalwater",
    ventilation: "Het ventilatiesysteem zorgt voor gezonde binnenlucht",
    solar_panels: "Zonnepanelen wekken duurzame elektriciteit op",
  };
  return contexts[type] || "";
};
