import { ComingInvoiceResponse } from "../@types";
import calculatePercentageDifference from "./percentDifference";
export interface Plan {
  plan?: string;
  usage: number | string;
  usageToday: number;
  usageThisWeek: number;
  comparisonDay: number;
  comparisonWeek: number;
  apiKey: string;
  status: string | null;
  comingEvent?: ComingInvoiceResponse["comingEvent"];
  subscription?: ComingInvoiceResponse["subscription"];
  usageDesc?: string;
}

export function createApiPlanTableRow({
  plan,
  usage,
  usageToday,
  usageThisWeek,
  comparisonDay: yesterday,
  comparisonWeek: lastWeek,
  apiKey,
  comingEvent,
  subscription,
  status = "active",
}: Plan): Plan {
  return {
    plan,
    usage,
    usageToday,
    usageThisWeek,
    comparisonDay: calculatePercentageDifference(yesterday, usageToday),
    comparisonWeek: calculatePercentageDifference(lastWeek, usageThisWeek),
    apiKey,
    comingEvent,
    subscription,
    status,
  };
}
