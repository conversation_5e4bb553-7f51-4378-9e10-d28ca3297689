import pool from "../db";
import { ConfigureOutput, PropertyDetails } from "../@types";

export async function saveChatHistory(
  userId: string,
  description: string,
  propertyDetails?: PropertyDetails,
  configureOutput?: ConfigureOutput,
  additionalNotes?: string,
  imageDescriptions?: string[],
  rate?: number,
  comment?: string,
): Promise<string> {
  const query = `
		INSERT INTO chat_history (
			user_id, description, property_details, configure_output, 
			additional_notes, image_descriptions, rate, comment
		) 
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
		RETURNING id
	`;
  const values = [
    userId,
    description,
    propertyDetails ? JSON.stringify(propertyDetails) : null,
    configureOutput ? JSON.stringify(configureOutput) : null,
    additionalNotes || null,
    imageDescriptions ? JSON.stringify(imageDescriptions) : null,
    rate || null,
    comment || null,
  ];
  const result = await pool.query(query, values);
  return result.rows[0].id;
}

export async function getChatHistory(
  userId: string,
  page: number = 1,
  pageSize: number = 10,
) {
  const offset = (page - 1) * pageSize;
  const query = `
    SELECT id, description, created_at
    FROM chat_history
    WHERE user_id = $1
    ORDER BY created_at DESC
    LIMIT $2 OFFSET $3
  `;
  const countQuery = `
    SELECT COUNT(*) as total
    FROM chat_history
    WHERE user_id = $1
  `;

  const values = [userId, pageSize, offset];
  const result = await pool.query(query, values);
  const countResult = await pool.query(countQuery, [userId]);

  const totalCount = parseInt(countResult.rows[0].total);
  const totalPages = Math.ceil(totalCount / pageSize);

  return {
    chatHistory: result.rows,
    pagination: {
      currentPage: page,
      pageSize: pageSize,
      totalCount: totalCount,
      totalPages: totalPages,
    },
  };
}

export async function deleteChatHistory(
  userId: string,
  id: string,
): Promise<void> {
  const query = "DELETE FROM chat_history WHERE user_id = $1 AND id=$2";
  const values = [userId, id];
  await pool.query(query, values);
}

export async function updateRateAndComment(
  id: string,
  rate: number,
  comment?: string,
): Promise<void> {
  const query = `
		UPDATE chat_history
		SET rate = $1, comment = $2
		WHERE id = $3
	`;
  const values = [rate, comment || null, id];
  await pool.query(query, values);
}

/*
CREATE TABLE chat_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    property_details JSONB,
    configure_output JSONB,
    additional_notes TEXT,
    image_descriptions JSONB,
    rate INTEGER,
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
*/
