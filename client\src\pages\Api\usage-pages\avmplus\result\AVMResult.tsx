import React, { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "../../../../../redux/hooks";
import { useFormContext } from "../../components/FormContext";
import { Redirect, useHistory } from "react-router-dom";
import RedoButton from "../../components/RedoButtons";
import ResultSkeleton from "../../sustainability/components/ResultSkeleton";
import Result from "../components/Result";
import { ResultSummary } from "../components/ResultSummary";
import {
  clearAvmResults,
  modifyAvmQueries,
} from "../../../../../redux/actions/avmActions";
import {
  clearAvmPlusResults,
  modifyAvmPlusQueries,
} from "../../../../../redux/actions/avmPlusActions";

const AVMResult = () => {
  const { result, loading } = useAppSelector((state) => state.avmPlus);
  const { buildingPhoto, map, setPostalAddress } = useFormContext();
  const history = useHistory();
  const dispatch = useAppDispatch();

  const clearResults = () => {
    dispatch(clearAvmPlusResults());
    history.push("/avmplus");
  };

  const modifyResults = () => {
    dispatch(modifyAvmPlusQueries());
    history.push("/avmplus");
  };

  useEffect(() => {
    const property = result as any;
    if (Object.keys(result).length > 0) {
      setPostalAddress(
        `${property.postcode}-${property.housenumber}-${
          property.houseaddition || ""
        }`,
      );
    }
  }, [result, setPostalAddress]);

  if (Object.keys(result).length === 0 && !loading) {
    return <Redirect to="/avmplus" />;
  }

  return (
    <>
      {loading ? (
        <ResultSkeleton />
      ) : (
        <>
          <ResultSummary
            property={result}
            buildingPhoto={buildingPhoto}
            map={map}
          />
          <Result property={result} buildingPhoto={buildingPhoto} map={map} />
          <RedoButton modify={modifyResults} clear={clearResults} />
        </>
      )}
    </>
  );
};

export default AVMResult;
