import AWS from "aws-sdk";
import AppError from "../../utils/appError";

if (!process.env.AWS_ACCESS_KEY || !process.env.AWS_SECRET_ACCESS_KEY) {
  console.error("AWS credentials not found in environment variables");
  throw new Error("AWS credentials are required");
}

AWS.config.update({
  region: "eu-west-1",
  credentials: new AWS.Credentials(
    process.env.AWS_ACCESS_KEY!,
    process.env.AWS_SECRET_ACCESS_KEY!,
  ),
});
const apiGateway = new AWS.APIGateway({
  accessKeyId: process.env.AWS_ACCESS_KEY,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  endpoint: "https://apigateway.eu-west-1.amazonaws.com",
});

export class AWSService {
  /**
   * Creates a new API key in AWS
   */
  static async createApiKey(email: string): Promise<AWS.APIGateway.ApiKey> {
    try {
      return await apiGateway
        .createApiKey({
          name: `${email}-${Date.now()}`,
          enabled: true,
          generateDistinctId: true,
        })
        .promise();
    } catch (error: any) {
      if (error.code === "AccessDeniedException") {
        throw new AppError(
          "AWS credentials lack permission to create API key",
          403,
        );
      }
      if (error.code === "LimitExceededException") {
        throw new AppError("AWS API key limit exceeded for this account", 429);
      }
      throw new AppError(
        `Failed to create API key: ${error.message}`,
        error.statusCode || 500,
      );
    }
  }

  /**
   * Creates a new usage plan in AWS
   */
  static async createUsagePlan(
    name: string,
  ): Promise<AWS.APIGateway.UsagePlan> {
    try {
      return await apiGateway
        .createUsagePlan({
          name,
          throttle: {
            burstLimit: 100,
            rateLimit: 50,
          },
          quota: {
            limit: 1000,
            period: "MONTH",
          },
          apiStages: [
            {
              apiId: process.env.AWS_API_ID!,
              stage: process.env.AWS_API_STAGE!,
            },
          ],
        })
        .promise();
    } catch (error: any) {
      if (error.code === "AccessDeniedException") {
        throw new AppError(
          "AWS credentials lack permission to create usage plan",
          403,
        );
      }
      if (error.code === "LimitExceededException") {
        throw new AppError(
          "AWS usage plan limit exceeded for this account",
          429,
        );
      }
      throw new AppError(
        `Failed to create usage plan: ${error.message}`,
        error.statusCode || 500,
      );
    }
  }

  /**
   * Links an API key to a usage plan
   */
  static async linkKeyToUsagePlan(
    usagePlanId: string,
    keyId: string,
  ): Promise<AWS.APIGateway.UsagePlanKey> {
    try {
      return await apiGateway
        .createUsagePlanKey({
          usagePlanId,
          keyId,
          keyType: "API_KEY",
        })
        .promise();
    } catch (error: any) {
      if (error.code === "NotFoundException") {
        throw new AppError("Usage plan or API key not found", 404);
      }
      if (error.code === "ConflictException") {
        throw new AppError(
          "API key is already associated with this usage plan",
          409,
        );
      }
      if (error.code === "AccessDeniedException") {
        throw new AppError(
          "AWS credentials lack permission to link key to usage plan",
          403,
        );
      }
      throw new AppError(
        `Failed to link key to usage plan: ${error.message}`,
        error.statusCode || 500,
      );
    }
  }

  /**
   * Gets an API key from AWS
   */
  static async getApiKey(apiKey: string): Promise<AWS.APIGateway.ApiKey> {
    try {
      return await apiGateway
        .getApiKey({
          apiKey,
          includeValue: true,
        })
        .promise();
    } catch (error: any) {
      if (error.code === "NotFoundException") {
        throw new AppError(`API key with ID ${apiKey} not found`, 404);
      }
      if (error.code === "AccessDeniedException") {
        throw new AppError(
          "AWS credentials lack permission to access API key",
          403,
        );
      }
      throw new AppError(
        `Failed to get API key: ${error.message}`,
        error.statusCode || 500,
      );
    }
  }

  /**
   * Gets usage plans for an API key
   */
  static async getUsagePlansForKey(
    keyId: string,
  ): Promise<AWS.APIGateway.UsagePlanKeys> {
    try {
      const usagePlans = await apiGateway.getUsagePlans().promise();
      const keys = await Promise.all(
        usagePlans.items?.map(async (plan) => {
          try {
            await apiGateway
              .getUsagePlanKey({
                usagePlanId: plan.id!,
                keyId: keyId,
              })
              .promise();
            return {
              id: keyId,
              type: "API_KEY",
              value: keyId,
              name: plan.name,
              usagePlanId: plan.id,
            };
          } catch (err) {
            return null;
          }
        }) || [],
      );

      return {
        items: keys.filter(
          (key): key is NonNullable<typeof key> => key !== null,
        ),
        position: undefined,
      };
    } catch (error: any) {
      if (error.code === "NotFoundException") {
        throw new AppError(`API key with ID ${keyId} not found`, 404);
      }
      if (error.code === "AccessDeniedException") {
        throw new AppError(
          "AWS credentials lack permission to access usage plans",
          403,
        );
      }
      throw new AppError(
        `Failed to get usage plans: ${error.message}`,
        error.statusCode || 500,
      );
    }
  }

  /**
   * Deletes an API key
   */
  static async deleteApiKey(apiKeyId: string): Promise<void> {
    try {
      console.log("Attempting to delete API key:", {
        keyId: apiKeyId,
        region: process.env.AWS_REGION || "eu-west-1",
        hasAccessKey: !!process.env.AWS_ACCESS_KEY,
        hasSecretKey: !!process.env.AWS_SECRET_ACCESS_KEY,
      });

      // Create a new API Gateway instance for this specific call
      if (!process.env.AWS_ACCESS_KEY || !process.env.AWS_SECRET_ACCESS_KEY) {
        throw new AppError("AWS credentials not found in environment", 500);
      }

      const gateway = new AWS.APIGateway({
        region: "eu-west-1",
        credentials: {
          accessKeyId: process.env.AWS_ACCESS_KEY!,
          secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
        },
      });

      await gateway
        .deleteApiKey({
          apiKey: apiKeyId,
        })
        .promise();
    } catch (error: any) {
      if (error.code === "NotFoundException") {
        throw new AppError(`API key with ID ${apiKeyId} not found`, 404);
      }
      if (error.code === "AccessDeniedException") {
        throw new AppError(
          "AWS credentials lack permission to delete API key",
          403,
        );
      }
      throw new AppError(
        `Failed to delete AWS resources: ${error.message}`,
        error.statusCode || 500,
      );
    }
  }

  /**
   * Gets all usage plans
   */
  static async getAllUsagePlans(): Promise<AWS.APIGateway.UsagePlans> {
    try {
      return await apiGateway.getUsagePlans().promise();
    } catch (error: any) {
      if (error.code === "AccessDeniedException") {
        throw new AppError(
          "AWS credentials lack permission to access usage plans",
          403,
        );
      }
      throw new AppError(
        `Failed to get usage plans: ${error.message}`,
        error.statusCode || 500,
      );
    }
  }

  /**
   * Gets a specific usage plan by ID
   */
  static async getUsagePlanById(
    usagePlanId: string,
  ): Promise<AWS.APIGateway.UsagePlan> {
    try {
      return await apiGateway
        .getUsagePlan({
          usagePlanId,
        })
        .promise();
    } catch (error: any) {
      if (error.code === "NotFoundException") {
        throw new AppError(`Usage plan with ID ${usagePlanId} not found`, 404);
      }
      if (error.code === "AccessDeniedException") {
        throw new AppError(
          "AWS credentials lack permission to access usage plan",
          403,
        );
      }
      throw new AppError(
        `Failed to get usage plan: ${error.message}`,
        error.statusCode || 500,
      );
    }
  }

  /**
   * Creates a custom usage plan
   */
  static async createCustomUsagePlan(planData: {
    name: string;
    description?: string;
    quota?: {
      limit: number;
      period: "DAY" | "WEEK" | "MONTH";
    };
    throttle?: {
      burstLimit: number;
      rateLimit: number;
    };
  }): Promise<AWS.APIGateway.UsagePlan> {
    try {
      return await apiGateway
        .createUsagePlan({
          name: planData.name,
          description: planData.description,
          quota: planData.quota,
          throttle: planData.throttle,
          apiStages: [
            {
              apiId: process.env.AWS_API_ID!,
              stage: process.env.AWS_API_STAGE!,
            },
          ],
        })
        .promise();
    } catch (error: any) {
      if (error.code === "AccessDeniedException") {
        throw new AppError(
          "AWS credentials lack permission to create usage plan",
          403,
        );
      }
      if (error.code === "LimitExceededException") {
        throw new AppError(
          "AWS usage plan limit exceeded for this account",
          429,
        );
      }
      throw new AppError(
        `Failed to create usage plan: ${error.message}`,
        error.statusCode || 500,
      );
    }
  }

  /**
   * Updates an existing usage plan
   */
  static async updateUsagePlan(
    usagePlanId: string,
    updateData: {
      name?: string;
      description?: string;
      quota?: {
        limit: number;
        period: "DAY" | "WEEK" | "MONTH";
      };
      throttle?: {
        burstLimit: number;
        rateLimit: number;
      };
    },
  ): Promise<AWS.APIGateway.UsagePlan> {
    try {
      return await apiGateway
        .updateUsagePlan({
          usagePlanId,
          patchOperations: [
            ...(updateData.name
              ? [
                  {
                    op: "replace",
                    path: "/name",
                    value: updateData.name,
                  },
                ]
              : []),
            ...(updateData.description
              ? [
                  {
                    op: "replace",
                    path: "/description",
                    value: updateData.description,
                  },
                ]
              : []),
            ...(updateData.quota
              ? [
                  {
                    op: "replace",
                    path: "/quota/limit",
                    value: updateData.quota.limit.toString(),
                  },
                  {
                    op: "replace",
                    path: "/quota/period",
                    value: updateData.quota.period,
                  },
                ]
              : []),
            ...(updateData.throttle
              ? [
                  {
                    op: "replace",
                    path: "/throttle/burstLimit",
                    value: updateData.throttle.burstLimit.toString(),
                  },
                  {
                    op: "replace",
                    path: "/throttle/rateLimit",
                    value: updateData.throttle.rateLimit.toString(),
                  },
                ]
              : []),
          ],
        })
        .promise();
    } catch (error: any) {
      throw new AppError(
        `Failed to update usage plan: ${error.message}`,
        error.statusCode || 500,
      );
    }
  }

  /**
   * Deletes a usage plan
   */
  static async deleteUsagePlan(usagePlanId: string): Promise<void> {
    try {
      await apiGateway
        .deleteUsagePlan({
          usagePlanId,
        })
        .promise();
    } catch (error: any) {
      if (error.code === "NotFoundException") {
        throw new AppError(`Usage plan with ID ${usagePlanId} not found`, 404);
      }
      if (error.code === "AccessDeniedException") {
        throw new AppError(
          "AWS credentials lack permission to delete usage plan",
          403,
        );
      }
      throw new AppError(
        `Failed to delete usage plan: ${error.message}`,
        error.statusCode || 500,
      );
    }
  }

  /**
   * Adds test credit by updating the API key's usage record
   */
  static async addTestCredit(
    usagePlanId: string,
    keyId: string,
    additionalCredit: number,
  ): Promise<void> {
    try {
      // Format and validate the date in yyyy-MM-dd format
      const now = new Date();
      const startDate = now.toISOString().split("T")[0]; // This ensures yyyy-MM-dd format

      // Validate the date format
      if (!/^\d{4}-\d{2}-\d{2}$/.test(startDate)) {
        throw new AppError("Invalid usage date format", 400);
      }

      // Get the usage plan details to get the quota
      const usagePlan = await apiGateway
        .getUsagePlan({ usagePlanId })
        .promise();

      // Get current usage for this API key
      const usage = await apiGateway
        .getUsage({
          usagePlanId,
          keyId,
          startDate,
          endDate: startDate, // Same as startDate since we only want current month
          limit: 1000,
        })
        .promise();

      // Calculate current usage
      const currentUsage = usage.items?.[0]?.values?.[0] || 0;

      // Calculate the new remaining quota by adding the additional credit
      const quotaLimit = usagePlan.quota?.limit || 0;
      const remainingQuota = Math.max(
        0,
        quotaLimit + additionalCredit - currentUsage,
      );

      // Update the usage for this API key
      await apiGateway
        .updateUsage({
          usagePlanId,
          keyId,
          patchOperations: [
            {
              op: "replace",
              path: "/remaining",
              value: remainingQuota.toString(),
            },
          ],
        })
        .promise();
    } catch (error: any) {
      throw new AppError(
        `Failed to add test credit: ${error.message}`,
        error.statusCode || 500,
      );
    }
  }
}
