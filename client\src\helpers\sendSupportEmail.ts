import axios from 'axios';

interface SupportEmail {
  email: string;
  subject: string;
  text: string;
}
type SupportEmailRes = {
  status: string;
  messageSent: boolean;
};
const sendSupportEmail = async (data: SupportEmail) => {
  try {
    const config = { headers: { 'Content-Type': 'application/json' } };
    const res = await axios.post<SupportEmailRes>(
      '/api/v1/mopsus/send-support-email',
      data,
      config,
    );
    return res.data.messageSent;
  } catch (error) {
    console.log(error);
  }
};
export default sendSupportEmail;
