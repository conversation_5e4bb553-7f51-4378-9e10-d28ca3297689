import React, { useEffect } from "react";
import { useAppDispatch, useAppSelector } from "../../../../../redux/hooks";
import { useFormContext } from "../../components/FormContext";
import { Redirect, useHistory } from "react-router-dom";
import RedoButton from "../../components/RedoButtons";
import {
  clearEnergyLabelResults,
  modifyEnergyLabelQueries,
} from "../../../../../redux/actions/energyLabelActions";
import ResultSkeleton from "../../sustainability/components/ResultSkeleton";
import Result from "../components/Result";
import { ResultSummary } from "../components/ResultSummary";
import { EnergyLabelData } from "../types";

// Type guard to validate the shape of the result
const isEnergyLabelData = (data: any): data is EnergyLabelData => {
  return (
    typeof data === "object" &&
    typeof data.post_code === "string" &&
    typeof data.house_number === "number"
  );
};

const EnergyLabelResult = () => {
  const { result, loading } = useAppSelector((state) => state.energyLabel);
  const { buildingPhoto, map, houseAddress, setPostalAddress } =
    useFormContext();
  const history = useHistory();
  const dispatch = useAppDispatch();

  const clearResults = () => {
    dispatch(clearEnergyLabelResults());
    history.push("/energylabel");
  };

  const modifyResults = () => {
    dispatch(modifyEnergyLabelQueries());
    history.push("/energylabel");
  };

  useEffect(() => {
    if (isEnergyLabelData(result)) {
      setPostalAddress(
        `${result.post_code}-${result.house_number}-${
          result.house_addition || ""
        }`,
      );
    }
  }, [result, setPostalAddress]);

  if (Object.keys(result).length === 0 && !loading) {
    return <Redirect to="/energylabel" />;
  }

  return (
    <>
      {loading ? (
        <ResultSkeleton />
      ) : (
        <>
          {isEnergyLabelData(result) ? (
            <>
              <ResultSummary
                property={result}
                buildingPhoto={buildingPhoto}
                map={map}
                houseAddress={houseAddress}
              />
              <Result
                property={result}
                buildingPhoto={buildingPhoto}
                map={map}
              />
              <RedoButton modify={modifyResults} clear={clearResults} />
            </>
          ) : (
            <div className="text-red-600">
              Invalid energy label data received from server
            </div>
          )}
        </>
      )}
    </>
  );
};

export default EnergyLabelResult;
