# Admin API Usage Monitoring and Rate Limiting Implementation

## Overview
This document outlines the enhanced implementation of API usage monitoring and rate limiting features for the Mopsus Data Platform admin interface, integrating with New Relic for usage tracking and AWS API Gateway for rate limiting.

## Table of Contents
1. [System Architecture](#system-architecture)
2. [API Endpoints](#api-endpoints)
3. [Services Implementation](#services-implementation)
4. [Rate Limiting Configuration](#rate-limiting-configuration)
5. [Monitoring and Alerts](#monitoring-and-alerts)
6. [Error Handling](#error-handling)

## System Architecture

### New Relic Integration
- Uses New Relic's GraphQL API to fetch API usage metrics
- Tracks API calls with detailed metrics including response codes
- Provides real-time and historical usage data

### AWS API Gateway Integration
- Manages API keys and usage plans
- Implements rate limiting and quota management
- Provides usage analytics through CloudWatch

## API Endpoints

### API Usage Monitoring

#### 1. Get API Usage Overview
```typescript
GET /admin/api-usage/overview
Query Parameters:
- startDate: ISO date string
- endDate: ISO date string
- groupBy: 'day' | 'week' | 'month'

Response:
{
    overview: {
        totalRequests: number,
        successRate: number,
        errorRate: number,
        avgResponseTime: number
    },
    statusCodes: {
        '200': number, // Successful requests
        '400': number, // Bad requests
        '422': number, // Validation errors
        '429': number, // Rate limit exceeded
        '500': number  // Server errors
    },
    topEndpoints: Array<{
        endpoint: string,
        calls: number,
        errorRate: number
    }>,
    timeSeriesData: Array<{
        timestamp: string,
        requests: number,
        errors: number
    }>
}
```

#### 2. Get User-Specific Usage
```typescript
GET /admin/api-usage/by-user/:userId
Query Parameters:
- startDate: ISO date string
- endDate: ISO date string

Response:
{
    user: {
        id: string,
        email: string,
        usagePlan: {
            id: string,
            name: string,
            quota: {
                limit: number,
                period: string,
                remaining: number
            },
            throttle: {
                rateLimit: number,
                burstLimit: number
            }
        }
    },
    usage: {
        current: {
            requests: number,
            errors: number,
            quotaUtilization: number
        },
        history: Array<{
            date: string,
            requests: number,
            errors: number
        }>,
        endpoints: Array<{
            path: string,
            requests: number,
            errorRate: number
        }>
    }
}
```

### Rate Limiting Management

#### 1. Get Usage Plans
```typescript
GET /admin/usage-plans
Response:
{
    plans: Array<{
        id: string,
        name: string,
        quota: {
            limit: number,
            period: string
        },
        throttle: {
            rateLimit: number,
            burstLimit: number
        },
        activeUsers: number,
        apiStages: Array<{
            apiId: string,
            stage: string
        }>
    }>
}
```

#### 2. Update Usage Plan
```typescript
PATCH /admin/usage-plans/:planId
Body:
{
    name?: string,
    quota?: {
        limit: number,
        period: 'DAY' | 'WEEK' | 'MONTH'
    },
    throttle?: {
        rateLimit: number,
        burstLimit: number
    }
}
```

## Services Implementation

### 1. Enhanced API Usage Service
```typescript
interface ApiUsageService {
    // Fetch usage from New Relic
    getNewRelicUsage(params: {
        apiKey: string,
        startDate: string,
        endDate: string
    }): Promise<{
        requests: number,
        errors: Record<string, number>
    }>;

    // Get AWS usage plan consumption
    getAwsUsagePlanUsage(params: {
        usagePlanId: string,
        keyId: string,
        startDate: string,
        endDate: string
    }): Promise<{
        usage: number,
        remaining: number
    }>;

    // Generate usage reports
    generateUsageReport(params: {
        userId?: string,
        startDate: string,
        endDate: string,
        format: 'csv' | 'pdf'
    }): Promise<Buffer>;
}
```

### 2. AWS Usage Plan Service
```typescript
interface AwsUsagePlanService {
    // Get usage plan details
    getUsagePlan(planId: string): Promise<AWS.APIGateway.UsagePlan>;
    
    // Update usage plan
    updateUsagePlan(params: {
        planId: string,
        updates: Partial<AWS.APIGateway.UpdateUsagePlanRequest>
    }): Promise<AWS.APIGateway.UsagePlan>;
    
    // Link/unlink API keys
    manageUsagePlanKeys(params: {
        planId: string,
        keyId: string,
        action: 'add' | 'remove'
    }): Promise<void>;
}
```

## Monitoring and Alerts

### Alert Types
1. Usage Alerts
   - Quota consumption > 80%
   - High error rates (>10%)
   - Unusual usage patterns

2. Rate Limiting Alerts
   - Frequent throttling
   - Burst limit exceeded
   - Usage plan quota exceeded

### Alert Configuration
```typescript
interface AlertConfig {
    type: 'quota' | 'errors' | 'throttling';
    threshold: number;
    period: 'hour' | 'day' | 'week';
    actions: {
        email?: string[];
        slack?: string;
        webhook?: string;
    };
}
```

## Error Handling

### Error Types
```typescript
enum ApiMonitoringErrorType {
    NEW_RELIC_ERROR = 'NEW_RELIC_ERROR',
    AWS_API_ERROR = 'AWS_API_ERROR',
    RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
    QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
    INVALID_USAGE_PLAN = 'INVALID_USAGE_PLAN'
}
```

## Implementation Steps

1. New Relic Integration
   - Set up GraphQL client for New Relic API
   - Implement usage data fetching
   - Create usage analytics dashboard

2. AWS API Gateway Integration
   - Enhance usage plan management
   - Implement rate limit configuration
   - Set up CloudWatch metrics

3. Admin Dashboard
   - Real-time usage monitoring
   - Usage plan management interface
   - Alert configuration

4. Reporting
   - Usage reports generation
   - Error rate analysis
   - Quota utilization tracking

5. Testing
   - Integration tests with New Relic
   - AWS API Gateway interaction tests
   - Rate limiting verification
