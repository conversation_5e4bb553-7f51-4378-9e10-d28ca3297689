import { FC, useState, useEffect, useCallback } from "react";
import Taxapi from "../../../../assets/images/taxapi.png";
import Woonu from "../../../../assets/images/woonnu.png";
import NN from "../../../../assets/images/national.png";
import Verduurzaming from "../../../../assets/images/Verduurzaming-v2.png";
import ReviewBadge from "./ReviewBadge";
import { FiChevronLeft, FiChevronRight } from "react-icons/fi";

const SignupRightSection: FC = () => {
  const [activeCard, setActiveCard] = useState(0);
  const totalCards = 3;
  const autoChangeInterval = 3000; // 3 seconds

  const nextCard = useCallback(() => {
    setActiveCard((prev) => (prev === totalCards - 1 ? 0 : prev + 1));
  }, [totalCards]);

  const prevCard = useCallback(() => {
    setActiveCard((prev) => (prev === 0 ? totalCards - 1 : prev - 1));
  }, [totalCards]);

  // Auto-change carousel
  useEffect(() => {
    const interval = setInterval(() => {
      nextCard();
    }, autoChangeInterval);

    return () => clearInterval(interval);
  }, [nextCard]);

  const renderDots = () => {
    return (
      <div className="flex justify-center mt-4 space-x-2">
        {Array.from({ length: totalCards }).map((_, index) => (
          <button
            key={index}
            onClick={() => setActiveCard(index)}
            className={`w-2 h-2 rounded-full ${
              activeCard === index ? "bg-white" : "bg-gray-400"
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </div>
    );
  };

  const renderSustainabilityCard = () => (
    <div className="bg-white rounded-lg p-6 shadow-lg w-full">
      <img
        src="/images/Signup_carousel_1.png"
        alt="Verduurzaming rapport"
        className="w-full h-[240px] object-contain"
      />
    </div>
  );

  const renderPropertyValuationCard = () => (
    <div className="bg-white rounded-lg p-6 shadow-lg w-full">
      <img
        src="/images/Signup_carousel_2.png"
        alt="Woningwaardering"
        className="w-full h-[240px] object-contain"
      />
    </div>
  );

  const renderEnergyEfficiencyCard = () => (
    <div className="bg-white rounded-lg p-6 shadow-lg w-full">
      <img
        src="/images/Signup_carousel_3.png"
        alt="Energie besparingsstatus"
        className="w-full h-[240px] object-contain"
      />
    </div>
  );

  const renderActiveCard = () => {
    switch (activeCard) {
      case 0:
        return renderSustainabilityCard();
      case 1:
        return renderPropertyValuationCard();
      case 2:
        return renderEnergyEfficiencyCard();
      default:
        return renderSustainabilityCard();
    }
  };

  return (
    <div className="w-full md:w-2/5 bg-[radial-gradient(circle_at_center,_#27AE60,_#214932)] p-8 hidden md:flex md:flex-col md:justify-center">
      <div className="max-w-md mx-auto w-full flex flex-col justify-evenly h-full">
        <div className="text-white mb-8">
          <h2 className="text-2xl font-bold mb-4">
            Vind de woningdata die je nodig hebt
          </h2>
          <p className="text-base">
            Toegang tot uitgebreide woningdata in Nederland met onze intuïtieve
            API's
          </p>
        </div>

        <div className="relative">
          <div className="flex justify-center items-center">
            <button
              onClick={prevCard}
              className="absolute left-0 z-10 p-1 bg-white bg-opacity-30 rounded-full text-white hover:bg-opacity-50"
              aria-label="Previous slide"
            >
              <FiChevronLeft size={24} />
            </button>

            <div className="w-full">{renderActiveCard()}</div>

            <button
              onClick={nextCard}
              className="absolute right-0 z-10 p-1 bg-white bg-opacity-30 rounded-full text-white hover:bg-opacity-50"
              aria-label="Next slide"
            >
              <FiChevronRight size={24} />
            </button>
          </div>
          {renderDots()}
        </div>

        <div className="mt-8 flex items-center justify-center">
          <ReviewBadge />
        </div>

        <div className="mt-8 flex justify-between items-center">
          <img src={Taxapi} alt="Taxapi" className="h-[100px] object-contain" />
          <img src={Woonu} alt="Woonnu" className="h-[100px] object-contain" />
          <img
            src={NN}
            alt="Nationale Nederlanden"
            className="h-[100px] object-contain"
          />
          {/* <img
            src={Verduurzaming}
            alt="Verduurzaming"
            className="h-[58px] object-contain"
          /> */}
        </div>
      </div>
    </div>
  );
};

export default SignupRightSection;
