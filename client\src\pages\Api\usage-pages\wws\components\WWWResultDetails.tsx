import React from "react";
import convertToEuFormat from "../../../../../helpers/convertToEuFormat";
import Table from "../../../../../components/Table";
import Subtitle from "../../../../../components/Subtitle";
import { keyTranslations } from "../translations";
import { AlertCircle } from "lucide-react";

export interface WWWResultInterface {
  detail?: string;
  total_wws_points: string;
  points_per_component: {
    [key: string]: string;
  };
}

const WWWResultDetails: React.FC<{ result: WWWResultInterface }> = ({
  result,
}) => {
  const translateKey = (key: string) => {
    return keyTranslations[key] || key.replace(/_/g, " ");
  };
  if (result.detail === "Not Found") {
    return (
      <div className="w-full max-w-2xl mx-auto bg-white rounded-lg shadow-md overflow-hidden">
        {/* Header */}
        <div className="px-6 pt-5 pb-2">
          <h2 className="text-2xl font-bold text-red-600 flex items-center gap-2">
            <AlertCircle className="h-6 w-6" />
            Geen Resultaten Gevonden
          </h2>
        </div>

        {/* Content */}
        <div className="px-6 pb-4">
          <div className="flex flex-col md:flex-row gap-6 items-center mb-4">
            <div className="flex-1">
              {/* Alert */}
              <div className="mb-4 p-4 border-l-4 border-red-500 bg-red-50 text-red-700 rounded">
                <p className="font-semibold">
                  Er konden geen WWS-punten worden berekend
                </p>
                <p className="text-sm mt-1">
                  We konden de benodigde informatie niet vinden voor deze
                  woning.
                </p>
              </div>

              <p className="text-gray-700 mb-3">
                Dit kan gebeuren om de volgende redenen:
              </p>
              <ul className="space-y-1.5 mb-4 text-gray-700">
                <li className="flex items-start">
                  <span className="mr-2 text-red-500">•</span>
                  Het adres niet gevonden kon worden
                </li>
                <li className="flex items-start">
                  <span className="mr-2 text-red-500">•</span>
                  Er onvoldoende gegevens beschikbaar zijn
                </li>
                <li className="flex items-start">
                  <span className="mr-2 text-red-500">•</span>
                  De woning niet in aanmerking komt voor WWS-punten
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <h2 className="text-2xl font-bold mb-4">WWS-punten Details</h2>
      <Table
        headers={["Component", "Punten"]}
        noDataText={""}
        tableItems={Object.entries(result.points_per_component).map(
          ([key, value]) => ({
            Component: translateKey(key),
            Punten: convertToEuFormat(value),
          }),
        )}
      />
      <Subtitle className="text-xl">
        {translateKey("total_wws_points")}:{" "}
        {convertToEuFormat(result.total_wws_points)}
      </Subtitle>
    </div>
  );
};

export default WWWResultDetails;
