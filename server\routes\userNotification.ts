import express from "express";
import pool from "../db";
import { protect } from "../controllers/authController";

const router = express.Router();

const fields = [
  "error_429",
  "error_403",
  "error_500",
  "error_422",
  "analytics_email_usage",
  "invoice_successful",
  "credit_depleted",
];

fields.forEach((field) => {
  router.patch(`/${field}/:id`, protect, async (req, res) => {
    const { id } = req.params;
    const { value } = req.body; // assume that client will send new value in body with key "value"

    try {
      await pool.query(
        `UPDATE user_notifications SET ${field} = $1 WHERE user_id = $2`,
        [value, id],
      );
      res.status(200).json({
        message: `Notification setting "${field}" for user_id ${id} updated successfully.`,
      });
    } catch (err: any) {
      res.status(500).json({ error: err.message });
    }
  });
});

router.get("/:id", protect, async (req, res) => {
  const { id } = req.params;

  try {
    const { rows } = await pool.query(
      `SELECT * FROM user_notifications WHERE user_id = $1`,
      [id],
    );

    if (rows.length > 0) {
      res.json(rows[0]); // Return the first (and only) matching row
    } else {
      res
        .status(404)
        .json({ message: `No notification settings found for user_id ${id}` });
    }
  } catch (err: any) {
    res.status(500).json({ error: err.message });
  }
});

export default router;
