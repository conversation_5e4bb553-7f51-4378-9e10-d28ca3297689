/**
 * Authentication API Documentation
 * This file contains TypeScript interfaces and documentation for the authentication API endpoints.
 */

export interface AuthEndpoints {
  /**
   * User Sign In
   * POST /api/v1/auth/signin
   * Authenticates a user with email and password
   */
  signIn: {
    body: {
      /** User's email address */
      email: string;
      /** User's password */
      password: string;
    };
    response: {
      status: "success" | "error";
      user?: {
        user_id: string;
        email: string;
        first_name: string;
        last_name: string;
        company?: string;
        kvk?: string;
        api_key: string;
        api_key_id: string;
        current_usage_plan: string;
        created_at: string;
        active: boolean;
      };
      message?: string;
    };
  };

  /**
   * Load User
   * GET /api/v1/auth/load
   * Loads the current authenticated user's data
   * @requires Authentication
   */
  loadUser: {
    response: {
      status: "success" | "error";
      user?: {
        user_id: string;
        email: string;
        first_name: string;
        last_name: string;
        company?: string;
        kvk?: string;
        api_key: string;
        api_key_id: string;
        current_usage_plan: string;
        created_at: string;
        active: boolean;
      };
    };
  };

  /**
   * Logout
   * POST /api/v1/auth/logout
   * Logs out the current user and clears their session
   * @requires Authentication
   */
  logout: {
    response: {
      status: "success" | "error";
      message: string;
    };
  };
}
