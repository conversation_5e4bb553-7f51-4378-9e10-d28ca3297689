export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "ASC" | "DESC";
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export const DEFAULT_PAGE_SIZE = 10;
export const MAX_PAGE_SIZE = 100;

export function getPaginationParams(params: PaginationParams): {
  offset: number;
  limit: number;
  sortBy: string;
  sortOrder: string;
} {
  const page = Math.max(1, params.page || 1);
  const limit = Math.min(
    MAX_PAGE_SIZE,
    Math.max(1, params.limit || DEFAULT_PAGE_SIZE),
  );
  const offset = (page - 1) * limit;
  const sortBy = params.sortBy || "created_at";
  const sortOrder = params.sortOrder || "DESC";

  return {
    offset,
    limit,
    sortBy,
    sortOrder,
  };
}

export function createPaginatedResponse<T>(
  data: T[],
  totalItems: number,
  params: PaginationParams,
): PaginatedResponse<T> {
  const limit = Math.min(
    MAX_PAGE_SIZE,
    Math.max(1, params.limit || DEFAULT_PAGE_SIZE),
  );
  const currentPage = Math.max(1, params.page || 1);
  const totalPages = Math.ceil(totalItems / limit);

  return {
    data,
    pagination: {
      currentPage,
      totalPages,
      totalItems,
      itemsPerPage: limit,
      hasNextPage: currentPage < totalPages,
      hasPreviousPage: currentPage > 1,
    },
  };
}
