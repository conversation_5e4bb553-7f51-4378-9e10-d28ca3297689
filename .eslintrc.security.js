// Security-focused ESLint configuration for CI/CD pipeline
// This is a standalone configuration to avoid plugin conflicts

module.exports = {
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: "module",
    ecmaFeatures: {
      jsx: true,
    },
  },
  plugins: ["@typescript-eslint", "@microsoft/sdl", "security"],
  extends: ["eslint:recommended", "@typescript-eslint/recommended"],
  rules: {
    // Microsoft SDL (Security Development Lifecycle) rules
    "@microsoft/sdl/no-html-method": "error",
    "@microsoft/sdl/no-insecure-url": "error",
    "@microsoft/sdl/no-unsafe-alloc": "error",
    "@microsoft/sdl/no-winjs-html-unsafe": "error",
    "@microsoft/sdl/no-msapp-exec-unsafe": "error",
    "@microsoft/sdl/no-cookies": "warn",
    "@microsoft/sdl/no-document-domain": "error",
    "@microsoft/sdl/no-document-write": "error",
    "@microsoft/sdl/no-inner-html": "error",
    "@microsoft/sdl/no-postmessage-star-origin": "error",

    // Security plugin rules
    "security/detect-buffer-noassert": "error",
    "security/detect-child-process": "warn",
    "security/detect-disable-mustache-escape": "error",
    "security/detect-eval-with-expression": "error",
    "security/detect-new-buffer": "error",
    "security/detect-no-csrf-before-method-override": "error",
    "security/detect-non-literal-fs-filename": "warn",
    "security/detect-non-literal-regexp": "warn",
    "security/detect-non-literal-require": "warn",
    "security/detect-object-injection": "warn",
    "security/detect-possible-timing-attacks": "warn",
    "security/detect-pseudoRandomBytes": "error",
    "security/detect-unsafe-regex": "error",

    // Remove no-secrets rules for now to avoid dependency issues

    // Additional security-focused rules
    "no-eval": "error",
    "no-implied-eval": "error",
    "no-new-func": "error",
    "no-script-url": "error",
    "no-alert": "error",
    "no-console": "warn",
    "no-debugger": "error",

    // Prevent dangerous patterns
    "no-unused-vars": [
      "error",
      {
        vars: "all",
        args: "after-used",
        ignoreRestSiblings: false,
      },
    ],
    "no-undef": "error",
    "no-redeclare": "error",
    "no-shadow": "error",

    // TypeScript specific security rules (simplified to avoid parser issues)
    "@typescript-eslint/no-explicit-any": "warn",
  },
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  overrides: [
    {
      files: ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"],
      rules: {
        // Relax some rules for test files
        "no-secrets/no-secrets": "off",
        "@microsoft/sdl/no-cookies": "off",
        "security/detect-non-literal-require": "off",
      },
    },
  ],
};
