import AVM from "./avm+.png";
import AVMPlus from "./AVM+-Photoroom.png";
import Listing from "./Listing_price.png";
import Ecovalue from "./sustainability.png";
import ImageApi from "./labelling-icon.png";
import LocationData from "./location-icon.png";
import MoveData from "./movers-icon.png";
import WOZ from "./woz-icon.png";
import ReferenceApi from "./interactivereference-icon.png";
import ReferenceImage from "./interactivereference-icon.png";
import TransactionAPI from "./kadastertransactions-icon.png";
import ConditionScore from "./bathroomcondition-icon.png";
import ObjectGeometry from "./buildinggeometry-icon.png";
import EnergyLabel from "./nta8800.png";
import EnergyInsightImg from "./house-shape-energy-consumption-effectivity-rating-chart-3d-illustration_764664-13583.jpg";
import ObjectData from "./housingfeatures-icon.png";
import solarscan from "./solarscan.png";
import rentalReference from "./rentalreference.png";
import autosuggest from "./autosuggest.jpg";
import rebuild from "./rebuildvalue-icon.png";
import energyClimate from "./Energie-klimaat.png";
import WWS from "./wws-removebg-preview.png";

export { AVM };

// export {BAG}

export { Ecovalue };

export { ImageApi };

export { LocationData };

export { MoveData };

export { WOZ };

export { ReferenceApi };

export { ReferenceImage };

export { TransactionAPI };

export { ConditionScore };
export { ObjectGeometry };
export { EnergyLabel };
export { EnergyInsightImg };
export { ObjectData };
export { solarscan };
export { rentalReference };
export { rebuild };
export { autosuggest };
export { energyClimate };
export { WWS };
export { AVMPlus };
export { Listing };
