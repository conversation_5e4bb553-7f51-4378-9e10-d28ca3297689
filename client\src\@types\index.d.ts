import { AsyncThunk } from "@reduxjs/toolkit";
import <PERSON><PERSON> from "stripe";
import { ApiOutputPayload } from "../helpers/createApiThunk";

interface User {
  two_factor_enabled: boolean;
  two_factor_type: "email" | "authenticator" | undefined;
  user_id: string;
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  role: "user" | "admin";
  active: boolean;
  password_reset_token: string;
  password_reset_token_expiry: Date;
  api_key: string;
  api_key_id: string;
  stripe_customer_id: string;
  current_usage_plan: string;
  receive_email: boolean;
  created_at: Date;
  updated_at: Date;
  plan_changed_at: Date;
  usage_tracked_at: Date;
  metered_billing: boolean;
  transaction_api_key: string;
  transaction_usage_plan: string;
  transaction_api_key_id: string;
  company: string;
  kvk: string;
}

export interface ApiOutput {
  Output: {
    BagID: string;
    PostCode: string;
    HouseNumber: number;
    HouseAddition?: string;
    Street: string;
    City: string;
    Latitude: number;
    Longitude: number;
    HouseType: string;
    BuildYear: number;
    OuterSurfaceArea: number;
  };
}

export interface FormOptions {
  postcode: string;
  housenumber: string;
  houseaddition?: string;
  [x: string]: string;
}
interface FormContextType {
  formValues: FormOptions | FormOptionsSustainability;
  errors: ValidationError;
  buildingPhoto: string;
  map: string;
  houseAddress: string;
  setFormValues: React.Dispatch<
    React.SetStateAction<FormOptions | FormOptionsSustainability>
  >;
  setPostalAddress: React.Dispatch<React.SetStateAction<string>>;
  validate: (
    input: ValidationInput,
    customRules?: ValidationRules,
    customErrorMessages?: ErrorMessages,
  ) => ValidationErrors;
  fetchAddressData: () => Promise<void>;
}

export interface FormOptionsSustainability {
  post_code?: string;
  house_number?: string;
  target_label?: string;
  fetch_definitive_label?: number;
  lock_measures?: any;
  [x: string]: any;
}

type InputProps = {
  name: string;
  label?: string;
  placeholder?: string;
  type?: string;
  tooltip?: string;
  options?: { label: string; value: string | number }[];
  value?: string | number | boolean;
  checked?: boolean;
  required?: boolean;
  dependsOn?: {
    field: string;
    value: string;
  };
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
};

export interface FormOptionLocation {
  searchString: string;
}

export type ApiFormProps = {
  postAction: AsyncThunk<
    ApiOutputPayload,
    {
      formData: any;
      apiKey: string;
    },
    any
  >;
  apiKey?: string;
  savedQueries: Partial<FormOptions>;
  demo: boolean;
};
interface CreateSubscriptionPayload {
  customerId: string;
  paymentMethodId: string | PaymentMethod;
  apikey: string;
  meteredPriceId: string;
  recurringPriceId: string;
}
interface CancelSubscriptionPayload {
  apikey: string;
  customerId: string;
  planId: string;
}

interface ComingInvoiceResponse {
  product: Stripe.Response<Stripe.Product>;
  comingEvent: Stripe.Response<Stripe.Invoice>;
  subscription: Stripe.Response<Stripe.Subscription>;
}

type InvoiceResponse = Stripe.Response<Stripe.ApiList<Stripe.Invoice>>;
interface ValidationInput {
  firstName?: string;
  lastName?: string;
  name?: string;
  email?: string;
  password?: string;
  kvk?: string;
  company?: string;
  vat?: string;
  [x: string]: any;
}
type FormField = {
  label: string;
  name: string;
  type: string;
};
interface Message {
  id: string;
  title: string;
  message: string;
  created_at: string;
  opened: boolean;
}
interface EmailSettings {
  error_429: boolean;
  error_403: boolean;
  error_500: boolean;
  error_422: boolean;
  analytics_email_usage: boolean;
  invoice_successful: boolean;
  credit_depleted: boolean;
  two_factor_enabled: boolean;
  two_factor_type?: "email" | "authenticator";
  user_id: string;
}

interface TwoFactorResponse {
  status: string;
  message: string;
  data?: {
    qrCode?: string;
    secret?: string;
  };
}

interface OnboardingQuestion {
  question_id: string;
  question_text: string;
  description: string;
  step: number;
  options: { option: string; rank: number }[];
}
interface Log {
  api_name: string;
  requestTime: string;
  status: string;
}

interface PropertyDetails {
  yearOfConstruction: string;
  location: string;
  area: string;
  propertyType: string;
  facilities: string;
}
interface ConfigureOutput {
  tone: string;
  targetAudience: string;
  languagePreference: string;
  descriptionLength: string;
}
declare global {
  interface Window {
    gtag?: any;
  }
}

export interface FormOptionsWWS extends FormOptionsSustainability {
  living_area_m2?: number;
  surface_area_other_spaces?: number;
  attic_space_area?: number;
  inner_surface_area?: number;
  countertop_lenght?: number;
  private_outdoor_space_m2?: number;
  renovation_expenses?: number;
  toilet_installed_in_bathroom_or_shower?: boolean;
  fixed_staircase?: boolean;
  energy_label_available?: boolean;
  energy_performance_payment?: boolean;
  countertop_with_base_cabinets?: boolean;
  sanitary_facilities?: boolean;
  facilities_for_disabled?: boolean;
  carport?: boolean;
  extra_quality_above_100m2?: boolean;
  fixed_staircase?: boolean;
  no_of_heated_rooms?: number;
  no_of_other_rooms?: number;
  no_of_rooms_heat_as_service_cost?: number;
  energy_performance_payment?: boolean;
  inner_surface_area?: number;
  validity?: string;
  sanitary_facilities?: boolean;
  care_home?: boolean;
  investment_for_bath_shower_extra_quality?: number;
  monument?: boolean;
  [key: string]: string | number | boolean | undefined;
}
