import React from "react";
import { Redirect } from "react-router";
import { useAppSelector } from "../../../redux/hooks";
import Loading from "../../Loading";
import AutoSuggestForm from "./AutoSuggestForm";
import { FormProvider } from "../../../pages/Api/usage-pages/components/FormContext";

const AutoSuggest = () => {
  const { loading, result } = useAppSelector((state) => state.autosuggest);

  if (loading) {
    return <Loading />;
  }
  return (
    <FormProvider>
      {Object.keys(result).length > 0 ? (
        <Redirect
          to={{
            pathname: "/autosuggest-result",
          }}
        />
      ) : (
        <AutoSuggestForm />
      )}
    </FormProvider>
  );
};

export default AutoSuggest;
