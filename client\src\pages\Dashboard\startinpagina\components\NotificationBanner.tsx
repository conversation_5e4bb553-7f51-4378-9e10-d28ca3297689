import React, { useEffect, useState } from "react";
import { FiX, FiCheck } from "react-icons/fi";
import Text from "../../../../components/Text";
import { Link } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "../../../../redux/hooks";
import { getOnboardingState } from "../../../../redux/actions/onboardingActions";

interface NotificationBannerProps {
  onClose: () => void;
}

const NotificationBanner: React.FC<NotificationBannerProps> = ({ onClose }) => {
  const dispatch = useAppDispatch();
  const { onboardingSteps } = useAppSelector((state) => state.onboarding);
  const [stepsLeft, setStepsLeft] = useState<number>(0);

  useEffect(() => {
    // Fetch onboarding state if not already loaded
    if (!onboardingSteps) {
      dispatch(getOnboardingState());
    }
  }, [dispatch, onboardingSteps]);

  useEffect(() => {
    if (onboardingSteps) {
      // Count the number of steps that are not completed
      const totalSteps = 6; // Based on the initialChecklistItems in get-started
      const completedSteps = onboardingSteps.length;
      setStepsLeft(totalSteps - completedSteps);
    }
  }, [onboardingSteps]);

  return (
    <div className="w-full bg-green-50 border border-green-100 rounded-lg p-4 mb-6 flex items-center justify-between">
      <div className="flex items-center">
        <div className="bg-green-100 rounded-full p-2 mr-3">
          <FiCheck className="text-green-600" size={16} />
        </div>
        <div>
          <Text className="text-gray-800 font-medium">
            Je bent bijna klaar met het opzetten van je account!
          </Text>
          <Text className="text-gray-600 text-sm">
            {stepsLeft > 0
              ? `Nog ${stepsLeft} ${
                  stepsLeft === 1 ? "stap" : "stappen"
                } te voltooien in minder dan 5 minuten.`
              : "Alle stappen zijn voltooid!"}
          </Text>
        </div>
      </div>

      <div className="flex items-center">
        <Link
          to="/dashboard/aandeslag"
          className="text-green-600 hover:text-green-700 text-sm font-medium mr-4 hover:underline"
        >
          {stepsLeft > 0 ? "Voltooien taken" : "Bekijk voltooide taken"}
        </Link>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-gray-500"
          aria-label="Close notification"
        >
          <FiX size={20} />
        </button>
      </div>
    </div>
  );
};

export default NotificationBanner;
