import { InfoIcon } from "lucide-react";

const NoAppartmentInfo = () => {
  return (
    <div className="flex">
      <InfoIcon className="inline-block mr-2 text-[#F2C94C]" />
      <span className="flex items-center gap-2 text-sm italic text-[#828282]">
        Let op: het is nog niet mogelijk adviezen te genereren voor
        appartementen (dit is in ontwikkeling)
      </span>
    </div>
  );
};

export default NoAppartmentInfo;
