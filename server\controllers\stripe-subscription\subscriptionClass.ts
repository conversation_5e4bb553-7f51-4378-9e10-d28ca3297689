import Pool from "../../db";
import {
  updateContact,
  getContactByEmail,
  isInList,
} from "../../utils/sendgridContactHandler";
import {
  STRIPE_3000_YEARLY_RECURRING_TEST,
  STRIPE_9000_YEARLY_RECURRING_TEST,
  STRIPE_24000_YEARLY_RECURRING_TEST,
  STRIPE_3000_YEARLY_RECURRING,
  STRIPE_9000_YEARLY_RECURRING,
  STRIPE_24000_YEARLY_RECURRING,
  STRIPE_20_DAILY,
  STRIPE_100_DAILY,
  STRIPE_250,
  STRIPE_20_DAILY_TEST,
  STRIPE_100_DAILY_TEST,
  STRIPE_250_DAILY_TEST,
  SENDGRID_LIST_UNLIMITED_MONTHLY_PAIDUSERS,
  STRIPE_1200_YEARLY_RECURRING,
} from "../../utils/constants";
import logger from "../../utils/logger";
import {
  updateUsagePlanKeyOnSubscription,
  updateUsagePlanKeyOnTransactionSubscription,
  updateUsagePlanKeyOnUnlimitedSubscription,
} from "../awsController";
import {
  SendGridContact,
  StripeObject,
  User,
  UserSubscription,
} from "../../@types";
import Stripe from "stripe";

class StripePlan {
  changedPlan: string;
  customerId: string | Stripe.Customer | Stripe.DeletedCustomer;
  subscriptionId: string;
  subscriptionItem: string;
  planId: string;
  user: User;
  table: string;

  constructor(
    dataObject: Stripe.Subscription,
    changedPlan: string,
    user: User,
    table: string,
  ) {
    this.customerId = dataObject.customer;
    this.changedPlan = changedPlan;
    this.subscriptionId = dataObject.id;
    this.subscriptionItem = dataObject.items.data[0].id;
    this.user = user;
    this.planId = dataObject.items.data[0].plan.id;
    this.table = table;
  }

  isYearlyPlan() {
    const yearlyPlanArr = [
      STRIPE_1200_YEARLY_RECURRING,
      STRIPE_3000_YEARLY_RECURRING,
      STRIPE_9000_YEARLY_RECURRING,
      STRIPE_24000_YEARLY_RECURRING,
      // TEST
      STRIPE_3000_YEARLY_RECURRING_TEST,
      STRIPE_9000_YEARLY_RECURRING_TEST,
      STRIPE_24000_YEARLY_RECURRING_TEST,
    ];
    return yearlyPlanArr.includes(this.planId);
  }

  isDailyQuota() {
    const dailyQuotaArr = [
      STRIPE_20_DAILY,
      STRIPE_100_DAILY,
      STRIPE_250, // TEST //
      STRIPE_20_DAILY_TEST,
      STRIPE_100_DAILY_TEST,
      STRIPE_250_DAILY_TEST,
    ];
    return dailyQuotaArr.includes(this.planId);
  }
}

export class MopsusPlan extends StripePlan {
  planDb = new PlanDb(
    this.customerId,
    this.table,
    this.subscriptionId,
    this.subscriptionItem,
  );

  private async createOrUpdateSubscription(updatedUsage: {
    status: string;
    received: boolean;
  }) {
    if (!updatedUsage.received) {
      return false;
    }

    const subscriptionExists = await this.planDb.checkTableForSubscription();

    if (subscriptionExists) {
      await this.planDb.updateSubscriptionInDb();
    } else {
      await this.planDb.saveSubscriptionInDb();
    }

    await this.updateMopsusPPU();
    const contact = await getContactByEmail(this.user);

    if (this.isYearlyPlan()) {
      await this.handleYearlyPlan(contact);
    } else {
      await this.handleMonthlyPlan(contact);
    }

    return true;
  }

  async createUsage() {
    const updatedUsage = await updateUsagePlanKeyOnSubscription(
      this.user.api_key_id,
      this.user.current_usage_plan,
      this.changedPlan,
      this.user.email,
    );

    try {
      return await this.createOrUpdateSubscription(updatedUsage);
    } catch (error) {
      console.error(error);
      return false;
    }
  }

  async deleteUsagePlan() {
    try {
      const updatedUsage = await updateUsagePlanKeyOnSubscription(
        this.user.api_key_id,
        this.user.current_usage_plan,
        process.env.AWS_MOPSUS_USAGE_PLAN_FREE!,
        this.user.email,
      );
      return updatedUsage.received && (await this.deactivateSubscription());
    } catch (err) {
      console.error("deleteUsagePlage", err);
      return false;
    }
  }

  private async updateMopsusPPU() {
    await this.planDb.updateSubscriptionChangeTimeMopsusPPU();
    await this.planDb.updateSubscriptionUsageTrackMopsusPPU();
  }

  private async handleYearlyPlan(contact: SendGridContact) {
    logger.info("this is a yearly plan");
    await this.planDb.setMeteredBilling(false);

    if (!isInList(contact, process.env.SENDGRID_LIST_ALTUM_YEARLY_PAIDUSERS!)) {
      await updateContact(
        this.user,
        process.env.SENDGRID_LIST_ALTUM_YEARLY_PAIDUSERS!,
      );
    }
  }

  private async handleMonthlyPlan(contact: SendGridContact) {
    logger.info("this is a monthly plan");
    await this.planDb.setMeteredBilling(!this.isDailyQuota());

    if (!isInList(contact, process.env.SENDGRID_LIST_ALTUM_PAIDUSERS!)) {
      await updateContact(
        this.user,
        process.env.SENDGRID_LIST_ALTUM_PAIDUSERS!,
      );
    }
  }

  private async deactivateSubscription() {
    await this.planDb.setSubscriptionActiveToFalse();
    await this.planDb.updateSubscriptionChangeTimeMopsusPPU();
    await this.planDb.setMeteredBilling(false);
    return true;
  }
}

export class TransactionPlan extends StripePlan {
  planDb = new PlanDb(
    this.customerId,
    this.table,
    this.subscriptionId,
    this.subscriptionItem,
  );

  private async createOrUpdateSubscription(updatedUsage: {
    status: string;
    received: boolean;
  }) {
    if (!updatedUsage.received) {
      return false;
    }

    const subscriptionExists = await this.planDb.checkTableForSubscription();

    if (subscriptionExists) {
      console.log("subscription Exists");
      await this.planDb.updateSubscriptionInDb();
    } else {
      console.log("subscription does not Exists");
      await this.planDb.saveSubscriptionInDb();
    }

    await this.update();
    const contact = await getContactByEmail(this.user);

    if (!isInList(contact, process.env.SENDGRID_LIST_KADASTER_PAIDUSERS!)) {
      await updateContact(
        this.user,
        process.env.SENDGRID_LIST_KADASTER_PAIDUSERS!,
      );
    }

    return true;
  }

  async createUsage() {
    const updatedUsage = await updateUsagePlanKeyOnTransactionSubscription(
      this.user,
      this.changedPlan,
    );

    try {
      return await this.createOrUpdateSubscription(updatedUsage);
    } catch (error) {
      console.error(error);
      return false;
    }
  }

  async deleteUsagePlan() {
    const updatedUsage = await updateUsagePlanKeyOnTransactionSubscription(
      this.user,
      this.changedPlan,
    );

    try {
      return updatedUsage.received && (await this.deactivateSubscription());
    } catch (err) {
      console.error(err);
      return false;
    }
  }

  private async update() {
    await this.planDb.updateSubscriptionChangeTime();
    await this.planDb.updateSubscriptionUsageTrack();
  }

  private async deactivateSubscription() {
    console.log("usage update received");
    await this.planDb.setSubscriptionActiveToFalse();
    await this.planDb.updateSubscriptionChangeTime();
    return true;
  }
}

export class UnlimitedMonthlyPlan extends StripePlan {
  planDb = new PlanDb(
    this.customerId,
    this.table,
    this.subscriptionId,
    this.subscriptionItem,
  );

  private async createOrUpdateSubscription(updatedUsage: {
    status: string;
    received: boolean;
  }) {
    if (!updatedUsage.received) {
      return false;
    }
    const subscriptionExists = await this.planDb.checkTableForSubscription();

    if (subscriptionExists) {
      await this.planDb.updateSubscriptionInDb();
    } else {
      await this.planDb.saveSubscriptionInDb();
    }

    await this.updateSubscriptionChangeTime();
    const contact = await getContactByEmail(this.user);

    if (!isInList(contact, SENDGRID_LIST_UNLIMITED_MONTHLY_PAIDUSERS)) {
      await updateContact(this.user, SENDGRID_LIST_UNLIMITED_MONTHLY_PAIDUSERS);
    }

    return true;
  }

  async createUsage() {
    const updatedUsage = await updateUsagePlanKeyOnUnlimitedSubscription(
      this.user,
      this.table,
      this.changedPlan,
    );

    try {
      return await this.createOrUpdateSubscription(updatedUsage);
    } catch (error) {
      console.error(error);
      return false;
    }
  }

  async deleteUsagePlan() {
    const updatedUsage = await updateUsagePlanKeyOnUnlimitedSubscription(
      this.user,
      this.table,
      this.changedPlan,
    );

    try {
      return updatedUsage.received && (await this.deactivateSubscription());
    } catch (err) {
      console.error(err);
      return false;
    }
  }

  private async updateSubscriptionChangeTime() {
    await this.planDb.updateSubscriptionChangeTime();
    // await this.planDb.updateSubscriptionUsageTrack();
  }

  private async deactivateSubscription() {
    await this.planDb.setSubscriptionActiveToFalse();
    await this.planDb.updateSubscriptionChangeTime();
    await this.planDb.setMeteredBilling(false);
    return true;
  }
}

export class PlanDb {
  customerId: string | Stripe.Customer | Stripe.DeletedCustomer | null;
  table: string;
  subscriptionId: string;
  subscriptionItem: string;
  constructor(
    customerId: string | Stripe.Customer | Stripe.DeletedCustomer | null,
    table: string,
    subscriptionId: string,
    subscriptionItem: string,
  ) {
    this.customerId = customerId;
    this.table = table;
    this.subscriptionId = subscriptionId;
    this.subscriptionItem = subscriptionItem;
  }

  async findUserByStripeId() {
    try {
      return await Pool.query(
        `SELECT * FROM users WHERE stripe_customer_id=$1`,
        [this.customerId],
      );
    } catch (err) {
      console.error("Query Error:", err);
    }
  }

  async checkTableForSubscription() {
    try {
      const subscriptionExists = await Pool.query(
        `SELECT * FROM ${this.table} where customer_id=$1`,
        [this.customerId],
      );
      if (subscriptionExists.rowCount && subscriptionExists.rowCount > 0) {
        return true;
      } else {
        false;
      }
    } catch (err) {
      console.error("Query Error:", err);
    }
  }

  async saveSubscriptionInDb() {
    try {
      return await Pool.query(
        `INSERT INTO ${this.table} (customer_id, subscription_id, subscription_item) VALUES($1, $2, $3) RETURNING *`,
        [this.customerId, this.subscriptionId, this.subscriptionItem],
      );
    } catch (err) {
      console.error("Query Error:", err);
    }
  }

  async updateSubscriptionInDb() {
    try {
      return await Pool.query<UserSubscription>(
        `UPDATE ${this.table} SET subscription_id=$1, subscription_item=$2, active=$3 where customer_id=$4 RETURNING *`,
        [this.subscriptionId, this.subscriptionItem, true, this.customerId],
      );
    } catch (err) {
      console.error("Query Error:", err);
    }
  }

  async updateSubscriptionChangeTimeMopsusPPU() {
    try {
      await Pool.query<User>(
        `UPDATE users SET plan_changed_at=$1 WHERE stripe_customer_id=$2 RETURNING *;`,
        [new Date(Date.now() - 100000), this.customerId],
      );
    } catch (err) {
      console.error("Query Error:", err);
    }
  }

  async updateSubscriptionUsageTrackMopsusPPU() {
    try {
      await Pool.query<User>(
        `UPDATE users SET usage_tracked_at=$1 WHERE stripe_customer_id=$2 RETURNING *;`,
        [new Date(Date.now() - 100000), this.customerId],
      );
    } catch (err) {
      console.error("Query Error:", err);
    }
  }

  async updateSubscriptionChangeTime() {
    try {
      await Pool.query<UserSubscription>(
        `UPDATE ${this.table} SET plan_changed_at=$1 WHERE customer_id=$2 RETURNING *;`,
        [new Date(Date.now() - 100000), this.customerId],
      );
    } catch (err) {
      console.error("Query Error:", err);
    }
  }

  async updateSubscriptionUsageTrack() {
    try {
      await Pool.query<UserSubscription>(
        `UPDATE ${this.table} SET usage_tracked_at=$1 WHERE customer_id=$2 RETURNING *;`,
        [new Date(Date.now() - 100000), this.customerId],
      );
    } catch (err) {
      console.error("Query Error:", err);
    }
  }

  async setSubscriptionActiveToFalse() {
    try {
      return await Pool.query<UserSubscription>(
        `UPDATE ${this.table} SET active=$1 WHERE customer_id=$2`,
        [false, this.customerId],
      );
    } catch (err) {
      console.error("Query Error:", err);
    }
  }

  async setMeteredBilling(state: boolean) {
    try {
      return await Pool.query<User>(
        "UPDATE users SET metered_billing=$1 WHERE stripe_customer_id=$2",
        [state, this.customerId],
      );
    } catch (err) {
      console.error("Query Error:", err);
    }
  }
}
