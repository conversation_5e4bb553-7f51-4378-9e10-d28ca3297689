import React from "react";
import SkeletonImage from "./components/Image";
import SkeletonText from "./components/Text";
import SkeletonButton from "./components/Button";

const SkeletonLoader: React.FC = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4">
      {Array.from({ length: 9 }).map((_, index) => (
        <div className="flex flex-col border rounded-lg p-3 justify-between w-[326px] h-[201px] md:w-[282px] lg:w-[292px] shadow-md animate-pulse">
          <div className="flex justify-between">
            <SkeletonImage width={"w-10"} height={"h-10"} />
            <SkeletonText width={"w-1/2"} height={"h-2"} />
          </div>
          <div className="flex flex-col gap-2">
            <SkeletonText width={"w-3/4"} height={"h-4"} />
            <SkeletonText width={"w-full"} height={"h-2"} />
            <SkeletonText width={"w-full"} height={"h-2"} />
            <SkeletonText width={"w-1/2"} height={"h-2"} />
          </div>
          <SkeletonButton className={"self-end"} />
        </div>
      ))}
    </div>
  );
};

export default SkeletonLoader;
