import React from "react";
import { Redirect } from "react-router";
import { useAppSelector } from "../../../redux/hooks";
import KadasterForm from "./KadasterForm";
import Loading from "../../Loading";
import { FormProvider } from "../../../pages/Api/usage-pages/components/FormContext";
const Kadaster = () => {
  const { loading, result } = useAppSelector((state) => state.transaction);

  if (loading) {
    return <Loading />;
  }

  return (
    <FormProvider>
      {Object.keys(result).length > 0 ? (
        <Redirect
          to={{
            pathname: "/transactie-result",
          }}
        />
      ) : (
        <KadasterForm />
      )}
    </FormProvider>
  );
};

export default Kadaster;
