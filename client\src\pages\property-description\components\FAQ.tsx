import { useState } from "react";
import Subtitle from "../../../components/Subtitle";
import Text from "../../../components/Text";
import { FiMinus, FiPlus } from "react-icons/fi";

const faqData = [
  {
    question:
      "Wat zijn de kosten voor het gebruik van de woningbeschrijving generator?",
    answer: "Het gebruik van de woningbeschrijving generator is gratis",
  },
  {
    question: "Hoe nauwkeurig zijn de gegenereerde beschrijvingen?",
    answer:
      "Onze AI gebruikt de door u verstrekte informatie en afbeeldingen om nauwkeurige, op maat gemaakte beschrijvingen te creëren. Controleer en pas deze indien nodig aan voor optimale nauwkeurigheid",
  },
  {
    question: "Kan ik de gegenereerde beschrijvingen bewerken?",
    answer:
      "Zeker! Gebruik onze beschrijvingen als uitgangspunt en pas ze aan naar uw specifieke wensen",
  },
];

const FAQ: React.FC = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  return (
    <div className="flex flex-col gap-4 justify-center items-center mt-16">
      <Subtitle className="text-lg font-bold">Veelgestelde vragen</Subtitle>
      <div className="grid md:grid-cols-2 grid-cols-1 gap-4 w-full p-4 shadow-[0px_0px_4px_0px_#00000026] rounded-lg justify-center items-center">
        {faqData.map((item, index) => (
          <div key={index} className="flex flex-col gap-2 w-full">
            <div
              className="flex items-start gap-2 cursor-pointer w-full"
              onClick={() => setOpenIndex(openIndex === index ? null : index)}
            >
              <span className="mt-1 flex-shrink-0">
                {openIndex === index ? (
                  <FiMinus size={15} className="text-purple-600" />
                ) : (
                  <FiPlus size={15} className="text-purple-600" />
                )}
              </span>
              <Subtitle className="text-base flex-grow">
                {item.question}
              </Subtitle>
            </div>
            <div
              className={`ml-7 overflow-hidden transition-all duration-300 ease-in-out ${
                openIndex === index ? "max-h-40" : "max-h-0"
              }`}
            >
              <Text className="text-gray-light">{item.answer}</Text>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default FAQ;
