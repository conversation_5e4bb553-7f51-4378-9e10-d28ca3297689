import { User } from "../../@types";
import pool from "../../db";
import AppError from "../../utils/appError";
import { createNewUser } from "../../controllers/authController";

export class UserService {
  /**
   * Creates a new user in the database
   */
  static async createUser(userData: {
    password: string;
    firstName?: string;
    lastName?: string;
    email: string;
    company?: string;
    kvk?: string;
    apiKey: string;
    apiKeyId: string;
    usagePlanId: string;
    stripeCustomerId: string;
    active?: boolean;
  }): Promise<User> {
    try {
      const user = await createNewUser(
        userData.firstName || "",
        userData.lastName || "",
        userData.email,
        userData.password,
        userData.company || "",
        userData.kvk || "",
        userData.apiKey,
        userData.apiKeyId,
        userData.stripeCustomerId,
        Math.floor((Date.now() + 24 * 60 * 60 * 1000) / 1000.0), // verificationExpiry
        userData.usagePlanId,
      );

      return user;
    } catch (error: any) {
      if (error.code === "23505") {
        // Unique violation
        if (error.constraint === "users_email_key") {
          throw new AppError("A user with this email already exists", 409);
        }
        if (error.constraint === "users_api_key_key") {
          throw new AppError("A user with this API key already exists", 409);
        }
      }
      throw error;
    }
  }

  /**
   * Creates a migrated user in the database
   */
  static async createMigratedUser(userData: {
    email: string;
    company?: string;
    kvk?: string;
    apiKey: string;
    apiKeyId: string;
    usagePlanId: string;
    stripeCustomerId: string;
    password: string;
  }): Promise<User> {
    try {
      const migratedUser = await pool.query<User>(
        `
        INSERT INTO users 
          (email, company, kvk, api_key, api_key_id, current_usage_plan, role, password, stripe_customer_id)
        VALUES ($1, $2, $3, $4, $5, $6, 'user', $7, $8)
        RETURNING *
      `,
        [
          userData.email,
          userData.company || "",
          userData.kvk || "",
          userData.apiKey,
          userData.apiKeyId,
          userData.usagePlanId,
          userData.password,
          userData.stripeCustomerId,
        ],
      );

      return migratedUser.rows[0];
    } catch (error: any) {
      throw new AppError(
        `Failed to create migrated user: ${error.message}`,
        500,
      );
    }
  }

  /**
   * Gets a user by ID
   */
  static async getUserById(userId: string): Promise<User | null> {
    try {
      const user = await pool.query<User>(
        "SELECT * FROM users WHERE user_id = $1",
        [userId],
      );

      if (!user.rows[0]) {
        throw new AppError("User not found", 404);
      }

      return user.rows[0] || null;
    } catch (error: any) {
      throw new AppError(`Failed to query user: ${error.message}`, 500);
    }
  }

  /**
   * Gets a user by email or API key
   */
  static async getUserByEmailOrApiKey(
    email: string,
    apiKey: string,
  ): Promise<User | null> {
    try {
      const result = await pool.query<User>(
        "SELECT * FROM users WHERE email = $1 OR api_key = $2",
        [email, apiKey],
      );
      return result.rows[0] || null;
    } catch (error: any) {
      throw new AppError(`Failed to query user: ${error.message}`, 500);
    }
  }

  /**
   * Gets users by usage plan
   */
  static async getUsersByUsagePlan(usagePlanId: string): Promise<User[]> {
    try {
      const result = await pool.query<User>(
        "SELECT * FROM users WHERE current_usage_plan = $1",
        [usagePlanId],
      );
      return result.rows;
    } catch (error: any) {
      throw new AppError(
        `Failed to query users by usage plan: ${error.message}`,
        500,
      );
    }
  }

  /**
   * Deletes a user and all associated data from the database
   */
  static async deleteUser(userId: string): Promise<void> {
    try {
      // Begin transaction
      await pool.query("BEGIN");

      try {
        // Delete from dependent tables first
        await pool.query(
          "DELETE FROM user_onboarding_state WHERE user_id = $1",
          [userId],
        );

        await pool.query("DELETE FROM user_notifications WHERE user_id = $1", [
          userId,
        ]);

        // Finally delete the user
        const result = await pool.query(
          "DELETE FROM users WHERE user_id = $1 RETURNING *",
          [userId],
        );

        if (!result.rows[0]) {
          throw new AppError(`User with ID ${userId} not found`, 404);
        }

        // Commit transaction if all operations succeed
        await pool.query("COMMIT");
      } catch (error) {
        // Rollback transaction if any operation fails
        await pool.query("ROLLBACK");
        throw error;
      }
    } catch (error: any) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Failed to delete user: ${error.message}`, 500);
    }
  }

  /**
   * Deletes a user by email and all associated data
   */
  static async deleteUserByEmail(email: string): Promise<void> {
    try {
      // Get user ID first
      const user = await pool.query<User>(
        "SELECT user_id FROM users WHERE email = $1",
        [email],
      );

      if (!user.rows[0]) {
        throw new AppError(`User with email ${email} not found`, 404);
      }

      // Delete user and associated data using the ID
      await this.deleteUser(user.rows[0].user_id);
    } catch (error: any) {
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(`Failed to delete user: ${error.message}`, 500);
    }
  }

  /**
   * Updates a user's usage plan
   */
  static async updateUserUsagePlan(
    userId: string,
    usagePlanId: string,
  ): Promise<User> {
    try {
      const result = await pool.query<User>(
        "UPDATE users SET current_usage_plan = $1 WHERE user_id = $2 RETURNING *",
        [usagePlanId, userId],
      );

      if (!result.rows[0]) {
        throw new AppError(`User with ID ${userId} not found`, 404);
      }

      return result.rows[0];
    } catch (error: any) {
      if (error.code === "23503") {
        // Foreign key violation
        throw new AppError("Referenced usage plan does not exist", 400);
      }
      if (error instanceof AppError) {
        throw error;
      }
      throw new AppError(
        `Failed to update user usage plan: ${error.message}`,
        500,
      );
    }
  }

  /**
   * Gets AWS resources for a user
   */
  static async getUserAwsResources(
    userId: string,
  ): Promise<{ apiKeyId: string; usagePlanId: string }> {
    try {
      const user = await pool.query<User>(
        "SELECT api_key_id, current_usage_plan FROM users WHERE user_id = $1",
        [userId],
      );

      if (!user.rows[0]) {
        throw new AppError("User not found", 404);
      }

      return {
        apiKeyId: user.rows[0].api_key_id,
        usagePlanId: user.rows[0].current_usage_plan,
      };
    } catch (error: any) {
      throw new AppError(
        `Failed to query user AWS resources: ${error.message}`,
        500,
      );
    }
  }
}
