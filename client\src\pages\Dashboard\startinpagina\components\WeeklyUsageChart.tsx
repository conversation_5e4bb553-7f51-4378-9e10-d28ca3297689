"use client";

import type React from "react";

import { useEffect, useRef, useState } from "react";
import { ArrowUpIcon, ArrowDownIcon } from "lucide-react";
import { useAppDispatch, useAppSelector } from "../../../../redux/hooks";
import { cn } from "../../../../lib/utils";
import { getWeeklyUsageData } from "../../../../redux/actions/apiUsage";
import CardContainer from "../../components/CardContainer";

interface WeeklyUsageData {
  weeklyUsage: {
    date: string;
    count: number;
  }[];
  currentWeekTotal: number;
  previousWeekTotal: number;
  percentageChange: number;
}

interface WeeklyUsageChartProps {
  data: WeeklyUsageData;
}

export default function WeeklyUsageChart() {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [hoveredBar, setHoveredBar] = useState<number | null>(null);
  const [tooltipInfo, setTooltipInfo] = useState<{
    x: number;
    y: number;
    count: number;
    date: string;
  } | null>(null);
  const dispatch = useAppDispatch();
  const {
    weeklyUsage,
    percentageChange = 0,
    previousWeekTotal,
    currentWeekTotal,
    loading,
    error,
  } = useAppSelector((state) => state.apiUsage.weeklyUsage);
  // Filter to show only the last 4 weeks of data

  useEffect(() => {
    dispatch(getWeeklyUsageData());
  }, [dispatch]);
  const lastFourWeeks = weeklyUsage;

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Set canvas dimensions
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    ctx.scale(dpr, dpr);

    // Clear canvas
    ctx.clearRect(0, 0, rect.width, rect.height);

    // Chart dimensions
    const padding = { top: 20, right: 20, bottom: 40, left: 40 };
    const chartWidth = rect.width - padding.left - padding.right;
    const chartHeight = rect.height - padding.top - padding.bottom;

    // Find max value for scaling
    const maxValue = Math.max(
      ...(lastFourWeeks?.map((week) => week.count) || []),
      5,
    ); // Minimum of 5 for scale

    // Draw y-axis
    ctx.beginPath();
    ctx.moveTo(padding.left, padding.top);
    ctx.lineTo(padding.left, padding.top + chartHeight);
    ctx.strokeStyle = "#333333";
    ctx.stroke();

    // Draw y-axis labels
    ctx.textAlign = "right";
    ctx.textBaseline = "middle";
    ctx.fillStyle = "#333333";
    ctx.font = "12px Inter, sans-serif";

    const yAxisSteps = 5;
    for (let i = 0; i <= yAxisSteps; i++) {
      const y = padding.top + chartHeight - (i / yAxisSteps) * chartHeight;
      const value = Math.round((i / yAxisSteps) * maxValue);

      ctx.fillText(value.toString(), padding.left - 10, y);

      // Draw horizontal grid line
      ctx.beginPath();
      ctx.moveTo(padding.left, y);
      ctx.lineTo(padding.left + chartWidth, y);
      ctx.strokeStyle = "#e2e8f0";
      ctx.stroke();
    }

    // Draw bars
    const barWidth = (chartWidth / (lastFourWeeks?.length ?? 1)) * 0.6;
    const barSpacing = chartWidth / (lastFourWeeks?.length ?? 1);

    (lastFourWeeks ?? []).forEach((week, index) => {
      const x =
        padding.left + index * barSpacing + barSpacing / 2 - barWidth / 2;
      const barHeight = (week.count / maxValue) * chartHeight;
      const y = padding.top + chartHeight - barHeight;

      // Draw bar
      ctx.beginPath();
      ctx.rect(x, y, barWidth, barHeight);

      // Highlight current week
      if (index === (lastFourWeeks?.length ?? 0) - 1) {
        ctx.fillStyle = "#27AE60"; // Green for current week
      } else if (index === hoveredBar) {
        ctx.fillStyle = "#27AE60"; // Light blue for hovered bar
      } else {
        ctx.fillStyle = "#E4E9E6"; // Gray for other weeks
      }

      ctx.fill();

      // Draw x-axis label (date)
      const date = new Date(week.date);
      const formattedDate = `${date.toLocaleDateString("nl-NL", {
        month: "short",
        day: "numeric",
      })}`;

      ctx.textAlign = "center";
      ctx.textBaseline = "top";
      ctx.fillStyle = "#333333";
      ctx.fillText(
        formattedDate,
        x + barWidth / 2,
        padding.top + chartHeight + 10,
      );

      // Store bar position for hover detection
      if (index === hoveredBar) {
        setTooltipInfo({
          x: x + barWidth / 2,
          y: y,
          count: week.count,
          date: formattedDate,
        });
      }
    });
  }, [lastFourWeeks, hoveredBar]);

  // Handle mouse movement for hover effects
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Chart dimensions
    const padding = { top: 20, right: 20, bottom: 40, left: 40 };
    const chartWidth = rect.width - padding.left - padding.right;
    const barSpacing = chartWidth / (lastFourWeeks?.length ?? 1);
    const barWidth = (chartWidth / (lastFourWeeks?.length ?? 1)) * 0.6;

    // Check if mouse is over any bar
    for (let i = 0; i < (lastFourWeeks ?? []).length; i++) {
      const barX =
        padding.left + i * barSpacing + barSpacing / 2 - barWidth / 2;
      if (x >= barX && x <= barX + barWidth) {
        setHoveredBar(i);
        return;
      }
    }

    setHoveredBar(null);
    setTooltipInfo(null);
  };

  const handleMouseLeave = () => {
    setHoveredBar(null);
    setTooltipInfo(null);
  };

  // Format percentage change
  const formatPercentageChange = (value: number) => {
    if (value === 0) return "0%";
    if (value > 1000) return `${value > 0 ? "+" : "-"}100+%`;
    return `${value > 0 ? "+" : ""}${Math.round(value)}%`;
  };

  // Determine if change is positive, negative, or neutral
  const getChangeColor = (value: number) => {
    if (value > 0) return "#E4E9E6";
    if (value < 0) return "text-red-500";
    return "text-gray-500";
  };

  // Get change icon
  const getChangeIcon = (value: number) => {
    if (value > 0) return <ArrowUpIcon className="h-4 w-4" />;
    if (value < 0) return <ArrowDownIcon className="h-4 w-4" />;
    return null;
  };

  return (
    <CardContainer title={"API Gebruiksinzicht"}>
      <div className="space-y-4">
        <div className="bg-green-50 p-4 rounded-lg">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="text-3xl font-bold">{currentWeekTotal}</h3>
              <p className="text-sm text-gray-500">API-aanroepen deze week</p>
            </div>
            <div
              className={cn(
                "flex items-center gap-1",
                getChangeColor(percentageChange),
              )}
            >
              {getChangeIcon(percentageChange)}
              <span>{formatPercentageChange(percentageChange)}</span>
              <span className="text-sm text-gray-500">vs vorige week</span>
            </div>
          </div>
        </div>

        <div className="relative h-80 w-full">
          <canvas
            ref={canvasRef}
            className="w-full h-full"
            onMouseMove={handleMouseMove}
            onMouseLeave={handleMouseLeave}
          />
          {tooltipInfo && (
            <div
              className="absolute bg-green-50 border border-green-100 rounded-md shadow-md p-2 text-sm pointer-events-none transform -translate-x-1/2 -translate-y-full"
              style={{
                left: `${tooltipInfo.x}px`,
                top: `${tooltipInfo.y}px`,
                marginTop: "-8px",
              }}
            >
              <div className="flex flex-col">
                <p className="text-lg font-bold">
                  {tooltipInfo.count} API-aanroepen
                </p>
                <div className="flex items-center gap-1 text-gray-600">
                  {hoveredBar !== null && hoveredBar > 0 && lastFourWeeks ? (
                    lastFourWeeks[hoveredBar].count >
                    lastFourWeeks[hoveredBar - 1].count ? (
                      <ArrowUpIcon className="h-4 w-4 text-green-600" />
                    ) : (
                      <ArrowDownIcon className="h-4 w-4 text-red-500" />
                    )
                  ) : null}
                  <span className="text-sm">
                    {hoveredBar !== null && hoveredBar > 0 && lastFourWeeks
                      ? formatPercentageChange(
                          ((lastFourWeeks[hoveredBar].count -
                            lastFourWeeks[hoveredBar - 1].count) /
                            lastFourWeeks[hoveredBar - 1].count) *
                            100,
                        )
                      : "0%"}{" "}
                    vs vorige week
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </CardContainer>
  );
}
