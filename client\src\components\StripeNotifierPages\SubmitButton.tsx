import { StripeError } from '@stripe/stripe-js';
import React, { FC, ReactNode } from 'react';

interface SubmitButtonProps {
  processing: boolean;
  error: Error | null | StripeError;
  children: ReactNode | string;
  disabled: boolean;
}
export const SubmitButton: FC<SubmitButtonProps> = ({
  processing,
  error,
  children,
  disabled,
}) => (
  <button
    className={`checkout-submitBtn ${error ? 'checkout-submitBtn--error' : ''}`}
    type="submit"
    disabled={processing || disabled || error instanceof Error}
  >
    {processing ? 'Wordt verwerkt...' : children}
  </button>
);
