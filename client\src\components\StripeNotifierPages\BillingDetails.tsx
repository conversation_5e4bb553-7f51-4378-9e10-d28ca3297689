import { ChangeEvent } from 'react';
import { Field } from './Field';

type BillingDetailsProps = {
  billingDetails: { email: string; name: string };
  setBillingDetails: (details: { email: string; name: string }) => void;
};

export function BillingDetails({
  billingDetails,
  setBillingDetails,
}: BillingDetailsProps) {
  return (
    <>
      <Field
        id="email"
        type="email"
        required
        autoComplete="email"
        value={billingDetails.email}
        onChange={(e: ChangeEvent<HTMLInputElement>) => {
				  setBillingDetails({
				    ...billingDetails,
				    email: e.target.value,
				  });
        }}
        placeholder="Email"
      />
      <Field
        id="name"
        type="text"
        placeholder="Persoon of Bedrijfsnaam"
        required
        autoComplete="name"
        value={billingDetails.name}
        onChange={(e: ChangeEvent<HTMLInputElement>) => {
				  setBillingDetails({
				    ...billingDetails,
				    name: e.target.value,
				  });
        }}
      />
    </>
  );
}
