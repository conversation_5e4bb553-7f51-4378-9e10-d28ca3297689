doctype html
html
    head
        meta(charset="UTF-8")
        meta(http-equiv="X-UA-Compatible" content="IE=edge")
        meta(name="viewport" content="width=device-width, initial-scale=1.0")
        title APIs van Altum AI

        link(rel="preconnect" href="https://fonts.googleapis.com")
        link(rel="preconnect" href="https://fonts.gstatic.com" crossorigin)
        link(href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet")

        include style

    body
        .container.guide-container
            img.guide-logo(src='img/altum.png' alt="logo")
            .guide-links-div
                p For usage of the API's of Altum AI, go to 
                    a.guide-link(href='https://mopsus.altum.ai' target="_blank" rel="noopener noreferrer") mopsus.altum.ai
                    |  to 
                    a.guide-link(href='https://mopsus.altum.ai/#/signup' target="_blank" rel="noopener noreferrer") sign up 
                    |  for an account.

                .guide-links-flex
                    .guide-links-flex-item
                        svg.guide-link-icon(xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor")
                            path(stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253")
                        p For documentation, go to 
                            a.guide-link(href="https://docs.altum.ai" target="_blank" rel="noopener noreferrer") docs.altum.ai
                    .guide-links-flex-item
                        svg.guide-link-icon(xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor")
                            path(stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01")
                        p For the Sandbox, go to 
                            a.guide-link(href="https://sandbox.altum.ai" target="_blank" rel="noopener noreferrer") sandbox.altum.ai
                    .guide-links-flex-item
                        svg.guide-link-icon(xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor")
                            path(stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z")
                        p For uptime of API's, go to 
                            a.guide-link(href="https://status.altum.ai" target="_blank" rel="noopener noreferrer") status.altum.ai