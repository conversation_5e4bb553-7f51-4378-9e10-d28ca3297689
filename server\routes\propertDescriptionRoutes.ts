import { NextFunction, Request, Response, Router } from "express";
import multer from "multer";
import { validateGenerateDescription } from "../middlewares/validationMiddleware";
import {
  deleteChatHistoryController,
  generateDescription,
  getChatHistoryController,
  handleChunkedUpload,
  updateChatHistoryRateAndComment,
} from "../controllers/propertyDescriptionController";
import { protect } from "../controllers/authController";

interface MulterRequest extends Request {
  files: Express.Multer.File[];
}

const router = Router();
const upload = multer({ storage: multer.memoryStorage() });
router.post(
  "/upload-chunk",
  protect,
  upload.single("file"),
  handleChunkedUpload,
);
router.post(
  "/generate-description",
  protect,
  upload.none(),
  generateDescription,
);
// router.post(
//   "/generate-description",
//   upload.array("images", 40),
//   validateGenerateDescription,
//   (req: Request, res: Response) =>
//     generateDescription(req as MulterRequest, res),
// );

router.delete("/chat-history/:id", protect, deleteChatHistoryController);
router.get("/chat-history", protect, getChatHistoryController);
router.post("/rate-comment", protect, updateChatHistoryRateAndComment);
export const propertyRoutes = router;
