import { AppDispatch } from "../store";
import { createApiThunkPdfApi } from "../../helpers/createApiThunk";

// Dispatch function to POST EPC API
export const postEPC = createApiThunkPdfApi(
  "epc/postEPC",
  "api",
  "EPC api used",
  "epc",
);

export const clearEPCResults = () => async (dispatch: AppDispatch) => {
  dispatch({ type: "epc/clearResults" });
};

export const modifyEPCQueries = () => async (dispatch: AppDispatch) => {
  dispatch({ type: "epc/modifyQueries" });
};

export const postEPCOneTimeUse = createApiThunkPdfApi(
  "epc/postEPCOneTimeUse",
  "demo",
  "EPC demo used",
  "epc",
);
