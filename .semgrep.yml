# Semgrep configuration for OWASP Top 10 security scanning
# This configuration focuses on detecting common security vulnerabilities

rules:
  # SQL Injection Detection
  - id: sql-injection-risk
    patterns:
      - pattern: |
          $QUERY = "..." + $INPUT + "..."
      - pattern: |
          $QUERY = `...${$INPUT}...`
      - pattern: |
          $DB.query($QUERY)
    message: "Potential SQL injection vulnerability detected"
    languages: [javascript, typescript]
    severity: ERROR
    metadata:
      owasp: "A03:2021 - Injection"
      category: security

  # XSS Prevention
  - id: dangerous-innerhtml
    patterns:
      - pattern: |
          $ELEMENT.innerHTML = $INPUT
      - pattern: |
          $ELEMENT.outerHTML = $INPUT
    message: "Potential XSS vulnerability: avoid setting innerHTML with user input"
    languages: [javascript, typescript]
    severity: ERROR
    metadata:
      owasp: "A03:2021 - Injection"
      category: security

  # Insecure Direct Object References
  - id: path-traversal
    patterns:
      - pattern: |
          fs.readFile($PATH + $INPUT, ...)
      - pattern: |
          fs.readFileSync($PATH + $INPUT, ...)
      - pattern: |
          path.join($PATH, $INPUT)
    message: "Potential path traversal vulnerability"
    languages: [javascript, typescript]
    severity: ERROR
    metadata:
      owasp: "A01:2021 - Broken Access Control"
      category: security

  # Weak Cryptography
  - id: weak-crypto
    patterns:
      - pattern: |
          crypto.createHash('md5')
      - pattern: |
          crypto.createHash('sha1')
      - pattern: |
          crypto.createCipher(...)
    message: "Weak cryptographic algorithm detected"
    languages: [javascript, typescript]
    severity: WARNING
    metadata:
      owasp: "A02:2021 - Cryptographic Failures"
      category: security

  # Hardcoded Secrets
  - id: hardcoded-secrets
    patterns:
      - pattern: |
          const $VAR = "sk_live_..."
      - pattern: |
          const $VAR = "pk_live_..."
      - pattern: |
          const password = "..."
      - pattern: |
          const secret = "..."
      - pattern: |
          const apiKey = "..."
    message: "Potential hardcoded secret detected"
    languages: [javascript, typescript]
    severity: ERROR
    metadata:
      owasp: "A07:2021 - Identification and Authentication Failures"
      category: security

  # Insecure HTTP Usage
  - id: insecure-http
    patterns:
      - pattern: |
          axios.get("http://...")
      - pattern: |
          fetch("http://...")
      - pattern: |
          request("http://...")
    message: "Insecure HTTP connection detected, use HTTPS"
    languages: [javascript, typescript]
    severity: WARNING
    metadata:
      owasp: "A02:2021 - Cryptographic Failures"
      category: security

  # Command Injection
  - id: command-injection
    patterns:
      - pattern: |
          exec($INPUT)
      - pattern: |
          spawn($INPUT, ...)
      - pattern: |
          execSync($INPUT)
    message: "Potential command injection vulnerability"
    languages: [javascript, typescript]
    severity: ERROR
    metadata:
      owasp: "A03:2021 - Injection"
      category: security

  # Insecure Randomness
  - id: insecure-random
    patterns:
      - pattern: |
          Math.random()
    message: "Math.random() is not cryptographically secure, use crypto.randomBytes()"
    languages: [javascript, typescript]
    severity: WARNING
    metadata:
      owasp: "A02:2021 - Cryptographic Failures"
      category: security

  # Missing Authentication
  - id: missing-auth-check
    patterns:
      - pattern: |
          app.$METHOD($PATH, ($REQ, $RES) => { ... })
      - metavariable-pattern:
          metavariable: $METHOD
          patterns:
            - pattern-either:
                - pattern: post
                - pattern: put
                - pattern: delete
                - pattern: patch
    message: "Endpoint may be missing authentication middleware"
    languages: [javascript, typescript]
    severity: INFO
    metadata:
      owasp: "A07:2021 - Identification and Authentication Failures"
      category: security

  # Prototype Pollution
  - id: prototype-pollution
    patterns:
      - pattern: |
          $OBJ.__proto__ = $VALUE
      - pattern: |
          $OBJ['__proto__'] = $VALUE
      - pattern: |
          $OBJ.constructor.prototype = $VALUE
    message: "Potential prototype pollution vulnerability"
    languages: [javascript, typescript]
    severity: ERROR
    metadata:
      owasp: "A06:2021 - Vulnerable and Outdated Components"
      category: security

  # Regex DoS
  - id: regex-dos
    patterns:
      - pattern: |
          new RegExp($PATTERN)
      - pattern: |
          /$PATTERN/
    message: "Review regex pattern for potential ReDoS vulnerability"
    languages: [javascript, typescript]
    severity: INFO
    metadata:
      owasp: "A06:2021 - Vulnerable and Outdated Components"
      category: security

  # Unsafe Eval Usage
  - id: unsafe-eval
    patterns:
      - pattern: |
          eval($INPUT)
      - pattern: |
          Function($INPUT)
      - pattern: |
          setTimeout($INPUT, ...)
      - pattern: |
          setInterval($INPUT, ...)
    message: "Unsafe eval-like function usage detected"
    languages: [javascript, typescript]
    severity: ERROR
    metadata:
      owasp: "A03:2021 - Injection"
      category: security
