import { AppDispatch } from "../store";
import { createApiThunk } from "../../helpers/createApiThunk";

export const postAvmDetails = createApiThunk(
  "avm/postAvmDetails",
  "api",
  "Avm Api Used",
  "avm",
);

export const clearAvmResults = () => async (dispatch: AppDispatch) => {
  dispatch({ type: "avm/clearResults" });
};

export const modifyAvmQueries = () => async (dispatch: AppDispatch) => {
  dispatch({ type: "avm/modifyQueries" });
};

export const avmOneTimeUse = createApiThunk(
  "avm/avmOneTimeUse",
  "demo",
  "avm demo used",
  "avm",
);
