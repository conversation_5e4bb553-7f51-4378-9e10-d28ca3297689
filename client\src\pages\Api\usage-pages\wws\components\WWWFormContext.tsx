import React, { createContext, useContext, useState, ReactNode } from "react";
import {
  FormOptionsSustainability,
  FormOptionsWWS,
} from "../../../../../@types";

interface WWWFormContextType {
  formValues: FormOptionsWWS;
  setFormValues: React.Dispatch<React.SetStateAction<FormOptionsWWS>>;
  errors: Record<string, string>;
  setErrors: React.Dispatch<React.SetStateAction<Record<string, string>>>;
}

const WWWFormContext = createContext<WWWFormContextType | undefined>(undefined);

export const useWWWFormContext = () => {
  const context = useContext(WWWFormContext);
  if (!context) {
    throw new Error("useWWWFormContext must be used within a WWWFormProvider");
  }
  return context;
};

interface WWWFormProviderProps {
  children: ReactNode;
}

export const WWWFormProvider: React.FC<WWWFormProviderProps> = ({
  children,
}) => {
  const [formValues, setFormValues] = useState<FormOptionsWWS>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  return (
    <WWWFormContext.Provider
      value={{ formValues, setFormValues, errors, setErrors }}
    >
      {children}
    </WWWFormContext.Provider>
  );
};
