import { useFormContext } from "../../components/FormContext";
import { propertyStatus } from "../data";
import ReturnInputField from "../../../../../components/ReturnInputField";

const PropertyStatus = () => {
  const { formValues, setFormValues } = useFormContext();

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold mb-4">
          Vertel ons iets meer over de bewoning, het verbruik en huidige
          maatregelen
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {propertyStatus.map((field, index) => (
            <ReturnInputField
              key={index}
              label={field.label}
              type={field.type}
              name={field.name}
              placeholder={field.placeholder}
              onChange={(e) => {
                "target" in e &&
                  setFormValues({
                    ...formValues,
                    [e.target.name]: e.target.value,
                  });
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default PropertyStatus;
