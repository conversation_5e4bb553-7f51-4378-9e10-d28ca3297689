import { GraphQLClient } from "graphql-request";
import AppError from "./appError";

export const fetchAnalytics = async (
  apiKey: string,
  apiKey2: string | null,
  startDate: string,
  endDate: string,
) => {
  try {
    const keyLength = apiKey.length;
    const last6 = apiKey.slice(keyLength - 6);
    const hidden = "*".repeat(keyLength - 6);
    const hiddenApiKey = `${hidden}${last6}`;
    let hiddenApiKey2 = hiddenApiKey;
    if (apiKey2 && apiKey2.length) {
      hiddenApiKey2 = `${hidden}${apiKey2.slice(apiKey2.length - 6)}`;
    }

    const query = `
    {
      actor {
        account(id: ${process.env.GRAPHQL_ID}) {
          nrql(
            query: "SELECT count(*) FROM Log WHERE apiKey='${hiddenApiKey}' OR apiKey='${hiddenApiKey2}' SINCE '${startDate} 00:00:00' UNTIL '${endDate} 00:00:00' FACET api_name  TIMESERIES 1 day LIMIT 180") {
            results
          }
        }
      }
    }
  `;

    // query = query.replace("$id", process.env.GRAPHQL_ID);
    // query = query.replace("$1", apiKey);
    // query = query.replace("$2", api_name);

    const client = new GraphQLClient("https://api.eu.newrelic.com/graphql", {
      headers: { "api-key": `${process.env.GRAPHQL_APIKEY}` },
    });

    return await client
      .request(query)
      .then((data) => data.actor.account.nrql.results)
      .catch((error) => {
        console.log(error);
        new AppError("unable to fetch analytics from new relic", 400);
      });
  } catch (err) {
    console.log(err);
    return new AppError("unable to fetch analytics from new relic", 400);
  }
};

export const fetchAllAnalytics = async (startDate: string, endDate: string) => {
  try {
    const query = `
    {
      actor {
        account(id: ${process.env.GRAPHQL_ID}) {
          nrql(
            query: "SELECT count(*) FROM Log SINCE '${startDate} 00:00:00' UNTIL '${endDate} 00:00:00' TIMESERIES 1 day LIMIT 180") {
            results
          }
        }
      }
    }
    `;

    const client = new GraphQLClient("https://api.eu.newrelic.com/graphql", {
      headers: { "api-key": `${process.env.GRAPHQL_APIKEY}` },
    });

    const response = await client
      .request(query)
      .then((data) => data.actor.account.nrql.results)
      .catch((error) => {
        console.log(error);
        throw new AppError("unable to fetch analytics from new relic", 400);
      });

    return response;
  } catch (err) {
    console.log(err);
    if (err instanceof AppError) {
      throw err;
    }
    throw new AppError("unable to fetch analytics from new relic", 400);
  }
};
