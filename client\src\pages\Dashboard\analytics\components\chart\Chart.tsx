import { FC } from "react";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, ResponsiveContainer } from "recharts";
import { filterAnylyticsData } from "../../../../../helpers/filterAnalyticData";
import apiNameConverter from "../../../../../helpers/apiNameConverter";
import apiColorPicker from "../../../../../helpers/apiColorPicker";
import CustomLegend from "./CustomLegend";
import Text from "../../../../../components/Text";
import Button from "../../../../../components/Button";
import { useHistory } from "react-router-dom";
import { TbExternalLink } from "react-icons/tb";

interface ChartProps {
  data: any[];
}
const NoDataInfo = () => {
  const history = useHistory();

  return (
    <div className="flex flex-col items-center justify-center h-full min-h-[300px] gap-2">
      <svg
        width="65"
        height="64"
        viewBox="0 0 65 64"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M11.1667 16.001C11.1667 20.4196 20.7187 24.001 32.5001 24.001C44.2814 24.001 53.8334 20.4196 53.8334 16.001C53.8334 11.5823 44.2814 8.00098 32.5001 8.00098C20.7187 8.00098 11.1667 11.5823 11.1667 16.001Z"
          stroke="#BDBDBD"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M11.1667 16.001V32.001C11.1667 35.8143 18.2867 39.0063 27.8147 39.8063M53.8334 28.001V16.001"
          stroke="#BDBDBD"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
        <path
          d="M11.1667 32.001V48.001C11.1667 52.1236 19.4867 55.521 30.1747 55.953M47.9667 55.513L42.1747 58.5476C42.0029 58.6371 41.8095 58.677 41.6162 58.663C41.423 58.6489 41.2374 58.5814 41.0803 58.468C40.9232 58.3546 40.8007 58.1997 40.7265 58.0207C40.6523 57.8417 40.6293 57.6456 40.6601 57.4543L41.7667 51.025L37.0814 46.473C36.9416 46.3378 36.8425 46.166 36.7957 45.9772C36.7488 45.7884 36.7559 45.5903 36.8162 45.4054C36.8766 45.2205 36.9877 45.0562 37.1369 44.9314C37.2861 44.8067 37.4674 44.7263 37.6601 44.6996L44.1348 43.761L47.0307 37.913C47.1174 37.7388 47.2509 37.5922 47.4162 37.4898C47.5816 37.3874 47.7722 37.3331 47.9667 37.3331C48.1613 37.3331 48.3519 37.3874 48.5173 37.4898C48.6826 37.5922 48.8161 37.7388 48.9027 37.913L51.7987 43.761L58.2734 44.6996C58.4655 44.7273 58.6461 44.8081 58.7946 44.933C58.9432 45.0579 59.0538 45.2219 59.114 45.4064C59.1742 45.5909 59.1816 45.7886 59.1353 45.9771C59.089 46.1656 58.9909 46.3373 58.8521 46.473L54.1667 51.025L55.2707 57.4516C55.3039 57.6433 55.2826 57.8404 55.2094 58.0205C55.1361 58.2007 55.0138 58.3567 54.8563 58.4709C54.6989 58.585 54.5125 58.6527 54.3185 58.6663C54.1245 58.6799 53.9306 58.6388 53.7587 58.5476L47.9667 55.513Z"
          stroke="#BDBDBD"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        />
      </svg>

      <Text className="text-center text-gray-200 w-[300px]">
        Nog geen API-aanroepen geregistreerd. Begin met het maken van aanroepen
        om uw gebruiksgegevens hier te zien.
      </Text>
      <div className="flex flex-col lg:flex-row gap-2 mt-6 w-full max-w-[600px]">
        <Button
          className="bg-primary w-full mt-0"
          onClick={() => history.push("/dashboard/api")}
          size="sm"
        >
          Ga naar API's
        </Button>
        <div className="shadow-md bg-white p-2 rounded-md flex items-center w-full justify-center">
          <span>
            <TbExternalLink className="mr-2" />
          </span>
          <a href="https://docs.api.nl" target="_blank" rel="noreferrer">
            Ga naar documentatie
          </a>
        </div>
      </div>
    </div>
  );
};
const Chart: FC<ChartProps> = ({ data }) => {
  const called = filterAnylyticsData(data);
  const newData = data.map((data) => {
    return {
      calls: data.calls,
      day: data.day,
      ...data.apiStats,
    };
  });

  return Object.entries(called).every(([key, value]) => value === 0) ? (
    <NoDataInfo />
  ) : (
    <>
      <ResponsiveContainer width="100%" height={400}>
        <BarChart data={newData} width={400} height={300}>
          {Object.entries(called).map(([key, value], index) => (
            <Bar
              key={key}
              dataKey={key}
              fill={apiColorPicker(key)}
              stackId="a"
              name={apiNameConverter(key)}
            />
          ))}
          <XAxis dataKey="day" />
          <YAxis allowDecimals={false} />
        </BarChart>
      </ResponsiveContainer>
      <CustomLegend legendKey={Object.keys(called)} />
    </>
  );
};

export default Chart;
