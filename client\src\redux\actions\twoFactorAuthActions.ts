import { createAsyncThunk } from "@reduxjs/toolkit";
import axios from "axios";
import { toast } from "react-toastify";
import { loadUser } from "./authActions";

export interface TwoFactorAuthResponse {
  token: string;
  user: any;
}

export const verify2FA = createAsyncThunk(
  "auth/verify2FA",
  async ({ userId, code }: { userId: string; code: string }, { dispatch }) => {
    try {
      const config = { headers: { "Content-Type": "application/json" } };
      const response = await axios.post<TwoFactorAuthResponse>(
        "/api/v1/2fa/verify",
        { userId, code },
        config,
      );

      // Load user after successful 2FA
      await dispatch(loadUser({ shouldLoadExtras: false }));

      toast.success("2FA verificatie succesvol");
      return response.data;
    } catch (error: any) {
      toast.error(error.response?.data?.message || "2FA verificatie mislukt");
      throw new Error(
        error.response?.data?.message || "2FA verificatie mislukt",
      );
    }
  },
);

export const request2FACode = createAsyncThunk(
  "auth/request2FACode",
  async (userId: string) => {
    try {
      const config = { headers: { "Content-Type": "application/json" } };
      const response = await axios.post(
        "/api/v1/2fa/request-code",
        { userId },
        config,
      );

      toast.success("Nieuwe code verzonden");
      return response.data;
    } catch (error: any) {
      toast.error(
        error.response?.data?.message || "Kon geen nieuwe code verzenden",
      );
      throw new Error(
        error.response?.data?.message || "Kon geen nieuwe code verzenden",
      );
    }
  },
);

// Add to your auth slice
export const twoFactorAuthReducer = {
  [verify2FA.pending.type]: (state: any) => {
    state.loading = true;
    state.error = null;
  },
  [verify2FA.fulfilled.type]: (state: any, action: any) => {
    state.loading = false;
    state.isAuthenticated = true;
    state.user = action.payload.user;
  },
  [verify2FA.rejected.type]: (state: any, action: any) => {
    state.loading = false;
    state.error = action.error.message;
  },
  [request2FACode.pending.type]: (state: any) => {
    state.loading = true;
    state.error = null;
  },
  [request2FACode.fulfilled.type]: (state: any) => {
    state.loading = false;
  },
  [request2FACode.rejected.type]: (state: any, action: any) => {
    state.loading = false;
    state.error = action.error.message;
  },
};
