import axios from "axios";

const API_BASE_URL = "https://api.usercheck.com";
const BEARER_TOKEN = process.env.USERCHECK_API_TOKEN;

export async function isDisposableDomain(domain: string): Promise<boolean> {
  try {
    const response = await axios.get(`${API_BASE_URL}/domain/${domain}`, {
      headers: { Authorization: `Bearer ${BEARER_TOKEN}` },
    });
    const data = response.data;
    // Block registration if any of these flags are true:
    return (
      data.disposable === true ||
      data.blocklisted === true ||
      data.relay_domain === true
    );
  } catch (error) {
    console.error("Error checking disposable domain:", error);
    // Fail-safe: block registration on error.
    return true;
  }
}
