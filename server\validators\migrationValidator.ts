import { RequestHand<PERSON> } from "express";
import { body, validationResult } from "express-validator";
import AppError from "../utils/appError";

export const validateMigrationRequest: RequestHandler[] = [
  body("externalApiKey").notEmpty().withMessage("External API key is required"),
  body("email").isEmail().withMessage("Valid email is required"),
  body("company").optional(),
  body("kvk").optional(),

  // Validation result handler
  (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new AppError(errors.array()[0].msg, 400));
    }
    next();
  },
];
