#!/bin/bash

# DAST (Dynamic Application Security Testing) Script for Mopsus Data Platform
# This script runs OWASP ZAP scans to detect runtime security vulnerabilities

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REPORT_DIR="security-reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
ZAP_REPORT_HTML="$REPORT_DIR/zap-report-$TIMESTAMP.html"
ZAP_REPORT_JSON="$REPORT_DIR/zap-report-$TIMESTAMP.json"
ZAP_REPORT_XML="$REPORT_DIR/zap-report-$TIMESTAMP.xml"
SCAN_RESULTS_FILE="$REPORT_DIR/dast-scan-summary-$TIMESTAMP.txt"

# Default target URL (can be overridden)
TARGET_URL="${1:-https://mopsus-test.altum.ai}"

# Create reports directory
mkdir -p $REPORT_DIR

# Logging function
log_result() {
    echo "$1" | tee -a "$SCAN_RESULTS_FILE"
}

# Check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

echo -e "${BLUE}🔍 Starting DAST Security Scanning for Mopsus Data Platform${NC}"
echo -e "${BLUE}Target URL: $TARGET_URL${NC}"
echo -e "${BLUE}Reports will be saved to: $REPORT_DIR${NC}"
echo ""

# Initialize scan results file
{
    echo "=========================================="
    echo "DAST SECURITY SCAN RESULTS"
    echo "=========================================="
    echo "Scan Date: $(date)"
    echo "Target URL: $TARGET_URL"
    echo "Project: Mopsus Data Platform"
    echo ""
} > "$SCAN_RESULTS_FILE"

# Function to run OWASP ZAP scan
run_zap_scan() {
    echo -e "${YELLOW}🕷️  Running OWASP ZAP Security Scan...${NC}"
    
    if command_exists docker; then
        echo "Using Docker for ZAP scanning..."
        
        # Run ZAP baseline scan
        echo "Running ZAP baseline scan..."
        docker run --rm -v "$(pwd)/$REPORT_DIR:/zap/wrk/:rw" \
            owasp/zap2docker-stable:latest zap-baseline.py \
            -t "$TARGET_URL" \
            -J "zap-baseline-$TIMESTAMP.json" \
            -H "zap-baseline-$TIMESTAMP.html" \
            -x "zap-baseline-$TIMESTAMP.xml" \
            -r "zap-baseline-$TIMESTAMP.md" \
            -I || true
        
        # Run ZAP full scan (more comprehensive but takes longer)
        echo "Running ZAP full scan..."
        docker run --rm -v "$(pwd)/$REPORT_DIR:/zap/wrk/:rw" \
            owasp/zap2docker-stable:latest zap-full-scan.py \
            -t "$TARGET_URL" \
            -J "zap-full-$TIMESTAMP.json" \
            -H "zap-full-$TIMESTAMP.html" \
            -x "zap-full-$TIMESTAMP.xml" \
            -r "zap-full-$TIMESTAMP.md" \
            -I || true
        
        log_result "✅ ZAP scanning completed"
        
    elif command_exists zap.sh || command_exists zap-cli; then
        echo "Using local ZAP installation..."
        
        # Use local ZAP installation
        if command_exists zap-cli; then
            zap-cli start --start-options '-config api.disablekey=true'
            zap-cli open-url "$TARGET_URL"
            zap-cli spider "$TARGET_URL"
            zap-cli active-scan "$TARGET_URL"
            zap-cli report -o "$ZAP_REPORT_HTML" -f html
            zap-cli report -o "$ZAP_REPORT_JSON" -f json
            zap-cli report -o "$ZAP_REPORT_XML" -f xml
            zap-cli shutdown
        fi
        
        log_result "✅ ZAP scanning completed"
        
    else
        echo "⚠️  OWASP ZAP not available (neither Docker nor local installation found)"
        echo "Please install Docker or OWASP ZAP to run DAST scanning"
        log_result "⚠️  DAST scanning skipped (ZAP not available)"
        return
    fi
}

# Function to run additional security checks
run_additional_checks() {
    echo -e "${YELLOW}🔒 Running Additional Security Checks...${NC}"
    
    # Check SSL/TLS configuration
    if command_exists openssl; then
        echo "Checking SSL/TLS configuration..."
        echo "$TARGET_URL" | sed 's|https://||' | sed 's|/.*||' | while read -r domain; do
            echo "SSL check for $domain:" >> "$REPORT_DIR/ssl-check-$TIMESTAMP.txt"
            echo | openssl s_client -connect "$domain:443" -servername "$domain" 2>/dev/null | \
                openssl x509 -noout -text >> "$REPORT_DIR/ssl-check-$TIMESTAMP.txt" 2>/dev/null || true
        done
        log_result "✅ SSL/TLS check completed"
    fi
    
    # Check HTTP security headers
    if command_exists curl; then
        echo "Checking HTTP security headers..."
        curl -I "$TARGET_URL" > "$REPORT_DIR/http-headers-$TIMESTAMP.txt" 2>/dev/null || true
        log_result "✅ HTTP headers check completed"
    fi
}

# Function to generate summary report
generate_summary() {
    echo -e "${YELLOW}📋 Generating DAST Summary Report...${NC}"
    
    {
        echo ""
        echo "SCANS PERFORMED:"
        echo "- OWASP ZAP baseline scan"
        echo "- OWASP ZAP full scan"
        echo "- SSL/TLS configuration check"
        echo "- HTTP security headers analysis"
        echo ""
        echo "REPORT FILES GENERATED:"
        ls -la "$REPORT_DIR"/*$TIMESTAMP* 2>/dev/null || echo "No report files found"
        echo ""
        echo "KEY SECURITY AREAS TESTED:"
        echo "- SQL Injection (A03:2021)"
        echo "- Cross-Site Scripting (A03:2021)"
        echo "- Broken Authentication (A07:2021)"
        echo "- Sensitive Data Exposure (A02:2021)"
        echo "- XML External Entities (A04:2021)"
        echo "- Broken Access Control (A01:2021)"
        echo "- Security Misconfiguration (A05:2021)"
        echo "- Cross-Site Request Forgery (A01:2021)"
        echo "- Components with Known Vulnerabilities (A06:2021)"
        echo "- Insufficient Logging & Monitoring (A09:2021)"
        echo ""
        echo "RECOMMENDATIONS:"
        echo "1. Review all HIGH and CRITICAL severity findings immediately"
        echo "2. Fix MEDIUM severity issues in the next sprint"
        echo "3. Address LOW severity findings as time permits"
        echo "4. Implement security headers if missing"
        echo "5. Ensure proper SSL/TLS configuration"
        echo "6. Regular DAST scanning in CI/CD pipeline"
        echo ""
        echo "NEXT STEPS:"
        echo "1. Open HTML reports for detailed analysis"
        echo "2. Create tickets for security findings"
        echo "3. Implement fixes and re-scan"
        echo "4. Update security documentation"
        echo ""
    } >> "$SCAN_RESULTS_FILE"
    
    log_result "📋 Summary report generated: $SCAN_RESULTS_FILE"
}

# Main execution
main() {
    # Validate target URL
    if [[ ! "$TARGET_URL" =~ ^https?:// ]]; then
        echo -e "${RED}❌ Error: Invalid target URL. Please provide a valid HTTP/HTTPS URL${NC}"
        echo "Usage: $0 [target_url]"
        echo "Example: $0 https://mopsus-test.altum.ai"
        exit 1
    fi
    
    # Run all scans
    run_zap_scan
    run_additional_checks
    
    # Generate summary
    generate_summary
    
    echo -e "${GREEN}🎉 DAST security scanning completed successfully!${NC}"
    echo -e "${BLUE}📋 Summary report: $SCAN_RESULTS_FILE${NC}"
    echo -e "${BLUE}📁 Detailed reports: $REPORT_DIR/${NC}"
    echo -e "${YELLOW}💡 Tip: Open the HTML reports in your browser for detailed analysis${NC}"
}

# Run main function
main "$@"
