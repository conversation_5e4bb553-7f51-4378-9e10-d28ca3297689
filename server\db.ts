import { Pool } from "pg";

let pool: Pool;
if (process.env.NODE_ENV === "production") {
  pool = new Pool({
    connectionString: process.env.POSTGRES_URI!,
  });
} else if (process.env.NODE_ENV === "development") {
  pool = new Pool({
    user: process.env.DB_USER!,
    password: process.env.DB_PASSWORD!,
    host: process.env.DB_HOST!,
    port: parseInt(process.env.DB_PORT!),
    database: process.env.DB!,
  }).on("connect", () => {
    console.log(`Connected to the DB: ${process.env.NODE_ENV}`);
  });
} else {
  pool = new Pool({
    connectionString: process.env.POSTGRES_TEST_URI!,
  }).on("connect", () => {
    console.log(`Connected to the DB: ${process.env.NODE_ENV}`);
  });
}

export default pool;
