// AUTHENTICATION
export const AUTH_REQUEST = 'AUTH_SIGNIN_REQUEST';
export const AUTH_SIGNIN_SUCCESS = 'AUTH_SIGNIN_SUCCESS';
export const AUTH_SIGNIN_FAIL = 'AUTH_SIGNIN_FAIL';
export const AUTH_LOAD_USER_SUCCESS = 'AUTH_LOAD_USER_SUCCESS';
export const AUTH_LOAD_USER_FAIL = 'AUTH_LOAD_USER_FAIL';
export const AUTH_SIGNUP_VERIFIED_REQUEST = 'AUTH_SIGNUP_VERIFIED_REQUEST';
export const AUTH_SIGNUP_SUCCESS = 'AUTH_SIGNUP_SUCCESS';
export const AUTH_SIGNUP_FAIL = 'AUTH_SIGNUP_FAIL';
export const AUTH_LOGOUT = 'AUTH_LOGOUT';
// ECOVALUE API
export const SUSTAINABILITY_API_REQUEST = 'SUSTAINABILITY_API_REQUEST';
export const SUSTAINABILITY_API_REQUEST_SUCCESS =	'SUSTAINABILITY_API_REQUEST_SUCCESS';
export const SUSTAINABILITY_API_REQUEST_FAIL =	'SUSTAINABILITY_API_REQUEST_FAIL';
export const SUSTAINABILITY_API_CLEAR_REQUEST =	'SUSTAINABILITY_API_CLEAR_REQUEST';
export const SUSTAINABILITY_API_MODIFY_REQUEST =	'SUSTAINABILITY_API_MODIFY_REQUEST';
export const SUSTAINABILITY_API_ONE_TIME_USED =	'SUSTAINABILITY_API_ONE_TIME_USED';
// AVM API
export const AVM_API_REQUEST = 'AVM_API_REQUEST';
export const AVM_API_REQUEST_SUCCESS = 'AVM_API_REQUEST_SUCCESS';
export const AVM_API_REQUEST_FAIL = 'AVM_API_REQUEST_FAIL';
export const AVM_API_CLEAR_REQUEST = 'AVM_API_CLEAR_REQUEST';
export const AVM_API_MODIFY_REQUEST = 'AVM_API_MODIFY_REQUEST';
export const AVM_API_ONE_TIME_USED = 'AVM_API_ONE_TIME_USED';
// AUTOSUGGEST API
export const AUTOSUGGEST_API_ONE_TIME_USED = 'AUTOSUGGEST_API_ONE_TIME_USED';
export const AUTOSUGGEST_API_REQUEST = 'AUTOSUGGEST_API_REQUEST';
export const AUTOSUGGEST_API_REQUEST_SUCCESS =	'AUTOSUGGEST_API_REQUEST_SUCCESS';
export const AUTOSUGGEST_API_REQUEST_FAIL = 'AUTOSUGGEST_API_REQUEST_FAIL';
export const AUTOSUGGEST_API_CLEAR_REQUEST = 'AUTOSUGGEST_API_CLEAR_REQUEST';
export const AUTOSUGGEST_API_MODIFY_REQUEST = 'AUTOSUGGEST_API_MODIFY_REQUEST';
// API USAGE
export const API_USAGE_REQUEST = 'API_USAGE_REQUEST';
export const API_USAGE_SUCCESS = 'API_USAGE_SUCCESS';
export const API_TOTAL_CALLS = 'API_TOTAL_CALLS';
export const API_USAGE_FAIL = 'API_USAGE_FAIL';
export const API_ANALYTICS = 'API_ANALYTICS';
// USER ACCOUNT
export const USER_ACCOUNT_REQUEST = 'USER_ACCOUNT_REQUEST';
export const USER_ACCOUNT_DELETE = 'USER_ACCOUNT_DELETE';
// WOZ API
export const WOZ_API_REQUEST = 'WOZ_API_REQUEST';
export const WOZ_API_REQUEST_SUCCESS = 'WOZ_API_REQUEST_SUCCESS';
export const WOZ_API_REQUEST_FAIL = 'WOZ_API_REQUEST_FAIL';
export const WOZ_API_CLEAR_REQUEST = 'WOZ_API_CLEAR_REQUEST';
export const WOZ_API_MODIFY_REQUEST = 'WOZ_API_MODIFY_REQUEST';
export const WOZ_API_ONE_TIME_USED = 'WOZ_API_ONE_TIME_USED';
// LOCATION DATA API
export const LOCATION_DATA_API_REQUEST = 'LOCATION_DATA_API_REQUEST';
export const LOCATION_DATA_API_REQUEST_SUCCESS =	'LOCATION_DATA_API_REQUEST_SUCCESS';
export const LOCATION_DATA_API_REQUEST_FAIL = 'LOCATION_DATA_API_REQUEST_FAIL';
export const LOCATION_DATA_API_CLEAR_REQUEST =	'LOCATION_DATA_API_CLEAR_REQUEST';
export const LOCATION_DATA_API_MODIFY_REQUEST =	'LOCATION_DATA_API_MODIFY_REQUEST';
export const LOCATION_DATA_API_ONE_TIME_USED =	'LOCATION_DATA_API_ONE_TIME_USED';
// MOVE DATA
export const MOVEDATA_API_REQUEST = 'MOVEDATA_API_REQUEST';
export const MOVEDATA_API_REQUEST_SUCCESS = 'MOVEDATA_API_REQUEST_SUCCESS';
export const MOVEDATA_API_REQUEST_FAIL = 'MOVEDATA_API_REQUEST_FAIL';
export const MOVEDATA_API_CLEAR_REQUEST = 'MOVEDATA_API_CLEAR_REQUEST';
export const MOVEDATA_API_MODIFY_REQUEST = 'MOVEDATA_API_MODIFY_REQUEST';
export const MOVEDATA_API_ONE_TIME_USED = 'MOVEDATA_API_ONE_TIME_USED';

// OBJECT GEOMETRY
export const OBJECT_GEOMETRY_API_REQUEST = 'OBJECT_GEOMETRY_API_REQUEST';
export const OBJECT_GEOMETRY_API_REQUEST_SUCCESS = 'OBJECT_GEOMETRY_API_REQUEST_SUCCESS';
export const OBJECT_GEOMETRY_API_REQUEST_FAIL = 'OBJECT_GEOMETRY_API_REQUEST_FAIL';
export const OBJECT_GEOMETRY_API_CLEAR_REQUEST = 'OBJECT_GEOMETRY_API_CLEAR_REQUEST';
export const OBJECT_GEOMETRY_API_MODIFY_REQUEST = 'OBJECT_GEOMETRY_API_MODIFY_REQUEST';
export const OBJECT_GEOMETRY_API_ONE_TIME_USED = 'OBJECT_GEOMETRY_API_ONE_TIME_USED';
// ENERGY LABEL
export const ENERGY_LABEL_API_REQUEST = 'ENERGY_LABEL_API_REQUEST';
export const ENERGY_LABEL_API_REQUEST_SUCCESS = 'ENERGY_LABEL_API_REQUEST_SUCCESS';
export const ENERGY_LABEL_API_REQUEST_FAIL = 'ENERGY_LABEL_API_REQUEST_FAIL';
export const ENERGY_LABEL_API_CLEAR_REQUEST = 'ENERGY_LABEL_API_CLEAR_REQUEST';
export const ENERGY_LABEL_API_MODIFY_REQUEST = 'ENERGY_LABEL_API_MODIFY_REQUEST';
export const ENERGY_LABEL_API_ONE_TIME_USED = 'ENERGY_LABEL_API_ONE_TIME_USED';
// AMENITIES API
export const AMENITIES_API_REQUEST = 'AMENITIES_API_REQUEST';
export const AMENITIES_API_REQUEST_SUCCESS = 'AMENITIES_API_REQUEST_SUCCESS';
export const AMENITIES_API_REQUEST_FAIL = 'AMENITIES_API_REQUEST_FAIL';
export const AMENITIES_API_CLEAR_REQUEST = 'AMENITIES_API_CLEAR_REQUEST';
export const AMENITIES_API_MODIFY_REQUEST = 'AMENITIES_API_MODIFY_REQUEST';
export const AMENITIES_API_ONE_TIME_USED = 'AMENITIES_API_ONE_TIME_USED';
// IMAGE LABEL API
export const IMAGE_LABEL_API_REQUEST = 'IMAGE_LABEL_API_REQUEST';
export const IMAGE_LABEL_API_REQUEST_SUCCESS =	'IMAGE_LABEL_API_REQUEST_SUCCESS';
export const IMAGE_LABEL_API_REQUEST_FAIL = 'IMAGE_LABEL_API_REQUEST_FAIL';
export const IMAGE_LABEL_API_CLEAR_REQUEST = 'IMAGE_LABEL_API_CLEAR_REQUEST';
export const IMAGE_LABEL_API_MODIFY_REQUEST = 'IMAGE_LABEL_API_MODIFY_REQUEST';
export const IMAGE_LABEL_API_ONE_TIME_USED = 'IMAGE_LABEL_API_ONE_TIME_USED';

// CONDITION_SCORE API
export const CONDITION_SCORE_API_REQUEST = 'CONDITION_SCORE_API_REQUEST';
export const CONDITION_SCORE_API_REQUEST_SUCCESS =	'CONDITION_SCORE_API_REQUEST_SUCCESS';
export const CONDITION_SCORE_API_REQUEST_FAIL =	'CONDITION_SCORE_API_REQUEST_FAIL';
export const CONDITION_SCORE_API_CLEAR_REQUEST =	'CONDITION_SCORE_API_CLEAR_REQUEST';
export const CONDITION_SCORE_API_MODIFY_REQUEST =	'CONDITION_SCORE_API_MODIFY_REQUEST';
export const CONDITION_SCORE_API_ONE_TIME_USED =	'CONDITION_SCORE_API_ONE_TIME_USED';

// BAG API
export const BAG_API_REQUEST = 'BAG_API_REQUEST';
export const BAG_API_REQUEST_SUCCESS = 'BAG_API_REQUEST_SUCCESS';
export const BAG_API_REQUEST_FAIL = 'BAG_API_REQUEST_FAIL';
export const BAG_API_CLEAR_REQUEST = 'BAG_API_CLEAR_REQUEST';
export const BAG_API_MODIFY_REQUEST = 'BAG_API_MODIFY_REQUEST';
export const BAG_API_ONE_TIME_USED = 'BAG_API_ONE_TIME_USED';
// MESSAGES
export const GET_ALL_MESSAGES_ADMIN = 'GET_ALL_MESSAGES_ADMIN';
export const GET_ALL_MESSAGES_USER = 'GET_ALL_MESSAGES_USER';
export const GET_ALL_UNREAD_MESSAGES_USER = 'GET_ALL_UNREAD_MESSAGES_USER';

export const GET_CURRENT_MESSAGE = 'GET_CURRENT_MESSAGE';
export const CLEAR_CURRENT_MESSAGE = 'CLEAR_CURRENT_MESSAGE';

export const SET_CURRENT_PAGE = 'SET_CURRENT_PAGE';
export const ADD_SELECTED_MESSAGES = 'ADD_SELECTED_MESSAGES';
export const REMOVE_SELECTED_MESSAGES = 'REMOVE_SELECTED_MESSAGES';
export const MARK_ALL_AS_READ = 'MARK_ALL_AS_READ';
export const DELETE_SELECTED_MESSAGES_USER = 'DELETE_SELECTED_MESSAGES_USER';

export const DELETE_CURRENT_MESSAGE_ADMIN = 'DELETE_CURRENT_MESSAGE_ADMIN';
export const DELETE_CURRENT_MESSAGE_USER = 'DELETE_CURRENT_MESSAGE_USER';
export const DELETE_SELECTED_MESSAGES_ADMIN = 'DELETE_SELECTED_MESSAGES_ADMIN';
// ADMIN
export const ADMIN_GET_USERS_REQUEST = 'ADMIN_GET_USERS_REQUEST';
export const ADMIN_GET_USERS_REQUEST_SUCCESS =	'ADMIN_GET_USERS_REQUEST_SUCCESS';
export const ADMIN_GET_USERS_REQUEST_FAIL = 'ADMIN_GET_USERS_REQUEST_FAIL';
export const ADMIN_GET_ALL_MESSAGES_REQUEST = 'ADMIN_GET_ALL_MESSAGES_REQUEST';
export const ADMIN_GET_ALL_MESSAGES_REQUEST_SUCCESS =	'ADMIN_GET_ALL_MESSAGES_REQUEST_SUCCESS';
export const ADMIN_GET_ALL_MESSAGES_REQUEST_FAIL =	'ADMIN_GET_ALL_MESSAGES_REQUEST_FAIL';
export const ADMIN_POST_NEW_MESSAGE_REQUEST = 'ADMIN_POST_NEW_MESSAGE_REQUEST';
export const ADMIN_POST_NEW_MESSAGE_SUCCESS = 'ADMIN_POST_NEW_MESSAGE_SUCCESS';
export const ADMIN_POST_NEW_MESSAGE_FAIL = 'ADMIN_POST_NEW_MESSAGE_FAIL';
export const ADMIN_DELETE_MESSAGES_REQUEST = 'ADMIN_DELETE_MESSAGES_REQUEST';
export const ADMIN_DELETE_MESSAGES_SUCCESS = 'ADMIN_DELETE_MESSAGES_SUCCESS';
export const ADMIN_DELETE_MESSAGES_FAIL = 'ADMIN_DELETE_MESSAGES_FAIL';
export const ADMIN_GET_ANALYTICS_REQUEST = 'ADMIN_GET_ANALYTICS_REQUEST';
export const ADMIN_GET_ANALYTICS_REQUEST_SUCCESS =	'ADMIN_GET_ANALYTICS_REQUEST_SUCCESS';
export const ADMIN_GET_ANALYTICS_REQUEST_FAIL =	'ADMIN_GET_ANALYTICS_REQUEST_FAIL';
export const ADMIN_EDIT_USER_REQUEST = 'ADMIN_EDIT_USER_REQUEST';
export const ADMIN_EDIT_USER_SUCCESS = 'ADMIN_EDIT_USER_SUCCESS';
export const ADMIN_EDIT_USER_FAIL = 'ADMIN_EDIT_USER_FAIL';
export const ADMIN_DELETE_USER_REQUEST = 'ADMIN_DELETE_USER_REQUEST';
export const ADMIN_DELETE_USER_SUCCESS = 'ADMIN_DELETE_USER_SUCCESS';
export const ADMIN_DELETE_USER_FAIL = 'ADMIN_DELETE_USER_FAIL';
// MOPSUS STRIPE
export const SAVE_CHECKOUT_PLAN_STATE = 'SAVE_CHECKOUT_PLAN_STATE';
export const CLEAR_CHECKOUT_PLAN_STATE = 'CLEAR_CHECKOUT_PLAN_STATE';
export const GET_ALL_INVOICES = 'GET_ALL_INVOICES';
export const GET_COMING_INVOICE = 'GET_COMING_INVOICE';
export const GET_ALL_PAYMENT_METHODS = 'GET_ALL_PAYMENT_METHODS';
export const EDIT_CARD_DETAILS = 'EDIT_CARD_DETAILS';
export const GET_ERROR = 'GET_ERROR';
export const SUBSCRIPTION_REQUEST = 'SUBSCRIPTION_REQUEST';
export const CHECK_SUBSCRIPTION_STATUS_SUCCESS =	'CHECK_SUBSCRIPTION_STATUS_SUCCESS';
export const CHECK_SUBSCRIPTION_STATUS_FAILED =	'CHECK_SUBSCRIPTION_STATUS_FAILED';
export const CREATE_SUBSCRIPTION_SUCCESS = 'CREATE_SUBSCRIPTION_SUCCESS';
export const CREATE_SUBSCRIPTION_FAILED = 'CREATE_SUBSCRIPTION_FAILED';
export const CANCEL_SUBSCRIPTION_SUCCESS = 'CANCEL_SUBSCRIPTION_SUCCESS';
export const CANCEL_SUBSCRIPTION_FAILED = 'CANCEL_SUBSCRIPTION_FAILED';
export const GET_CARD_PAYMENT_METHODS = 'GET_CARD_PAYMENT_METHODS';
export const GET_SEPA_PAYMENT_METHODS = 'GET_SEPA_PAYMENT_METHODS';
// TRANSACTION STRIPE

export const GET_TRANSACTION_COMING_INVOICE_SUCCESS =	'GET_TRANSACTION_COMING_INVOICE_SUCCESS';
export const GET_TRANSACTION_COMING_INVOICE_FAILED =	'GET_TRANSACTION_COMING_INVOICE_FAILED';
export const CHECK_TRANSACTION_SUBSCRIPTION_STATUS_SUCCESS =	'CHECK_TRANSACTION_SUBSCRIPTION_STATUS_SUCCESS';
export const CHECK_TRANSACTION_SUBSCRIPTION_STATUS_FAILED =	'CHECK_TRANSACTION_SUBSCRIPTION_STATUS_FAILED';
export const CREATE_TRANSACTION_SUBSCRIPTION_SUCCESS =	'CREATE_TRANSACTION_SUBSCRIPTION_SUCCESS';
export const CREATE_TRANSACTION_SUBSCRIPTION_FAILED =	'CREATE_TRANSACTION_SUBSCRIPTION_FAILED';
export const CANCEL_TRANSACTION_SUBSCRIPTION_SUCCESS =	'CANCEL_TRANSACTION_SUBSCRIPTION_SUCCESS';
export const CANCEL_TRANSACTION_SUBSCRIPTION_FAILED =	'CANCEL_SUBSCRIPTION_FAILED';

// REFERENCE API
export const REFERENCE_API_REQUEST = 'REFERENCE_API_REQUEST';
export const REFERENCE_API_REQUEST_SUCCESS = 'REFERENCE_API_REQUEST_SUCCESS';
export const REFERENCE_API_REQUEST_FAIL = 'REFERENCE_API_REQUEST_FAIL';
export const REFERENCE_API_CLEAR_REQUEST = 'REFERENCE_API_CLEAR_REQUEST';
export const REFERENCE_API_MODIFY_REQUEST = 'REFERENCE_API_MODIFY_REQUEST';
export const REFERENCE_API_ONE_TIME_USED = 'REFERENCE_API_ONE_TIME_USED';

// TRANSACTION API
export const TRANSACTION_API_REQUEST = 'TRANSACTION_API_REQUEST';
export const TRANSACTION_API_REQUEST_SUCCESS =	'TRANSACTION_API_REQUEST_SUCCESS';
export const TRANSACTION_API_REQUEST_FAIL = 'TRANSACTION_API_REQUEST_FAIL';
export const TRANSACTION_API_CLEAR_REQUEST = 'TRANSACTION_API_CLEAR_REQUEST';
export const TRANSACTION_API_MODIFY_REQUEST = 'TRANSACTION_API_MODIFY_REQUEST';
export const TRANSACTION_API_ONE_TIME_USED = 'TRANSACTION_API_ONE_TIME_USED';
export const TRANSACTION_API_PING_SUCCESS = 'TRANSACTION_API_PING_SUCCESS';
export const TRANSACTION_API_PING_FAIL = 'TRANSACTION_API_PING_FAIL';

export const CLEAR_REDUX_STATE = 'CLEAR_REDUX_STATE';
