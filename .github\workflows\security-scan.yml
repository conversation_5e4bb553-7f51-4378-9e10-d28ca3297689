name: Security Scanning

on:
  push:
    branches: [ master, staging ]
  pull_request:
    branches: [ master, staging ]
  schedule:
    # Run security scan daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: |
          client/package-lock.json
          server/package-lock.json
    
    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.x'
    
    - name: Install dependencies
      run: |
        cd client && npm ci --legacy-peer-deps
        cd ../server && npm ci
        cd ..
    
    - name: Install security scanning tools
      run: |
        pip install semgrep
        npm install -g retire audit-ci
    
    - name: Run dependency vulnerability scanning
      run: |
        echo "::group::Client dependency scan"
        cd client
        npm audit --audit-level=moderate || true
        audit-ci --moderate || true
        retire --outputformat json --outputpath ../client-retire-report.json . || true
        cd ..
        
        echo "::group::Server dependency scan"
        cd server
        npm audit --audit-level=moderate || true
        audit-ci --moderate || true
        retire --path . --outputformat json --outputpath ../server-retire-report.json . || true
        cd ..
    
    - name: Run Semgrep SAST scanning
      run: |
        echo "::group::Semgrep SAST scan"
        semgrep --config=.semgrep.yml --json --output=semgrep-custom-report.json . || true
        semgrep --config=auto --json --output=semgrep-auto-report.json . || true
        semgrep --config=p/owasp-top-ten --json --output=semgrep-owasp-report.json . || true
    
    - name: Run ESLint security scanning
      run: |
        echo "::group::ESLint security scan"
        cd client
        npx eslint . --ext .ts,.tsx --config ../.eslintrc.security.js --format json --output-file ../eslint-client-security-report.json || true
        cd ../server
        npx eslint . --ext .ts --config ../.eslintrc.security.js --format json --output-file ../eslint-server-security-report.json || true
        cd ..
    
    - name: Setup Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Build Docker image for scanning
      run: |
        docker build -t mopsus-security-scan -f Dockerfile .
    
    - name: Run Trivy container scan
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'mopsus-security-scan'
        format: 'json'
        output: 'trivy-report.json'
      continue-on-error: true
    
    - name: Run Docker Bench Security
      run: |
        docker run --rm --net host --pid host --userns host --cap-add audit_control \
          -e DOCKER_CONTENT_TRUST=$DOCKER_CONTENT_TRUST \
          -v /etc:/etc:ro \
          -v /usr/bin/containerd:/usr/bin/containerd:ro \
          -v /usr/bin/runc:/usr/bin/runc:ro \
          -v /usr/lib/systemd:/usr/lib/systemd:ro \
          -v /var/lib:/var/lib:ro \
          -v /var/run/docker.sock:/var/run/docker.sock:ro \
          --label docker_bench_security \
          docker/docker-bench-security:latest > docker-bench-report.txt || true
    
    - name: Generate security summary
      run: |
        echo "# Security Scan Summary" > security-summary.md
        echo "" >> security-summary.md
        echo "## Scan Results" >> security-summary.md
        echo "" >> security-summary.md
        echo "- **Date**: $(date)" >> security-summary.md
        echo "- **Commit**: ${{ github.sha }}" >> security-summary.md
        echo "- **Branch**: ${{ github.ref_name }}" >> security-summary.md
        echo "" >> security-summary.md
        echo "## Tools Used" >> security-summary.md
        echo "" >> security-summary.md
        echo "- Semgrep (SAST)" >> security-summary.md
        echo "- npm audit (Dependency vulnerabilities)" >> security-summary.md
        echo "- retire.js (Vulnerable libraries)" >> security-summary.md
        echo "- ESLint (Code quality and security)" >> security-summary.md
        echo "- Trivy (Container vulnerabilities)" >> security-summary.md
        echo "- Docker Bench Security (Container configuration)" >> security-summary.md
        echo "" >> security-summary.md
        echo "## Report Files" >> security-summary.md
        echo "" >> security-summary.md
        ls -la *-report.* >> security-summary.md || true
    
    - name: Upload security reports
      uses: actions/upload-artifact@v4
      with:
        name: security-reports-${{ github.run_number }}
        path: |
          *-report.json
          *-report.txt
          security-summary.md
        retention-days: 30
    
    - name: Comment PR with security summary
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const summary = fs.readFileSync('security-summary.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## 🔒 Security Scan Results\n\n${summary}\n\n📁 Detailed reports are available in the workflow artifacts.`
          });
    
    - name: Fail on critical vulnerabilities
      run: |
        # Check for critical findings and fail the build if found
        # This is a placeholder - implement based on your security requirements
        echo "Checking for critical security findings..."
        
        # Example: Check Semgrep results for critical issues
        if [ -f "semgrep-auto-report.json" ]; then
          CRITICAL_COUNT=$(jq '[.results[] | select(.extra.severity == "ERROR")] | length' semgrep-auto-report.json || echo "0")
          if [ "$CRITICAL_COUNT" -gt "0" ]; then
            echo "❌ Found $CRITICAL_COUNT critical security issues"
            echo "Please review the security reports and fix critical issues before merging"
            exit 1
          fi
        fi
        
        echo "✅ No critical security issues found"

  # Separate job for DAST scanning (if needed in the future)
  dast-scan:
    name: Dynamic Application Security Testing
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/staging'
    needs: security-scan
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: OWASP ZAP Baseline Scan
      uses: zaproxy/action-baseline@v0.10.0
      with:
        target: 'https://mopsus-test.altum.ai'
        rules_file_name: '.zap/rules.tsv'
        cmd_options: '-a'
      continue-on-error: true
    
    - name: Upload DAST results
      uses: actions/upload-artifact@v4
      with:
        name: dast-report-${{ github.run_number }}
        path: report_html.html
        retention-days: 30
