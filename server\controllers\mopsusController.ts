import { NextFunction, Request, Response } from "express";
import AppError from "../utils/appError";
import SupportEmail from "../utils/supportEmailHandler";
import { tagEvent } from "../utils/amplitudeLogger";
import { translateEnglishToDutch } from "../utils/translateToDutch";

export const sendSupportEmail = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { email, subject, text } = req.body;

    const supportEmail = new SupportEmail(email);
    supportEmail.sendSupportEmail(subject, text);

    res.status(200).json({
      status: "success",
      messageSent: true,
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const sendCoropData = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const corop = require("../mapdata/corop.json");
    const coropPC6 = require("../mapdata/coropPC6.json");
    const coropWOZ = require("../mapdata/coropWOZ.json");

    res.status(200).json({
      status: "success",
      corop,
      coropPC6,
      coropWOZ,
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const sendGementeeData = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const gementee = require("../mapdata/gem.json");
    const gementeePC6 = require("../mapdata/gemPC6.json");
    const gementeeWOZ2015 = require("../mapdata/gemWOZ2015.json");
    const gementeeWOZ2017 = require("../mapdata/gemWOZ2017.json");
    const gementeeWOZ2018 = require("../mapdata/gemWOZ2018.json");
    const gementeeWOZ2019 = require("../mapdata/gemWOZ2019.json");
    const gementeeWOZ2020 = require("../mapdata/gemWOZ2020.json");

    res.status(200).json({
      status: "success",
      gementee,
      gementeePC6,
      gementeeWOZ2015,
      gementeeWOZ2017,
      gementeeWOZ2018,
      gementeeWOZ2019,
      gementeeWOZ2020,
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const tagAmplitudeEvent = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { event, userId } = req.body;
    const response = await tagEvent(event, userId);

    return res.status(200).json({
      status: "success",
      data: response,
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const translator = async (req: Request, res: Response) => {
  try {
    const text = req.body;
    const translation = await translateEnglishToDutch(text.text);
    res.json(translation);
  } catch (error: any) {
    console.log(error);
    return new AppError(error.message, 400, false);
  }
};
