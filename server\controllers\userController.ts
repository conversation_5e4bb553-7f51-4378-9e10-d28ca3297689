import bcrypt from "bcryptjs";
import jwt, { SignOptions } from "jsonwebtoken";
import AWS from "aws-sdk";
import axios from "axios";
import Stripe from "stripe";
import { NextFunction, Request, Response } from "express";
import AppError from "../utils/appError";
import pool from "../db";
import { AuthRequest, User } from "../@types";
import { createTokenResponse } from "../utils/authUtil";
import {
  verifyUnsubscribeToken,
  clearUnsubscribeToken,
} from "../utils/unsubscribeUtils";

AWS.config.update({ region: "eu-west-1" });
const apiGateway = new AWS.APIGateway({
  accessKeyId: process.env.AWS_ACCESS_KEY!,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  endpoint: "https://apigateway.eu-west-1.amazonaws.com",
});

const stripe = new Stripe(
  process.env.NODE_ENV === "production"
    ? process.env.STRIPE_SK_LIVE!
    : process.env.STRIPE_SK_TEST!,
  { apiVersion: "2025-02-24.acacia" },
);

const signJWTToken = (user_id: number): string => {
  const options: SignOptions = {
    expiresIn: 24 * 60 * 60, // 24 hours in seconds
  };
  return jwt.sign(
    { id: user_id },
    process.env.JWT_SECRET_KEY || "default-secret-key",
    options,
  );
};

export const changePassword = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { oldPassword, newPassword, confirmPassword } = req.body;
    if (req.user == null) {
      return;
    }
    const user1 = req.user as User;
    const user = await pool.query<User>(
      "SELECT * from users WHERE email=$1 AND active=$2",
      [user1.email, true],
    );
    if (user.rows.length === 0) {
      next(new AppError("Geen gebruiker gevonden", 400, true));
      return;
    }

    const verifyOldPassword = await bcrypt.compare(
      oldPassword,
      user.rows[0].password,
    );

    if (!verifyOldPassword) {
      next(new AppError("Wachtwoord is niet correct", 400, true));
      return;
    }

    if (newPassword !== confirmPassword) {
      next(new AppError("Wachtwoorden komen niet overeen", 400, true));
      return;
    }
    const salt = await bcrypt.genSalt(10);
    const encryptedPassword = await bcrypt.hash(newPassword, salt);

    const updatedUser = await pool.query<User>(
      "UPDATE users SET password=$1, password_reset_token=$2, password_reset_token_expiry=$3 WHERE email=$4 RETURNING *",
      [encryptedPassword, "", null, user.rows[0].email],
    );

    createTokenResponse(updatedUser.rows[0], res);
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const deleteAccountDetails = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  const { password, id } = req.body;
  const user = await pool.query<User>(
    "SELECT * from users WHERE user_id=$1 AND active=$2",
    [id, true],
  );
  if (user.rows.length === 0) {
    next(new AppError("Geen gebruiker gevonden", 400, true));
    return;
  }

  const verifyPassword = await bcrypt.compare(password, user.rows[0].password);
  if (!verifyPassword) {
    next(new AppError("Password is incorrect", 400, true));
    return;
  }

  await axios.delete(`${req.protocol}://${req.get(
    "host",
  )}/api/v1/users/account/delete-account/${id}
  `);
};

export const deleteAccount = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    if (req.user == null) {
      return;
    }
    const user = req.user as User;
    apiGateway.deleteApiKey({ apiKey: user.api_key_id }, async (err, data) => {
      if (err) {
        next(new AppError(err.message, 400));
        return;
      }
      await stripe.customers.del(user.stripe_customer_id);

      const updatedUser = await pool.query<User>(
        "UPDATE users SET api_key=$1, api_key_id=$2, stripe_customer_id=$3, active=$4, current_usage_plan=$5 WHERE email=$6 RETURNING *",
        ["", "", "", false, process.env.AWS_MOPSUS_USAGE_PLAN_FREE, user.email],
      );

      await pool.query("DELETE from users where email=$1", [user.email]);

      res.status(204).json({
        status: "success",
        deleted: true,
        updatedUser,
      });
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const checkEmailExists = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { email } = req.query;

    const result = await pool.query<User>(
      "SELECT * FROM users WHERE email=$1",
      [email],
    );

    const exists = result.rows.length > 0;
    const isSubscribed = exists && result.rows[0].receive_email;

    res.status(200).json({
      exists,
      isSubscribed,
      message:
        !exists || !isSubscribed
          ? "Er is een fout opgetreden bij het verwerken van je verzoek"
          : "E-mail controle voltooid",
    });
  } catch (error: any) {
    next(new AppError(error.message, 500));
  }
};

export const unsubscribeToEmail = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { email, token } = req.query;

    if (!email || !token) {
      return next(new AppError("Email and token are required", 400));
    }

    const user = await pool.query<User>("SELECT * from users WHERE email=$1", [
      email,
    ]);

    if (user.rows.length === 0) {
      return next(
        new AppError(
          "Er is een fout opgetreden bij het verwerken van je verzoek",
          400,
        ),
      );
    }

    if (!user.rows[0].receive_email) {
      return next(
        new AppError(
          "Er is een fout opgetreden bij het verwerken van je verzoek",
          400,
        ),
      );
    }

    const isValidToken = await verifyUnsubscribeToken(
      email.toString(),
      token.toString(),
    );

    if (!isValidToken) {
      return next(new AppError("Invalid or expired unsubscribe token", 401));
    }

    await pool.query<User>("UPDATE users SET receive_email=$1 WHERE email=$2", [
      false,
      email,
    ]);

    await clearUnsubscribeToken(email.toString());

    res.status(200).json({
      success: true,
      message: "Je hebt je succesvol uitgeschreven",
    });
  } catch (error: any) {
    next(
      new AppError(
        "Er is een fout opgetreden bij het verwerken van je verzoek",
        500,
      ),
    );
  }
};

export const updateKvk = async (req: Request, res: Response) => {
  try {
    if (req.user == null) {
      return;
    }
    const user = req.user as User;
    await pool.query<User>(
      "UPDATE users SET kvk=$1 WHERE email=$2 RETURNING *",
      [req.body.kvk, user.email],
    );

    res.status(204).json({
      status: "success",
      updated: true,
    });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};

export const updateCompanyName = async (req: Request, res: Response) => {
  try {
    if (req.user == null) {
      return;
    }
    const user = req.user as User;
    await stripe.customers.update(user.stripe_customer_id, {
      name: req.body.company,
    });
    await pool.query("UPDATE users SET company=$1 WHERE email=$2 RETURNING *", [
      req.body.company,
      user.email,
    ]);

    res.status(204).json({
      status: "success",
      updated: true,
    });
  } catch (error: any) {
    res.status(500).json({ error: error.message });
  }
};
