import axios from "axios";
import React, { ChangeEvent, FormEvent, useState } from "react";
import { useHistory } from "react-router-dom";
import { toast } from "react-toastify";
import Modal from "../components/Modal";
import TextInput from "../components/TextInput";

interface CheckEmailResponse {
  exists: boolean;
  isSubscribed: boolean;
  message: string;
}

interface UnsubscribeResponse {
  success: boolean;
  message: string;
}

const Unsubscribe = () => {
  const history = useHistory();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
  });
  const [showEmailField, setShowEmailField] = useState(false);
  const [formErrors, setFormErrors] = useState({
    email: "",
  });

  const handleUnsubscribeClick = async () => {
    const urlParams = new URLSearchParams(window.location.search);
    const emailFromQuery = urlParams.get("email");
    const token = urlParams.get("token");

    if (emailFromQuery && token) {
      try {
        const res = await axios.get<UnsubscribeResponse>(
          `/api/v1/users/unsubscribe?email=${emailFromQuery}&token=${token}`,
        );
        if (res.data.success) {
          toast.success(res.data.message);
          history.push("/unsubscribe-confirm");
        } else {
          toast.error(res.data.message);
          setShowEmailField(true);
          setIsModalOpen(true);
        }
      } catch (error: any) {
        const message =
          error.response?.data?.message || "Er is een fout opgetreden";
        toast.error(message);
        setShowEmailField(true);
        setIsModalOpen(true);
      }
    } else {
      toast.error(
        "Ongeldige uitschrijflink. Controleer je e-mail voor een geldige link.",
      );
    }
  };

  const handleFormChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    setFormErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const validateForm = (): boolean => {
    const errors = {
      email: "",
    };
    let isValid = true;

    if (!formData.email) {
      errors.email = "E-mailadres is verplicht";
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get("token");

    if (!token) {
      toast.error(
        "Invalid unsubscribe link. Please check your email for a valid link.",
      );
      return;
    }

    try {
      const res = await axios.get<UnsubscribeResponse>(
        `/api/v1/users/unsubscribe?email=${formData.email}&token=${token}`,
      );
      const { success, message } = res.data;
      if (success) {
        toast.success(message);
        setIsModalOpen(false);
        history.push("/unsubscribe-confirm");
      }
    } catch (error: any) {
      const message =
        error.response?.data?.message ||
        "Er is een fout opgetreden bij het verwerken van je verzoek";
      toast.error(message);
    }
  };

  return (
    <div className="container mx-auto px-4">
      <div className="max-w-2xl mx-auto my-16 text-center">
        <h1 className="text-3xl font-bold mb-6">Nieuwsbrief Uitschrijven</h1>
        <p className="text-gray-600 mb-8">
          Je staat op het punt je uit te schrijven voor onze nieuwsbrief. Als je
          doorgaat, ontvang je geen updates en meldingen meer.
        </p>
        <button
          onClick={handleUnsubscribeClick}
          className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-dark transition-colors"
        >
          Uitschrijven
        </button>

        <Modal trigger={isModalOpen} setTrigger={setIsModalOpen}>
          <div className="text-center">
            <h2 className="text-2xl font-semibold mb-6">
              Bevestig E-mailadres
            </h2>
            <form onSubmit={handleSubmit} className="space-y-6">
              <TextInput
                type="email"
                name="email"
                placeholder="Voer je e-mailadres in"
                value={formData.email}
                onChange={handleFormChange}
                message={formErrors.email}
              />
              <div className="flex justify-end space-x-4 mt-8">
                <button
                  type="button"
                  onClick={() => setIsModalOpen(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Annuleren
                </button>
                <button
                  type="submit"
                  className="bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-dark transition-colors"
                >
                  Bevestig Uitschrijving
                </button>
              </div>
            </form>
          </div>
        </Modal>
      </div>
    </div>
  );
};

export default Unsubscribe;
