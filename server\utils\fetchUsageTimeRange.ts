import { GraphQLClient } from "graphql-request";
import AppError from "./appError";

export const fetchUsageByTime = async (
  apiKey: string,
  startDate: string,
  endDate: string,
) => {
  try {
    const keyLength = apiKey.length;
    const last6 = apiKey.slice(keyLength - 6);
    const hidden = "*".repeat(keyLength - 6);
    const hiddenApiKey = `${hidden}${last6}`;
    const query = `
    {
      actor {
        account(id: ${process.env.GRAPHQL_ID}) {
          nrql(
            query: "SELECT count(*) FROM Log WHERE apiKey='${hiddenApiKey}' SINCE '${startDate}' UNTIL '${endDate}'") {
            results
          }
        }
      }
    }
  `;

    const client = new GraphQLClient("https://api.eu.newrelic.com/graphql", {
      headers: { "api-key": `${process.env.GRAPHQL_APIKEY}` },
    });

    return await client
      .request(query)
      .then((data) => data.actor.account.nrql.results[0].count)
      .catch((error) => {
        console.log(error);
        new AppError("unable to fetch from new relic", 400);
      });
  } catch (err) {
    console.log(err);
    return new AppError("unable to fetch from new relic", 400);
  }
};
