interface DescriptionSection {
  type: "heading" | "text";
  content: string;
}

interface ParsedDescription {
  sections: DescriptionSection[];
}
export function parseAiGeneratedText(text: string): ParsedDescription {
  const lines = text.split("\n").map((line) => line.trim());
  const sections: DescriptionSection[] = [];
  let currentText = "";

  lines.forEach((line) => {
    if (line.startsWith("**") && line.endsWith("**")) {
      if (currentText) {
        sections.push({ type: "text", content: currentText.trim() });
        currentText = "";
      }
      sections.push({
        type: "heading",
        content: line.replace(/\*\*/g, "").trim(),
      });
    } else {
      currentText += line + "\n";
    }
  });

  if (currentText) {
    sections.push({ type: "text", content: currentText.trim() });
  }

  return { sections };
}
