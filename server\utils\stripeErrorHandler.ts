import Stripe from "stripe";
import AppError from "./appError";

const stripeErrorHandler = (e: Stripe.StripeRawError) => {
  switch (e.type) {
    case "card_error":
      return new AppError(
        `A payment error occurred: ${e.message}`,
        e.statusCode || 400,
      );
    case "invalid_request_error":
      return new AppError(
        `An invalid request occurred: ${e.message}`,
        e.statusCode || 400,
      );

    case "authentication_error":
      return new AppError(
        `An authentication error occurred: ${e.message}`,
        e.statusCode || 400,
      );
    case "rate_limit_error":
      return new AppError(
        `Rate limit error error occurred: ${e.message}`,
        e.statusCode || 400,
      );
    default:
      return new AppError(
        `Another problem occurred, maybe unrelated to Stripe: ${e.message}`,
        e.statusCode || 400,
      );
  }
};
export default stripeErrorHandler;
