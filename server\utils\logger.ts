import { createLogger, format, transports } from "winston";

const { combine, timestamp, printf } = format;
const logFormat = combine(
  timestamp(),
  printf(
    ({ timestamp, level, message }) =>
      `${timestamp} [${level.toUpperCase()}]: ${message}`,
  ),
);

const logger = createLogger({
  transports: [
    new transports.Console({ level: "debug" }),
    new transports.File({ filename: "../logs/app.log", level: "debug" }),
    new transports.File({ filename: "../logs/errors.log", level: "error" }),
  ],
  format: logFormat,
});

export default logger;
