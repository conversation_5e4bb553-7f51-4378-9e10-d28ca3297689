/** @type {import('tailwindcss').Config} */

require("tailwindcss/colors");
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "node_modules/flowbite-react/lib/esm/**/*.js",
  ],
  theme: {
    colors: {
      primary: "#27AE60",
      secondary: "#F0FFF9",
      "gray-light": "#828282",
      "gray-dark": "#333333",
      "accent-blue": "#0888ff",
      "label-a-4": "rgba(0, 166, 82, 1)",
      "label-a-3": "rgba(0, 166, 82, 1)",
      "label-a-2": "rgba(0, 166, 82, 1)",
      "label-a-1": "rgba(0, 166, 82, 1)",
      "label-a": "rgba(0, 166, 82, 1)",
      "label-b": "rgba(138, 199, 62, 1)",
      "label-c": "rgba(189, 214, 48, 1)",
      "label-d": "rgba(220, 179, 39, 1)",
      "label-e": "rgba(247, 148, 29, 1)",
      "label-f": "rgba(241, 90, 43, 1)",
      "label-g": "rgba(238, 28, 37, 1)",
    },
    extend: {},
  },
  plugins: [require("flowbite/plugin")],
};
