import crypto from "crypto";
import pool from "../db";
import { User } from "../@types";

// Generate a secure random token for unsubscribe links
export const generateUnsubscribeToken = async (
  email: string,
): Promise<string> => {
  const token = crypto.randomBytes(32).toString("hex");
  const expiry = new Date();
  expiry.setDate(expiry.getDate() + 30); // Token valid for 30 days

  await pool.query(
    "UPDATE users SET unsubscribe_token = $1, unsubscribe_token_expiry = $2 WHERE email = $3",
    [token, expiry, email],
  );

  return token;
};

// Verify an unsubscribe token is valid
export const verifyUnsubscribeToken = async (
  email: string,
  token: string,
): Promise<boolean> => {
  const result = await pool.query<User>(
    "SELECT * FROM users WHERE email = $1 AND unsubscribe_token = $2 AND unsubscribe_token_expiry > NOW()",
    [email, token],
  );

  return result.rows.length > 0;
};

// Clear the unsubscribe token after use
export const clearUnsubscribeToken = async (email: string): Promise<void> => {
  await pool.query(
    "UPDATE users SET unsubscribe_token = NULL, unsubscribe_token_expiry = NULL WHERE email = $1",
    [email],
  );
};
