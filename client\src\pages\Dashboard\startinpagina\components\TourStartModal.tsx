import React from "react";

interface TourStartModalProps {
  isOpen: boolean;
  onClose: () => void;
  onStartTour: () => void;
}

const TourStartModal: React.FC<TourStartModalProps> = ({
  isOpen,
  onClose,
  onStartTour,
}) => {
  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      {/* Adjusted modal width and max-width */}
      <div className="bg-white rounded-lg shadow-xl p-6 md:p-8 w-full max-w-sm md:max-w-[630px] text-center relative">
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-700 text-2xl md:top-4 md:right-4"
          aria-label="Close modal"
        >
          &times;
        </button>

        {/* Adjusted image size for mobile and desktop */}
        <img
          src="/images/festivities.svg"
          alt="Festivities illustration"
          className="mx-auto mb-6 w-56 h-auto md:w-[280px] md:h-[303px]"
        />

        <h2 className="text-xl md:text-2xl font-semibold text-gray-800 mb-3">
          Welkom bij Altum AI
        </h2>
        <p className="text-sm md:text-base text-gray-600 mb-6">
          Je bent bijna klaar – we hebben alvast{" "}
          <strong>15 gratis credits</strong> aan je account toegevoegd om je op
          weg te helpen met het ontdekken van nauwkeurige woningdata. Volg een
          korte rondleiding van minder dan een minuut
        </p>

        <button
          onClick={onStartTour}
          className="w-full bg-primary hover:bg-primary-dark text-white font-semibold py-3 px-4 rounded-lg text-base md:text-lg mb-3 transition duration-150 ease-in-out"
        >
          Rondleiding starten
        </button>
        <button
          onClick={onClose}
          className="w-full text-primary hover:underline text-sm md:text-base font-medium"
        >
          Rondleiding overslaan
        </button>
      </div>
    </div>
  );
};

export default TourStartModal;
