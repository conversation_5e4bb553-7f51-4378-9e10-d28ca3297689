import pool from "../../db";
import { AnalyticsLoggingService } from "./analyticsLoggingService";

interface ChartData {
  dates: string[];
  values: number[];
  total: number;
  change: {
    value: number;
    percentage: number;
  };
}

interface AnalyticsMetrics {
  status: string;
  activeUsers: {
    count: number;
    growth: number;
  };
  newUsers: {
    count: number;
    growth: number;
  };
  propertyGenerations: {
    count: number;
    growth: number;
  };
  onboardingFunnel: {
    signups: number;
    emailVerifications: {
      count: number;
      rate: number;
    };
    onboardingCompletes: {
      count: number;
      rate: number;
    };
    firstApiCalls: {
      count: number;
      rate: number;
    };
  };
}

export class AnalyticsService {
  static async getMopsusAnalytics(
    timeframe: string = "30d",
  ): Promise<AnalyticsMetrics> {
    const [activeUsers, newUsers, propertyGenerations, onboardingFunnel] =
      await Promise.all([
        AnalyticsLoggingService.getActiveUsersMetrics(timeframe),
        AnalyticsLoggingService.getNewUsersMetrics(timeframe),
        AnalyticsLoggingService.getPropertyGenerationMetrics(timeframe),
        AnalyticsLoggingService.getOnboardingFunnelMetrics(timeframe),
      ]);

    const response: AnalyticsMetrics = {
      status: "success",
      activeUsers: {
        count: activeUsers.current_active_users,
        growth: activeUsers.growth_percentage,
      },
      newUsers: {
        count: newUsers.new_users_today,
        growth: newUsers.growth_percentage,
      },
      propertyGenerations: {
        count: propertyGenerations.total_generations,
        growth: propertyGenerations.growth_percentage,
      },
      onboardingFunnel: {
        signups: onboardingFunnel.signups,
        emailVerifications: {
          count: onboardingFunnel.email_verifications,
          rate: onboardingFunnel.email_verification_rate,
        },
        onboardingCompletes: {
          count: onboardingFunnel.onboarding_completes,
          rate: onboardingFunnel.onboarding_completion_rate,
        },
        firstApiCalls: {
          count: onboardingFunnel.first_api_calls,
          rate: onboardingFunnel.api_conversion_rate,
        },
      },
    };

    return response;
  }

  private static getMetricsSummary(
    currentPeriodTotal: number,
    previousPeriodTotal: number,
  ) {
    const change = currentPeriodTotal - previousPeriodTotal;
    let percentageChange: number;

    if (previousPeriodTotal === 0) {
      percentageChange = currentPeriodTotal === 0 ? 0 : 100;
    } else {
      percentageChange = (change / previousPeriodTotal) * 100;
    }

    return {
      total: currentPeriodTotal,
      change: {
        value: change,
        percentage: Math.round(percentageChange * 100) / 100,
      },
    };
  }

  private static getDateRanges(
    start: string | undefined,
    end: string | undefined,
  ) {
    let startDate = start
      ? new Date(start)
      : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    let endDate = end ? new Date(end) : new Date();

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      endDate = new Date();
    }

    // Calculate previous period
    const periodLength = endDate.getTime() - startDate.getTime();
    const previousStartDate = new Date(startDate.getTime() - periodLength);
    const previousEndDate = new Date(startDate.getTime() - 1); // Day before start

    return {
      startDate,
      endDate,
      previousStartDate,
      previousEndDate,
    };
  }

  static async getActiveUsersLineChart(
    start: string,
    end: string,
  ): Promise<{ status: string; data: ChartData }> {
    const { startDate, endDate } = this.getDateRanges(start, end);

    const query = `
      WITH RECURSIVE dates AS (
        SELECT $1::date AS date
        UNION ALL
        SELECT date + 1
        FROM dates
        WHERE date < $2::date
      ),
      point_in_time_counts AS (
        SELECT 
          d.date,
          COUNT(*) FILTER (WHERE created_at <= d.date AND active = true) as active_count
        FROM dates d
        CROSS JOIN users
        GROUP BY d.date
      ),
      current_total AS (
        SELECT active_count as count
        FROM point_in_time_counts
        WHERE date = $2::date
      ),
      previous_total AS (
        SELECT active_count as count
        FROM point_in_time_counts
        WHERE date = $1::date
      )
      SELECT
        d.date::date,
        COALESCE(p.active_count, 0) as count,
        COALESCE((SELECT count FROM current_total), 0) as current_total,
        COALESCE((SELECT count FROM previous_total), 0) as previous_total
      FROM dates d
      LEFT JOIN point_in_time_counts p ON d.date = p.date
      ORDER BY d.date;
    `;

    const result = await pool.query(query, [
      startDate.toISOString().split("T")[0],
      endDate.toISOString().split("T")[0],
    ]);

    const currentTotal = parseInt(result.rows[0]?.current_total) || 0;
    const previousTotal = parseInt(result.rows[0]?.previous_total) || 0;

    return {
      status: "success",
      data: {
        dates: result.rows.map((row) => row.date),
        values: result.rows.map((row) => parseInt(row.count)),
        ...this.getMetricsSummary(currentTotal, previousTotal),
      },
    };
  }

  static async getNewUsersBarChart(
    start: string,
    end: string,
  ): Promise<{ status: string; data: ChartData }> {
    const { startDate, endDate, previousStartDate, previousEndDate } =
      this.getDateRanges(start, end);

    const query = `
      WITH RECURSIVE dates AS (
        SELECT $1::date AS date
        UNION ALL
        SELECT date + 1
        FROM dates
        WHERE date < $2::date
      ),
      all_data AS (
        SELECT 
          date,
          COUNT(*) as daily_count
        FROM analytics_logger
        WHERE date BETWEEN $3::date AND $2::date
        AND category = 'Authentication'
        AND event IN ('Signup', 'Signup-with-google', 'Signup-with-Linkedin')
        GROUP BY date
      ),
      period_totals AS (
        SELECT 
          SUM(CASE WHEN date >= $1::date THEN daily_count ELSE 0 END) as current_total,
          SUM(CASE WHEN date < $1::date THEN daily_count ELSE 0 END) as previous_total
        FROM all_data
      )
      SELECT
        d.date::date,
        COALESCE(a.daily_count, 0) as count,
        COALESCE((SELECT current_total FROM period_totals), 0) as current_total,
        COALESCE((SELECT previous_total FROM period_totals), 0) as previous_total
      FROM dates d
      LEFT JOIN all_data a ON d.date = a.date
      WHERE d.date >= $1::date
      ORDER BY d.date;
    `;

    const result = await pool.query(query, [
      startDate.toISOString().split("T")[0],
      endDate.toISOString().split("T")[0],
      previousStartDate.toISOString().split("T")[0],
    ]);

    const currentTotal = parseInt(result.rows[0]?.current_total) || 0;
    const previousTotal = parseInt(result.rows[0]?.previous_total) || 0;

    return {
      status: "success",
      data: {
        dates: result.rows.map((row) => row.date),
        values: result.rows.map((row) => parseInt(row.count)),
        ...this.getMetricsSummary(currentTotal, previousTotal),
      },
    };
  }

  static async getPropertyGenerationsBarChart(
    start: string,
    end: string,
  ): Promise<{ status: string; data: ChartData }> {
    const { startDate, endDate, previousStartDate, previousEndDate } =
      this.getDateRanges(start, end);

    const query = `
      WITH RECURSIVE dates AS (
        SELECT $1::date AS date
        UNION ALL
        SELECT date + 1
        FROM dates
        WHERE date < $2::date
      ),
      all_data AS (
        SELECT 
          date,
          COUNT(*) as daily_count
        FROM analytics_logger
        WHERE date BETWEEN $3::date AND $2::date
        AND category = 'Property'
        AND event = 'PropertyGenerated'
        GROUP BY date
      ),
      period_totals AS (
        SELECT 
          SUM(CASE WHEN date >= $1::date THEN daily_count ELSE 0 END) as current_total,
          SUM(CASE WHEN date < $1::date THEN daily_count ELSE 0 END) as previous_total
        FROM all_data
      )
      SELECT
        d.date::date,
        COALESCE(a.daily_count, 0) as count,
        COALESCE((SELECT current_total FROM period_totals), 0) as current_total,
        COALESCE((SELECT previous_total FROM period_totals), 0) as previous_total
      FROM dates d
      LEFT JOIN all_data a ON d.date = a.date
      WHERE d.date >= $1::date
      ORDER BY d.date;
    `;

    const result = await pool.query(query, [
      startDate.toISOString().split("T")[0],
      endDate.toISOString().split("T")[0],
      previousStartDate.toISOString().split("T")[0],
    ]);

    const currentTotal = parseInt(result.rows[0]?.current_total) || 0;
    const previousTotal = parseInt(result.rows[0]?.previous_total) || 0;

    return {
      status: "success",
      data: {
        dates: result.rows.map((row) => row.date),
        values: result.rows.map((row) => parseInt(row.count)),
        ...this.getMetricsSummary(currentTotal, previousTotal),
      },
    };
  }
}
