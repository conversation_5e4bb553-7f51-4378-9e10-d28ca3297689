import express from "express";
import { body, query } from "express-validator";
import {
  errorCheck,
  logout,
  forgotPassword,
  checkValidResetPasswordToken,
  resetPassword,
  signup,
  signIn,
  protect,
  loadUser,
  verifyAccount,
  sendVerificationEmail,
  allowSignupConfirmation,
  loginLimiter,
  passwordResetLimiter,
  verify2FA,
} from "../controllers/authController";

import {
  changePassword,
  deleteAccountDetails,
  deleteAccount,
  unsubscribeToEmail,
  checkEmailExists,
  updateKvk,
  updateCompanyName,
} from "../controllers/userController";

import { getVatId, createVatId } from "../controllers/customerPortalController";

const router = express.Router();

// Sign up
router
  .route("/signup")
  .post(
    [
      body("first_name"),
      body("last_name"),
      body("email").isEmail().not().isEmpty(),
      body("password")
        .not()
        .isEmpty()
        .isLength({ min: 6 })
        .withMessage("must be at least 6 characters long"),
    ],
    errorCheck,
    signup,
  );

// Sign in
router
  .route("/signin")
  .post(
    [
      body("email").isEmail().not().isEmpty(),
      body("password")
        .not()
        .isEmpty()
        .isLength({ min: 8 })
        .withMessage("must be at least 8 characters long"),
    ],
    loginLimiter,
    errorCheck,
    signIn,
  );

// Logout
router.get("/logout", logout);

// Forgot Password
router.post(
  "/forgotPassword",
  [body("email").isEmail().not().isEmpty()],
  passwordResetLimiter,
  errorCheck,
  forgotPassword,
);

// Check if reset token is valid or not
router.get("/resetPassword/:resetToken", checkValidResetPasswordToken);

// Reset Password
router.patch(
  "/resetPassword/:resetToken",
  [
    body("password").not().isEmpty().isLength({ min: 8 }),
    body("passwordConfirm").not().isEmpty().isLength({ min: 8 }),
  ],
  passwordResetLimiter,
  errorCheck,
  resetPassword,
);

// Load User (Protected Route)
router.get("/", protect, loadUser);

// Verify Account
router.post(
  "/verify-email",
  [
    body("email").isEmail().not().isEmpty(),
    body("code").not().isEmpty().isLength({ min: 6 }),
  ],
  errorCheck,
  verifyAccount,
);

// Resend account verification email
router.post(
  "/send-verification-email",
  [body("email").isEmail().not().isEmpty()],
  errorCheck,
  sendVerificationEmail,
);

// Allow signup confirmation
router.get("/allow-signup-confirmation/:id", allowSignupConfirmation);

// Account specific routes (LOGGED IN)
router.patch(
  "/account/change-password",
  [
    body("oldPassword").not().isEmpty().isLength({ min: 8 }),
    body("newPassword").not().isEmpty().isLength({ min: 8 }),
    body("confirmPassword").not().isEmpty().isLength({ min: 8 }),
  ],
  errorCheck,
  protect,
  changePassword,
);

router.post(
  "/account/delete-account/:id",
  [body("password").not().isEmpty().isLength({ min: 8 })],
  errorCheck,
  protect,
  deleteAccountDetails,
);

router.delete("/account/delete-account/:id", protect, deleteAccount);

router.get(
  "/check-email",
  [query("email").isEmail().not().isEmpty()],
  errorCheck,
  checkEmailExists,
);

router.get(
  "/unsubscribe",
  [query("email").isEmail().not().isEmpty(), query("token").not().isEmpty()],
  errorCheck,
  unsubscribeToEmail,
);

router.post(
  "/kvk",
  [body("kvk").not().isEmpty()],
  errorCheck,
  protect,
  updateKvk,
);
router.post(
  "/company",
  [body("company").not().isEmpty()],
  errorCheck,
  protect,
  updateCompanyName,
);

router.post(
  "/stripe",
  [body("company").not().isEmpty()],
  errorCheck,
  protect,
  updateCompanyName,
);
router.post(
  "/vat",
  [body("vat").not().isEmpty()],
  errorCheck,
  protect,
  createVatId,
);
router.get("/vat", protect, getVatId);

/*
    This route completes the authentication sequence when Google redirects the
    user back to the application.  When a new user signs in, a user account is
    automatically created and their Google account is linked.  When an existing
    user returns, they are signed in to their linked account.
*/
// router.get(
// 	"/redirect/google",
// 	passport.authenticate("google", {
// 		successReturnToOrRedirect: "/",
// 		failureRedirect: "/login",
// 	})
// );

export default router;
