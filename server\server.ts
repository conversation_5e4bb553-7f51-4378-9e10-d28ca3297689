// Set up path and config file
import http from "http";
import "dotenv/config";
import logger from "./utils/logger";

// Import app
import app from "./app";

// GLOBAL HANDLER FOR UNCAUGHT EXCEPTIONS - SYNC
process.on("uncaughtException", (err) => {
  logger.error(err.stack);
  logger.error("Uncaught Exception. Shutting down...");
  process.exit(1);
});
const server = http.createServer(app);

// Set up server to listen on
const PORT = process.env.PORT || 5000;
server.listen(PORT, () => {
  logger.info(`Server is running on ${PORT}`);
});

// GLOBAL HANDLER FOR UNHANDLED REJECTED PROMISES - ASYNC
process.on("unhandledRejection", (err: any) => {
  logger.error(err.stack);
  logger.error("Unhandled Rejection. Shutting down...");
  server.close(() => {
    process.exit(1);
  });
});
