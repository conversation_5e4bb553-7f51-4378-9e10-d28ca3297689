const checkIfStorageExist: boolean = typeof window !== "undefined";

export const saveItemToLocalStorage = (key: string, value: string): void => {
  if (checkIfStorageExist) return window.localStorage.setItem(key, value);
};

export const getItemFromStorage = (key: string): string | null => {
  if (checkIfStorageExist) return window.localStorage.getItem(key);
  return null;
};

export const clearItems = () => {
  localStorage.clear();
};
