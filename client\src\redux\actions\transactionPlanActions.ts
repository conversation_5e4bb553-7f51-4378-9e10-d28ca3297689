import axios from "axios";
import { toast } from "react-toastify";
import { createAsyncThunk } from "@reduxjs/toolkit";
import {
	CancelSubscriptionPayload,
	CreateSubscriptionPayload,
} from "../../@types";
import { AppDispatch } from "../store";
import { clearApiUsageState } from "./apiUsage";
import { clearStripeState } from "./stripeActions";

// Subscriptions

export const createTransactionSubscription = createAsyncThunk(
	"transactionSub/createSub",
	async (
		{
			body,
			dispatch,
		}: { body: CreateSubscriptionPayload; dispatch: AppDispatch },
		{ rejectWithValue }
	) => {
		try {
			const res = await axios.post(
				"/api/v1/mopsus/create-transaction-subscription",
				body,
				{ headers: { "Content-Type": "application/json" } }
			);

			if (res.status === 200) {
				toast.success("Abonnement succesvol geactiveerd");
				dispatch(clearApiUsageState());
				dispatch(clearStripeState());
				return true;
			}
			throw new Error();
		} catch (error: any) {
			toast.error(error.response?.data.error.message);
			rejectWithValue(error.response?.data.error.message);
			return false;
		}
	}
);

export const updateTransactionSubscription = createAsyncThunk(
	"transactionSub/updateSub",
	async (
		{
			body,
			dispatch,
		}: { body: CreateSubscriptionPayload; dispatch: AppDispatch },
		{ rejectWithValue }
	) => {
		try {
			await axios.post("/api/v1/mopsus/update-transaction-subscription", body, {
				headers: { "Content-Type": "application/json" },
			});
			toast.success("Abonnement Bijgewerkt");
			dispatch(clearApiUsageState());
			dispatch(clearStripeState());
			return true;
		} catch (error: any) {
			// toast.error(error.response?.data.error.message);
			rejectWithValue(error.response?.data.error.message);
			return false;
		}
	}
);

export const cancelTransactionSubscription = createAsyncThunk(
	"transactionSub/cancelSub",
	async (body: CancelSubscriptionPayload, { rejectWithValue }) => {
		try {
			const res = await axios.post(
				"/api/v1/mopsus/cancel-transaction-subscription",
				body,
				{ headers: { "Content-Type": "application/json" } }
			);

			if (res.status === 200) {
				toast.success("Abonnement Opgezegd");
				return true;
			}
			throw new Error();
		} catch (error: any) {
			// toast.error(error.response?.data.error.message);
			rejectWithValue(error.response?.data.error.message);
			return false;
		}
	}
);
