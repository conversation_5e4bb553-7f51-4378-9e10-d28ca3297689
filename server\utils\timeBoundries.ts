export default function getTimeBoundaries() {
  // Get the current date
  const now = new Date();

  // Calculate the difference between the current day of the week and the beginning of the week (assumed to be Sunday)
  const diffToThisWeekBeginning = now.getDay();
  const diffToLastWeekBeginning = now.getDay() + 7;
  const diffToYesterdayBeginning = 1;

  // Get the beginning of this week
  const thisWeekBeginning = new Date(
    new Date(
      new Date(now).setDate(now.getDate() - diffToThisWeekBeginning),
    ).setHours(0, 0, 0, 0),
  )
    .toISOString()
    .replace("T", " ")
    .replace(".000Z", "");

  // Get the beginning of last week
  const lastWeekBeginning = new Date(
    new Date(
      new Date(now).setDate(now.getDate() - diffToLastWeekBeginning),
    ).setHours(0, 0, 0, 0),
  )
    .toISOString()
    .replace("T", " ")
    .replace(".000Z", "");

  // Get the beginning of yesterday
  const yesterdayBeginning = new Date(
    new Date(
      new Date(now).setDate(now.getDate() - diffToYesterdayBeginning),
    ).setHours(0, 0, 0, 0),
  )
    .toISOString()
    .replace("T", " ")
    .replace(".000Z", "");

  // Get the beginning of today
  const todayBeginning = new Date(new Date(now).setHours(0, 0, 0, 0))
    .toISOString()
    .replace("T", " ")
    .replace(".000Z", "");

  const rightNow = new Date(now)
    .toISOString()
    .replace("T", " ")
    .replace(/[.][0-9]{3}[Z]$/i, "");

  return {
    lastWeekBeginning,
    thisWeekBeginning,
    yesterdayBeginning,
    todayBeginning,
    rightNow,
  };
}
