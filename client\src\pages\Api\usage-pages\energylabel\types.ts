// Define the energy label enum
export enum EnergyLabel {
  "A++++" = "A++++",
  "A+++" = "A+++",
  "A++" = "A++",
  "A+" = "A+",
  A = "A",
  B = "B",
  C = "C",
  D = "D",
  E = "E",
  F = "F",
  G = "G",
}

export const ENERGY_RATINGS = [
  { label: EnergyLabel.G, color: "#E1261C", value: 350 },
  { label: EnergyLabel.F, color: "#E63323", value: 335 },
  { label: EnergyLabel.E, color: "#F9A11B", value: 290 },
  { label: EnergyLabel.D, color: "#FECC00", value: 250 },
  { label: EnergyLabel.C, color: "#B0D136", value: 190 },
  { label: EnergyLabel.B, color: "#7FB239", value: 160 },
  { label: EnergyLabel.A, color: "#4CAF50", value: 105 },
  { label: EnergyLabel["A+"], color: "#388E3C", value: 75 },
  { label: EnergyLabel["A++"], color: "#2E7D32", value: 50 },
  { label: EnergyLabel["A+++"], color: "#1B5E20", value: 30 },
  { label: EnergyLabel["A++++"], color: "#0A3D00", value: 0 },
] as const;

export interface EnergyLabelData {
  post_code: string;
  house_number: number;
  house_addition: string | null;
  build_year: number;
  inner_surface_area: number;
  house_type: string;
  installation: number;
  wall_insulation: number;
  roof_insulation: number;
  floor_insulation: number;
  living_room_windows: number;
  ventilation: number;
  bedroom_windows: number;
  shower: number;
  solar_panels: number;
  solarpanel_watt_peak: number;
  CO2: number;
  definitive_energy_label: EnergyLabel;
  definitive_energy_label_type: string;
  definitive_energy_label_validity_date: string;
  current_estimated_energy_label: EnergyLabel;
  current_estimated_BENG2_score: number;
  estimated_gas_usage: number;
  estimated_energy_usage: number;
}

export const isValidEnergyLabel = (label: string): label is EnergyLabel => {
  return Object.values(EnergyLabel).includes(label as EnergyLabel);
};
