.signup{
    margin-top: 2rem;
    margin-bottom: 4rem;
}
.signup__container{
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
    width: 90%;
    margin: auto;  
}
.signup__info{
    flex: 1 1;
}
.signup__info h2{
    line-height: 140%;
    color: var(--dark-color);  
}
.signup__info ul{
    margin: 1rem auto;
}
.signup__info ul li {
    display: flex;
    align-items: center;
}


.signup__info ul li{
    font-size: 1.125rem;
}
.signup__info ul li svg{
    margin-right: 0.5rem;
    color: var(--primary-color);
}
.signup__illustration{
    background: url('../../imgs/data.png') no-repeat;
    /* background-size: cover; */
    background-size: contain;
    min-height: 250px;
    height: 45vh;
    max-height: 280px;
    background-position: center;
}
.signup-form__container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    max-width: 375px;
    min-width: 300px;
}
.clients__slider--signup-container{
    background-color: var(--light-color);
    padding: 1rem;
}
.clients__header--signup{
    text-align: center;
    width: 100%;

}
.clients__slider--signup{
    width: 50%!important;
    /* background-color: var(--light-color); */
}

.signup-confirm__container{
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
    width: 80%;
    margin: 0 auto;
    margin-top: 4rem;
    margin-bottom: 4rem;
}

.signup-confirm__container--title{
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.signup-confirm__container--tip{
    margin: 0.8rem 0 1.5rem 0;
    font-size: 15px;
}

.signup-confirm__container--tip span{
    font-weight: bold;
}

.redirect-btn {
    display: block;
    margin: 0 auto;
}

@media screen and (min-width: 768px){
    .signup__info h2{
        
        margin-left: 1rem;
    }
    .signup__info ul{
        margin: 1rem 0 1rem 1rem;
    }
    .signup__info{
        flex: 0.45 1;
    }
    .signup{
        margin-top: 4rem;
    }
    .signup__container{
        max-width: 1200px;
        flex-direction: row;
        margin: 0 auto;
        justify-content: space-evenly;
    }
    .signup-form__container{
        flex: 0.45;
    }
    .signup__illustration{
        flex: 0.45;
        background-position: left;
        background-size: cover;
        /* margin-left: 3rem; */
        /* max-width: 350px; */
    }
}