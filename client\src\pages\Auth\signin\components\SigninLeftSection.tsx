import {
  ChangeEvent,
  FormEvent,
  useCallback,
  useEffect,
  useRef,
  useState,
} from "react";
import { <PERSON><PERSON>Lock, CiMail } from "react-icons/ci";
import { Fa<PERSON>eg<PERSON>ye, FaRegEyeSlash } from "react-icons/fa6";
import { Link, useHistory, useLocation } from "react-router-dom";
import TextInput from "../../../../components/TextInput";
import DividerWithText from "../../../../components/DividerWithText";
import Social from "../../components/Social";
import AuthForm from "../../components/AuthForm";
import Text from "../../../../components/Text";
import { useAppDispatch, useAppSelector } from "../../../../redux/hooks";
import { signIn } from "../../../../redux/actions/authActions";
import { set2FAState } from "../../../../redux/features/authSlice";
import { useValidation } from "../../../../hooks/useValidator";
import TwoFactorVerify from "./TwoFactorVerify";
import AltumLogo from "../../../../assets/images/Logo-AltumAI.png";

interface AuthFormData {
  email: string;
  password: string;
}

const SigninLeftSection = () => {
  const history = useHistory();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const { isAuthenticated, loading, requires2FA, userId, twoFactorType } =
    useAppSelector((state) => state.auth);
  const [formData, setFormData] = useState<AuthFormData>({
    email: "",
    password: "",
  });
  const [touchedFields, setTouchedFields] = useState<{
    [key: string]: boolean;
  }>({});
  const { errors, validate } = useValidation();
  const [showPassword, setShowPassword] = useState(false);

  // Use a ref to track redirect attempts and prevent multiple redirects
  const redirectAttempted = useRef(false);

  useEffect(() => {
    // Using localStorage instead of sessionStorage for more persistent tracking
    const hasRedirected =
      localStorage.getItem("dashboardRedirected") === "true";

    // Only redirect if:
    // 1. User is authenticated
    // 2. 2FA is not required
    // 3. Loading is complete
    // 4. No redirection has been attempted during this authentication session
    // 5. No redirection has already occurred (stored in localStorage)
    if (
      isAuthenticated &&
      !requires2FA &&
      !loading &&
      !redirectAttempted.current &&
      !hasRedirected
    ) {
      // Set both flags to prevent future redirects
      redirectAttempted.current = true;
      localStorage.setItem("dashboardRedirected", "true");

      console.log("Redirecting to dashboard...");

      // Use replace with a single call to prevent navigation issues
      history.replace("/dashboard/startpagina");
    }

    // Reset flags ONLY when authentication state actually changes to not authenticated
    // This prevents clearing flags during component re-renders when still authenticated
    if (!isAuthenticated && (redirectAttempted.current || hasRedirected)) {
      redirectAttempted.current = false;
      localStorage.removeItem("dashboardRedirected");
    }
  }, [isAuthenticated, requires2FA, loading, history]);

  const handleBlur = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      const { name } = e.target;
      setTouchedFields((prev) => ({ ...prev, [name]: true }));
      validate(formData);
    },
    [formData, validate],
  );

  const handleChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      const { name, value } = e.target;
      setFormData((prevData) => ({ ...prevData, [name]: value }));
      if (touchedFields[name]) {
        validate({ ...formData, [name]: value });
      }
    },
    [touchedFields, formData, validate],
  );

  const handleSubmit = useCallback(
    async (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      try {
        await dispatch(signIn({ formData, dispatch })).unwrap();
      } catch (error) {
        console.error("Login error:", error);
      }
    },
    [dispatch, formData],
  );

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const userParam = params.get("user");
    if (userParam) {
      try {
        const userData = JSON.parse(decodeURIComponent(userParam));
        if (userData.requires2FA && userData.userId && userData.type) {
          dispatch(
            set2FAState({
              requires2FA: true,
              userId: userData.userId,
              twoFactorType: userData.type,
            }),
          );
        }
      } catch (error) {
        console.error("Error parsing user data:", error);
      }
    }
  }, [location.search, dispatch]);

  const handle2FAVerified = useCallback(() => {
    // Clear any existing dashboard redirect flag
    redirectAttempted.current = false;
    localStorage.removeItem("dashboardRedirected");

    // Force immediate redirect to dashboard using a timeout to ensure state updates complete first
    const redirectTimer = setTimeout(() => {
      if (!redirectAttempted.current) {
        redirectAttempted.current = true;
        history.replace("/dashboard/startpagina");
      }
    }, 0);
    return () => clearTimeout(redirectTimer);
  }, [history]);

  if (requires2FA && userId && twoFactorType) {
    return (
      <TwoFactorVerify
        userId={userId}
        twoFactorType={twoFactorType}
        onVerifySuccess={handle2FAVerified}
      />
    );
  }

  return (
    <div className="w-full md:w-3/5 p-5 flex flex-col justify-between">
      <div className="max-w-md mx-auto w-full flex flex-col items-center">
        <img src={AltumLogo} alt="Altum AI" className="w-48 mt-8 mb-4" />

        <div className="text-center mb-4">
          <h1 className="text-2xl font-semibold text-gray-800">
            Log in op jouw account
          </h1>
          <p className="text-gray-600 mt-2 text-sm">
            Selecteer een methode om in te loggen
          </p>
        </div>

        <Social />

        <DividerWithText
          className="md:max-w-[424px] w-full my-4"
          text="of log in met e-mail"
        />

        <AuthForm
          cta="Inloggen"
          onSubmit={handleSubmit}
          isLoading={loading}
          isError={!!errors["email"] || !!errors["password"]}
        >
          <TextInput
            type="email"
            name="email"
            placeholder="E-mail"
            className="mt-3"
            icon={CiMail}
            message={touchedFields.email ? errors["email"] : ""}
            onChange={handleChange}
            onBlur={handleBlur}
          />
          <div className="relative">
            <TextInput
              type={showPassword ? "text" : "password"}
              name="password"
              placeholder="Wachtwoord (8+ tekens)"
              className="mt-3"
              icon={CiLock}
              message={touchedFields.password ? errors["password"] : ""}
              onChange={handleChange}
              onBlur={handleBlur}
            />
            <button
              type="button"
              onClick={togglePasswordVisibility}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 mt-1"
            >
              {showPassword ? <FaRegEyeSlash /> : <FaRegEye />}
            </button>
          </div>
          <Text className="text-center mt-1 text-xs">
            Wachtwoord vergeten?{" "}
            <Link
              to="/forgot-password"
              className="text-green-600 hover:underline text-xs"
            >
              Wachtwoord opnieuw instellen
            </Link>
          </Text>
        </AuthForm>

        <Text className="text-center mt-4 text-gray-600">
          Nog geen account?{" "}
          <Link to="/signup" className="text-green-600 hover:underline">
            Aanmelden
          </Link>
        </Text>
      </div>
    </div>
  );
};

export default SigninLeftSection;
