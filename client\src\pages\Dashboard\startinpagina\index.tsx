import DashboardContainer from "../components/Container";
import Header from "../components/Header";
import RecommendedAPI from "./components/RecommendedAPI";
import ActivePlanCard from "./components/ActivePlanCard";
import TotalAPICallCard from "./components/TotalAPICallCard";
import APILogs from "./components/APILogs";
import SelectInput from "../../../components/SelectInput";
import { useEffect, useMemo, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../../redux/hooks";
import calculatePercentageDifference from "../../../helpers/percentDifference";
import convertToEuFormat from "../../../helpers/convertToEuFormat";
import { getUsageLogs } from "../../../redux/actions/apiUsage";
import { knowWhen } from "../../../helpers/time";
import { isPPUPlan, isTransactionPlan } from "../../../helpers/stripeHelper";
import timeConverter from "../../../helpers/timeStampConverter";
import useIsomorphicLayoutEffect from "../../../hooks/useIsomorphicLayoutEffect";
import apiNameConverter from "../../../helpers/apiNameConverter";
import ActivePlanCardSkelecton from "./components/ActivePlanCardSkelecton";
import APILogSkeleton from "./components/APILogSkeleton";
import { Log } from "../../../@types";

interface Option {
  value: string;
  label: string;
}

const Dashboard = () => {
  const dispatch = useAppDispatch();
  const { loading: apiUsageLoading, logs: existingLogs } = useAppSelector(
    (state) => state.apiUsage.usageLogs || { loading: false, logs: [] },
  );
  const [logsFetched, setLogsFetched] = useState(false);

  useIsomorphicLayoutEffect(() => {
    // Only fetch logs once when component mounts and not already loading
    if (!logsFetched && !apiUsageLoading) {
      dispatch(getUsageLogs());
      setLogsFetched(true);
    }
    // We don't include logsFetched in deps array to prevent re-runs
  }, [dispatch, apiUsageLoading]);

  const {
    usage: {
      plan,
      totalCredits,
      used,
      transactionUsed,
      remaining,
      usageToday = 0,
      usageYesterday = 0,
      usageThisWeek = 0,
      usageLastWeek = 0,
      subscription,
      loading,
    },
    usageLogs: { loading: logsLoading, logs },
  } = useAppSelector((state) => state.apiUsage);

  const {
    comingInvoice,
    transactionComingInvoice,
    loading: porterLoading,
  } = useAppSelector((state) => state.portal);

  const options: Option[] = useMemo(
    () => [
      {
        value:
          comingInvoice?.product.name ||
          (plan === "Platform - Free Tier" ? "Altum AI - gratis account" : "-"),
        label:
          comingInvoice?.product.name ||
          (plan === "Platform - Free Tier" ? "Altum AI - gratis account" : "-"),
      },
      ...(transactionComingInvoice?.product.name
        ? [
            {
              value: transactionComingInvoice.product.name,
              label: transactionComingInvoice.product.name,
            },
          ]
        : []),
    ],
    [comingInvoice?.product.name, plan, transactionComingInvoice?.product.name],
  );
  const [currentPlan, setCurrentPlan] = useState(options[0]);
  useEffect(() => {
    if (comingInvoice?.product.name) {
      setCurrentPlan({
        value: comingInvoice.product.name,
        label: comingInvoice.product.name,
      });
    }
  }, [comingInvoice?.product.name]);

  useEffect(() => {
    if (plan === "Platform - Free Tier") {
      setCurrentPlan({
        value: "Altum AI - gratis account",
        label: "Altum AI - gratis account",
      });
    }
  }, [plan]);

  const { isPPU, isTransaction } = useMemo(
    () => ({
      isPPU: !!currentPlan && isPPUPlan(currentPlan.value),
      isTransaction: !!currentPlan && isTransactionPlan(currentPlan.value),
    }),
    [currentPlan],
  );

  const dailyPercentageUsage = useMemo(
    () => calculatePercentageDifference(usageYesterday, usageToday),
    [usageYesterday, usageToday],
  );

  const formattedLogs = useMemo(
    () =>
      logs?.map((log: Log) => ({
        ...log,
        api_name: apiNameConverter(log.api_name),
        requestTime: knowWhen(log.requestTime),
      })),
    [logs],
  );

  return (
    <>
      <DashboardContainer pageTitle="Dashboard - Altum AI">
        <Header
          title="Overzicht"
          subtitle="Hier vind je een overzicht van jouw API activiteiten"
        />
        <div className="w-48 mb-8 transition-all">
          {options.length > 1 && (
            <SelectInput
              options={options}
              onChange={(e) => {
                const selectedOption = options.find(
                  (opt) => opt.value === e.target.value,
                );
                if (selectedOption) setCurrentPlan(selectedOption);
              }}
            />
          )}
        </div>
        <div className="flex flex-col md:flex-row gap-8 transition-all w-full">
          <div className="flex flex-col gap-8 w-full">
            <div className="flex flex-col md:flex-row gap-8">
              {porterLoading || loading ? (
                <ActivePlanCardSkelecton />
              ) : (
                <ActivePlanCard
                  currentPlan={porterLoading ? "-" : currentPlan?.value || "-"}
                  creditUsed={
                    isPPU
                      ? used || 0
                      : isTransaction
                      ? transactionUsed || 0
                      : remaining || 0
                  }
                  totalCredit={String(totalCredits || "-")}
                  renewalDate={
                    isPPU
                      ? getFirstOfNextMonth()
                      : isTransaction
                      ? transactionComingInvoice?.subscription
                          ?.current_period_end
                        ? new Date(
                            transactionComingInvoice.subscription
                              .current_period_end * 1000,
                          ).toLocaleDateString()
                        : "-"
                      : comingInvoice?.subscription?.current_period_end
                      ? new Date(
                          comingInvoice.subscription.current_period_end * 1000,
                        ).toLocaleDateString()
                      : "-"
                  }
                  nextInvoices={String(
                    isPPU && comingInvoice?.comingEvent?.amount_due
                      ? (comingInvoice.comingEvent.amount_due / 100).toFixed(2)
                      : isTransaction &&
                        transactionComingInvoice?.comingEvent?.amount_due
                      ? (
                          transactionComingInvoice.comingEvent.amount_due / 100
                        ).toFixed(2)
                      : "-",
                  )}
                  usagePercentage={
                    isPPU
                      ? 0
                      : isTransaction
                      ? Math.round((transactionUsed || 0) / 100)
                      : Math.round((used || 0) / 100)
                  }
                />
              )}
              {porterLoading || loading ? (
                <ActivePlanCardSkelecton />
              ) : (
                <TotalAPICallCard
                  usageToday={usageToday}
                  usageYesterday={usageYesterday}
                  usageThisWeek={usageThisWeek}
                  usageLastWeek={usageLastWeek}
                />
              )}
            </div>
            <div className="lg:hidden">
              <RecommendedAPI />
            </div>
            {logsLoading && logs?.length === 0 ? (
              <APILogSkeleton />
            ) : (
              <APILogs logs={formattedLogs} />
            )}
          </div>
          <div className="hidden lg:block">
            <RecommendedAPI />
          </div>
        </div>
      </DashboardContainer>
    </>
  );
};

export default Dashboard;

function getFirstOfNextMonth(): string {
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const currentYear = currentDate.getFullYear();

  const nextMonth = currentMonth === 11 ? 0 : currentMonth + 1;
  const nextMonthYear = currentMonth === 11 ? currentYear + 1 : currentYear;

  const firstOfNextMonth = new Date(nextMonthYear, nextMonth, 1);
  return firstOfNextMonth.toLocaleDateString("en-CA", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
  });
}
