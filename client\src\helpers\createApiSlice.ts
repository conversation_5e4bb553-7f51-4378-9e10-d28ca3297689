import {
  createSlice,
  AsyncThunk,
  PayloadAction,
  Draft,
} from "@reduxjs/toolkit";
import { ApiOutput } from "../@types";
import { translationError } from "./createApiThunk";
import { toast } from "react-toastify";

interface ApiInitState<T = any> {
  loading: boolean;
  result: T;
  errors: unknown[];
  savedQueries: Record<string, any>;
  oneTimeResult: T;
  ping: string | null;
}

interface ApiOutputPayload<T = any> {
  output: T;
  query: {
    postcode: string;
  };
}

const createApiSlice = <T extends Record<string, any>>(
  sliceName: string,
  postApi: AsyncThunk<any, any, any>,
  oneTimeUse?: AsyncThunk<any, any, any>,
) => {
  const initialState: ApiInitState<T> = {
    loading: false,
    result: {} as T,
    errors: [],
    savedQueries: {},
    oneTimeResult: {} as T,
    ping: null,
  };

  const apiSlice = createSlice({
    name: sliceName,
    initialState,
    reducers: {
      clearResults: (state) => {
        state.result = {} as Draft<T>;
        state.savedQueries = {};
      },
      modifyQueries: (state) => {
        state.loading = false;
        state.result = {} as Draft<T>;
      },
      translations: (state) => {
        state.loading = false;
      },
    },
    extraReducers: (builder) => {
      builder
        .addCase(postApi.pending, (state) => {
          state.loading = true;
          state.errors = [];
        })
        .addCase(
          postApi.fulfilled,
          (state, action: PayloadAction<ApiOutputPayload<T>>) => {
            state.loading = false;
            state.result = action.payload.output as Draft<T>;
            state.savedQueries = action.payload.query;
          },
        )
        .addCase(postApi.rejected, (state, action) => {
          state.loading = true;
          state.errors.push(action.payload);
        })
        .addCase(translationError.fulfilled, (state, action) => {
          state.loading = false;
          toast.error(action.payload, { toastId: action.payload });
        })
        .addCase(translationError.rejected, (state, action) => {
          state.loading = false;
          toast.error(action.payload as string, {
            toastId: action.payload as string,
          });
        });

      if (oneTimeUse) {
        builder
          .addCase(oneTimeUse.pending, (state) => {
            state.loading = true;
            state.errors = [];
          })
          .addCase(
            oneTimeUse.fulfilled,
            (state, action: PayloadAction<ApiOutputPayload<T>>) => {
              state.loading = false;
              state.oneTimeResult = action.payload.output as Draft<T>;
              state.savedQueries = action.payload.query;
              const used = window.localStorage.getItem(
                `${sliceName}-demo-cookie`,
              );
              if (used) {
                window.localStorage.setItem(
                  `${sliceName}-demo-cookie`,
                  (parseInt(used) + 1).toString(),
                );
              } else {
                window.localStorage.setItem(`${sliceName}-demo-cookie`, "1");
              }
            },
          )
          .addCase(oneTimeUse.rejected, (state, action) => {
            state.errors.push(action.payload);
          });
      }
    },
  });

  return apiSlice;
};

export default createApiSlice;
export type { ApiInitState, ApiOutputPayload };
