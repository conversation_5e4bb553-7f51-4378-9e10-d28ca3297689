/**
 * Message module
 * @module messageController
 */
import { QueryResult } from "pg";
import { NextFunction, Request, Response } from "express";
import pool from "../db";
import AppError from "../utils/appError";
import { Message } from "../@types";

/**
 * Replaces character references with special characters
 * @param {string} text - text that contains character references
 * @returns {string} - text that contains special characters
 */
const escapeHtml = (text: string): string => {
  const map: Record<string, string> = {
    "&amp;": "&",
    "&lt;": "<",
    "&gt;": ">",
    "&quot;": '"',
    "&#039;": "'",
  };

  return text.replace(/&amp;|&lt;|&gt;|&quot;|&#039;/g, (m: string) => map[m]);
};

/**
 * Post general message (Admin)
 */
export const createGeneralMessage = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { title, message } = req.body;

    const newMessage = await pool.query(
      `INSERT INTO messages(title, message) VALUES($1, $2) RETURNING *;`,
      [title, message],
    );

    const users = await pool.query(`SELECT user_id from users;`);

    users.rows.forEach(async (user) => {
      await pool.query(
        `INSERT INTO messages_users (user_id, message_id) VALUES ($1, $2);`,
        [user.user_id, newMessage.rows[0].id],
      );
    });

    res.status(201).json({
      status: "success",
      data: {
        message: newMessage.rows[0],
      },
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

/**
 * Get array of messages (Admin)
 */
export const getAllMessages = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const messages = await pool.query(
      `SELECT * from messages ORDER BY created_at DESC;`,
    );
    if (!messages) {
      next(new AppError("Geen berichten", 404, true));
      return;
    }

    const escapedMessages = messages.rows.map((item) => ({
      ...item,
      message: escapeHtml(item.message),
    }));

    res.status(200).json({
      status: "success",
      results: messages.rows.length,
      data: {
        messages: escapedMessages,
      },
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

/**
 * Delete all messages (Admin)
 */
export const deleteAllMessages = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    await pool.query(`DELETE FROM messages;`);

    res.status(204).json({
      status: "success",
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

/**
 * Delete (by Patch) selected messages (Admin)
 * @param {Array} req.body.ids - array of message ids
 */
export const deleteMessageById = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { ids } = req.body;

    const promises: QueryResult[] = [];
    ids.forEach((id: string) => {
      pool.query(`DELETE from messages where id=$1`, [id]).then((result) => {
        promises.push(result);
      });
    });

    Promise.all(promises).then(() => {
      res.status(204).json({
        status: "success",
      });
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

/**
 * Get message
 * @param {String} req.params.id - message id
 */
export const getMessageById = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { id } = req.params;
    const message = await pool.query(`SELECT * from messages where id=$1`, [
      id,
    ]);
    if (!message) {
      next(new AppError("Ongeldig berichten ID", 404, true));
      return;
    }

    res.status(200).json({
      status: "success",
      data: {
        message: message.rows[0],
      },
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

/**
 * Get all messages of user
 * @param {Number} req.query.offset - records to skip
 * @param {Number} req.query.page - page after records skipped
 * @param {String} req.params.userId - user id
 */
export const getMessagesForUser = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { userId } = req.params;
    const offset = req.query.offset || 0;
    const page = Number(req.query.page) || 1;
    let messagesTotal = await pool.query(
      `SELECT COUNT(*) from messages_users where user_id=$1`,
      [userId],
    );
    messagesTotal = messagesTotal.rows[0].count;

    let unreadMessages = await pool.query<{ count: number }>(
      `SELECT COUNT(*) from messages_users where user_id=$1 and opened=$2;`,
      [userId, false],
    );
    const unreadMessagesCount = unreadMessages.rows[0].count;

    const messages = await pool.query<Message>(
      `SELECT title, message, type, created_at, opened, id 
      FROM messages INNER JOIN messages_users on messages.id = messages_users.message_id
      where user_id=$1 ORDER BY created_at DESC LIMIT 8 OFFSET $2;`,
      [userId, offset],
    );
    if (!messages) {
      next(new AppError("Geen berichten", 404, true));
      return;
    }

    const escapedMessages = messages.rows.map((item) => ({
      ...item,
      message: escapeHtml(item.message),
    }));

    res.status(200).json({
      status: "success",
      messagesTotal,
      resultsPerPage: messages.rows.length,
      unreadMessages: unreadMessagesCount,
      page,
      data: {
        userId,
        messages: escapedMessages,
      },
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

/**
 * Delete (by Patch) selected messages of user
 * @param {Array} req.params.messageIds - array of message ids
 * @param {String} req.params.userId - user id
 */
export const deleteMessagesFromUserList = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    // const { userId } = req.params;
    const { messageIds, userId } = req.body;

    if (messageIds.length === 0 || messageIds === null) {
      next(new AppError("Geen berichten geselecteerd", 400, true));
      return;
    }

    messageIds.forEach(async (id: string) => {
      await pool.query(
        `DELETE from messages_users where message_id=$1 AND user_id=$2`,
        [id, userId],
      );
    });

    res.status(204).json({
      status: "success",
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

/**
 * Patch message as opened for user
 * @param {String} req.params.userId - user id
 * @param {String} req.params.messageId - message id
 */
export const setMessageToOpened = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { messageId, userId } = req.params;
    const message = await pool.query(`SELECT * from messages where id=$1`, [
      messageId,
    ]);
    if (!message) {
      next(new AppError("Ongeldig berichten ID", 404, true));
      return;
    }

    const updatedMessageUser = await pool.query<Message>(
      `UPDATE messages_users SET opened=true where user_id=$1 AND message_id=$2 RETURNING *;`,
      [userId, messageId],
    );

    let unreadMessages = await pool.query<{ count: number }>(
      `SELECT COUNT(*) from messages_users where user_id=$1 and opened=$2;`,
      [userId, false],
    );
    const unreadMessagesCount = unreadMessages.rows[0].count;

    res.status(200).json({
      status: "success",
      unreadMessagesCount,
      data: {
        messageUser: updatedMessageUser.rows[0],
      },
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

/**
 * Patch all messages as opened for user
 * @param {String} req.params.userId - user id
 */
export const markAllAsReadForUser = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { userId } = req.params;

    const updatedMessageUser = await pool.query(
      `UPDATE messages_users SET opened=true where user_id=$1 RETURNING *;`,
      [userId],
    );

    res.status(200).json({
      status: "success",
      data: {
        messageUser: updatedMessageUser.rows,
      },
    });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};
