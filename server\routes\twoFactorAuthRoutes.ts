import express from "express";
import {
  enable2<PERSON><PERSON><PERSON><PERSON>,
  disable2<PERSON><PERSON><PERSON><PERSON>,
  verify2<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  request2FA<PERSON>odeHand<PERSON>,
} from "../controllers/twoFactorAuthController";
import { protect } from "../controllers/authController";

const router = express.Router();
// Public routes (no authentication required)
router.post("/verify", verify2FALoginHandler);
router.post("/request-code", request2FACodeHandler);

// Protected routes (require authentication)
router.use(protect);

router.post("/enable", enable2FAHandler);
router.post("/disable", disable2FAHandler);

export default router;
