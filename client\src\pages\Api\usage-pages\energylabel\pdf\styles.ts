import { StyleSheet } from "@react-pdf/renderer";

const styles = StyleSheet.create({
  page: {
    padding: 30,
    color: "#2B2D34",
    position: "relative",
  },
  mainPage: {
    padding: 10,
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    position: "relative",
  },
  title: {
    fontSize: 24,

    fontWeight: 700,
    color: "#00A651",
    marginBottom: 20,
  },
  subtitle: {
    fontSize: 16,

    fontWeight: 400,
    color: "#333333",
    marginTop: 10,
  },
  text: {
    fontSize: 12,

    fontWeight: 400,
    color: "#333333",
    marginBottom: 10,
    lineHeight: 1.5,
  },
  section: {
    marginTop: 20,
  },
  sectionTitle: {
    fontSize: 16,

    fontWeight: 600,
    color: "#00A651",
    marginBottom: 10,
  },
  propertyDetails: {
    marginTop: 20,
    padding: 15,
    backgroundColor: "#F5F5F5",
    borderRadius: 5,
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
    alignItems: "center",
  },
  detailLabel: {
    fontSize: 12,

    fontWeight: 400,
    color: "#666666",
  },
  detailValue: {
    fontSize: 12,

    fontWeight: 600,
    color: "#333333",
  },
  energyLabel: {
    width: 60,
    height: 60,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  labelText: {
    fontSize: 32,

    fontWeight: 700,
    color: "#FFFFFF",
  },
  labelA: { backgroundColor: "#00A651" },
  "labelA+": { backgroundColor: "#00A651" },
  "labelA++": { backgroundColor: "#00A651" },
  "labelA+++": { backgroundColor: "#00A651" },
  "labelA++++": { backgroundColor: "#00A651" },
  labelB: { backgroundColor: "#50B848" },
  labelC: { backgroundColor: "#B0D136" },
  labelD: { backgroundColor: "#FECC00" },
  labelE: { backgroundColor: "#F9A11B" },
  labelF: { backgroundColor: "#E63323" },
  labelG: { backgroundColor: "#E1261C" },
  footer: {
    position: "absolute",
    bottom: 30,
    left: 40,
    right: 40,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  footerText: {
    fontSize: 10,

    fontWeight: 400,
    color: "#333333",
  },
  footerLogo: {
    width: 60,
  },
  energyScaleContainer: {
    marginTop: 20,
    width: "100%",
  },
  energyScaleRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 5,
  },
  energyScaleLabel: {
    width: 30,
    height: 30,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  energyScaleBar: {
    height: 30,
    flex: 1,
    marginRight: 40,
  },
  energyScaleValue: {
    width: 40,
    fontSize: 10,
    textAlign: "right",
  },
  measureTable: {
    width: "100%",
    marginTop: 20,
  },
  measureRow: {
    flexDirection: "row",
    borderBottomWidth: 1,
    borderBottomColor: "#E5E5E5",
    paddingVertical: 8,
  },
  measureIcon: {
    width: 20,
    height: 20,
    marginRight: 10,
  },
  measureLabel: {
    flex: 1,
    fontSize: 12,
  },
  measureValue: {
    width: 100,
    fontSize: 12,
    textAlign: "right",
  },
  glossaryContainer: {
    marginTop: 20,
  },
  glossaryItem: {
    marginBottom: 15,
  },
  glossaryTerm: {
    fontSize: 12,

    fontWeight: 600,
    color: "#333333",
  },
  glossaryDefinition: {
    fontSize: 12,

    fontWeight: 400,
    color: "#333333",
    marginLeft: 20,
  },
  checklistItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
  },
  checkIcon: {
    width: 16,
    height: 16,
    marginRight: 8,
  },
  checkText: {
    fontSize: 12,

    fontWeight: 400,
    color: "#333333",
  },
  coverTitle: {
    fontSize: 48,

    fontWeight: 700,
    color: "#00A651",
    textAlign: "center",
  },
  coverSubtitle: {
    fontSize: 20,

    fontWeight: 400,
    color: "#333333",
    textAlign: "center",
    marginTop: 20,
  },
  addressBox: {
    position: "absolute",
    bottom: 100,
    left: 40,
  },
  addressLabel: {
    fontSize: 12,

    fontWeight: 600,
    color: "#FF6B00",
    marginBottom: 5,
  },
  addressText: {
    fontSize: 16,

    fontWeight: 600,
    color: "#333333",
  },
  energyScaleLegend: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 10,
  },
  legendItem: {
    flexDirection: "row",
    alignItems: "center",
    marginHorizontal: 10,
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 5,
  },
  legendText: {
    fontSize: 10,

    color: "#666666",
  },
  beng2Container: {
    marginTop: 30,
    padding: 20,
    backgroundColor: "#F5F5F5",
    borderRadius: 10,
  },
  beng2Value: {
    fontSize: 24,

    fontWeight: 700,
    color: "#00A651",
    textAlign: "center",
    marginBottom: 10,
  },
  beng2Label: {
    fontSize: 14,

    color: "#666666",
    textAlign: "center",
  },
});

export default styles;
