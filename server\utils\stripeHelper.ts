import { Request, Response } from "express";
import { stripe } from "../controllers/stripeController";
import { AuthRequest, User } from "../@types";
import pool from "../db";
import {
  STRIPE_100,
  STRIPE_250,
  STRIPE_750,
  STRIPE_2000,
  STRIPE_3000_YEARLY_RECURRING,
  STRIPE_9000_YEARLY_RECURRING,
  STRIPE_24000_YEARLY_RECURRING,
  STRIPE_20_DAILY,
  AWS_MOPSUS_USAGE_DAILY_20,
  STRIPE_100_DAILY,
  AWS_MOPSUS_USAGE_DAILY_100,
  STRIPE_250_DAILY,
  AWS_MOPSUS_USAGE_DAILY_250,
  STRIPE_TRANSACTION_100,
  STRIPE_100_TEST,
  STRIPE_250_TEST,
  STRIPE_750_TEST,
  STRIPE_2000_TEST,
  STRIPE_3000_YEARLY_RECURRING_TEST,
  STRIPE_9000_YEARLY_RECURRING_TEST,
  STRIPE_24000_YEARLY_RECURRING_TEST,
  STRIPE_20_DAILY_TEST,
  STRIPE_100_DAILY_TEST,
  STRIPE_250_DAILY_TEST,
  STRIPE_TRANSACTION_100_TEST,
  MOPSUS_100,
  MOPSUS_250,
  MOPSUS_750,
  MOPSUS_2000,
  MOPSUS_DAILY_20,
  MOPSUS_DAILY_100,
  MOPSUS_DAILY_250,
  MOPSUS_3000,
  MOPSUS_9000,
  MOPSUS_24000,
  STRIPE_WOZ_UNLIMITED,
  AWS_MOPSUS_USAGE_WOZ_UNLIMITED,
  MOPSUS_WOZ_UNLIMITED,
  STRIPE_WOZ_UNLIMITED_TEST,
  STRIPE_OBJ_DATA_UNLIMITED,
  AWS_MOPSUS_USAGE_OBJ_DATA_UNLIMITED,
  STRIPE_OBJ_DATA_UNLIMITED_TEST,
  MOPSUS_OBJ_DATA_UNLIMITED,
  STRIPE_1200_YEARLY_RECURRING,
  STRIPE_1200_YEARLY_RECURRING_TEST,
  MOPSUS_1200,
  TRANSACTION_100,
  MOPSUS_PPU,
  STRIPE_PPU,
  STRIPE_PPU_TEST,
  STRIPE_TRANSACTION_PPU,
  STRIPE_TRANSACTION_PPU_TEST,
  TRANSACTION_PPU,
  STRIPE_AVM_UNLIMITED,
  AWS_MOPSUS_USAGE_AVM_UNLIMITED,
  STRIPE_REFERENCE_UNLIMITED,
  AWS_MOPSUS_USAGE_REFERENCE_UNLIMITED,
  STRIPE_AVM_UNLIMITED_TEST,
  STRIPE_REFERENCE_UNLIMITED_TEST,
  MOPSUS_AVM_UNLIMITED,
  MOPSUS_REFERENCE_UNLIMITED,
  AWS_MOPSUS_USAGE_ECO_UNLIMITED,
  AWS_MOPSUS_USAGE_ENERGY_UNLIMITED,
  STRIPE_ECO_UNLIMITED,
  STRIPE_ENERGY_UNLIMITED,
  STRIPE_ECO_UNLIMITED_TEST,
  STRIPE_ENERGY_UNLIMITED_TEST,
  MOPSUS_ECO_UNLIMITED,
  MOPSUS_ENERGY_UNLIMITED,
  AWS_MOPSUS_STARTUP_5000,
  STRIPE_STARTUP_5000,
  STRIPE_STARTUP_200,
  AWS_MOPSUS_STARTUP_200,
  STRIPE_STARTUP_5000_TEST,
  STRIPE_STARTUP_200_TEST,
  MOPSUS_STARTUP_5000,
  MOPSUS_STARTUP_200,
} from "./constants";

export const changeUsagePlanSubscription = (priceId: string) => {
  // TODO: Replace all these production plans with new ones

  if (process.env.NODE_ENV === "production") {
    switch (priceId) {
      case STRIPE_PPU:
      case STRIPE_100:
      case STRIPE_250:
      case STRIPE_750:
      case STRIPE_2000:
      // YEARLY PLAN
      case STRIPE_1200_YEARLY_RECURRING:
      case STRIPE_3000_YEARLY_RECURRING:
      case STRIPE_9000_YEARLY_RECURRING:
      case STRIPE_24000_YEARLY_RECURRING:
        return `${process.env.AWS_MOPSUS_USAGE_PLAN_PPU}`;

      //STARTUP PLAN
      case STRIPE_STARTUP_5000:
        return AWS_MOPSUS_STARTUP_5000;
      case STRIPE_STARTUP_200:
        return AWS_MOPSUS_STARTUP_200;
      // DAILY QUOTA
      case STRIPE_20_DAILY:
        return AWS_MOPSUS_USAGE_DAILY_20;
      case STRIPE_100_DAILY:
        return AWS_MOPSUS_USAGE_DAILY_100;
      case STRIPE_250_DAILY:
        return AWS_MOPSUS_USAGE_DAILY_250;
      // ULIMITED
      case STRIPE_WOZ_UNLIMITED:
        return AWS_MOPSUS_USAGE_WOZ_UNLIMITED;
      case STRIPE_OBJ_DATA_UNLIMITED:
        return AWS_MOPSUS_USAGE_OBJ_DATA_UNLIMITED;
      case STRIPE_AVM_UNLIMITED:
        return AWS_MOPSUS_USAGE_AVM_UNLIMITED;
      case STRIPE_REFERENCE_UNLIMITED:
        return AWS_MOPSUS_USAGE_REFERENCE_UNLIMITED;
      case STRIPE_ECO_UNLIMITED:
        return AWS_MOPSUS_USAGE_ECO_UNLIMITED;
      case STRIPE_ENERGY_UNLIMITED:
        return AWS_MOPSUS_USAGE_ENERGY_UNLIMITED;
      //TRANSACTION
      case STRIPE_TRANSACTION_100:
      case STRIPE_TRANSACTION_PPU:
        return `${process.env.AWS_MOPSUS_USAGE_TRANSACTION_PPU}`;
      default:
        return `${process.env.AWS_MOPSUS_USAGE_PLAN_PPU}`;
    }
  } else {
    switch (priceId) {
      case STRIPE_PPU_TEST:
      case STRIPE_100_TEST:
      case STRIPE_250_TEST:
      case STRIPE_750_TEST:
      case STRIPE_2000_TEST:
      case STRIPE_1200_YEARLY_RECURRING_TEST:
      case STRIPE_3000_YEARLY_RECURRING_TEST:
      case STRIPE_9000_YEARLY_RECURRING_TEST:
      case STRIPE_24000_YEARLY_RECURRING_TEST:
        return `${process.env.AWS_MOPSUS_USAGE_PLAN_PPU}`;
      case STRIPE_20_DAILY_TEST:
        return AWS_MOPSUS_USAGE_DAILY_20;
      case STRIPE_100_DAILY_TEST:
        return AWS_MOPSUS_USAGE_DAILY_100;
      case STRIPE_250_DAILY_TEST:
        return AWS_MOPSUS_USAGE_DAILY_250;

      case STRIPE_STARTUP_5000_TEST:
        return AWS_MOPSUS_STARTUP_5000;
      case STRIPE_STARTUP_200_TEST:
        return AWS_MOPSUS_STARTUP_200;
      // UNLIMITED
      case STRIPE_WOZ_UNLIMITED_TEST:
        return AWS_MOPSUS_USAGE_WOZ_UNLIMITED;
      case STRIPE_OBJ_DATA_UNLIMITED_TEST:
        return AWS_MOPSUS_USAGE_OBJ_DATA_UNLIMITED;
      case STRIPE_AVM_UNLIMITED_TEST:
        return AWS_MOPSUS_USAGE_AVM_UNLIMITED;
      case STRIPE_REFERENCE_UNLIMITED_TEST:
        return AWS_MOPSUS_USAGE_REFERENCE_UNLIMITED;
      case STRIPE_ECO_UNLIMITED_TEST:
        return AWS_MOPSUS_USAGE_ECO_UNLIMITED;
      case STRIPE_ENERGY_UNLIMITED_TEST:
        return AWS_MOPSUS_USAGE_ENERGY_UNLIMITED;
      // TRANSACTION PLAN
      case STRIPE_TRANSACTION_PPU_TEST:
      case STRIPE_TRANSACTION_100_TEST:
        return `${process.env.AWS_MOPSUS_USAGE_TRANSACTION_PPU}`;
      default:
        return `${process.env.AWS_MOPSUS_USAGE_PLAN_PPU}`;
    }
  }
};
export const returnSubscriptionPlan = (priceId: string) => {
  if (process.env.NODE_ENV === "production") {
    switch (priceId) {
      case STRIPE_PPU:
        return MOPSUS_PPU;

      case STRIPE_100:
        return MOPSUS_100;

      case STRIPE_250:
        return MOPSUS_250;

      case STRIPE_750:
        return MOPSUS_750;

      case STRIPE_2000:
        return MOPSUS_2000;

      case STRIPE_STARTUP_5000:
        return MOPSUS_STARTUP_5000;
      case STRIPE_STARTUP_200:
        return MOPSUS_STARTUP_200;
      // DAILY QUOTA
      case STRIPE_20_DAILY:
        return MOPSUS_DAILY_20;

      case STRIPE_100_DAILY:
        return MOPSUS_DAILY_100;

      case STRIPE_250_DAILY:
        return MOPSUS_DAILY_250;
      // YEARLY
      case STRIPE_1200_YEARLY_RECURRING:
        return MOPSUS_1200;

      case STRIPE_3000_YEARLY_RECURRING:
        return MOPSUS_3000;

      case STRIPE_9000_YEARLY_RECURRING:
        return MOPSUS_9000;

      case STRIPE_24000_YEARLY_RECURRING:
        return MOPSUS_24000;
      // UNLIMITED
      case STRIPE_WOZ_UNLIMITED:
        return MOPSUS_WOZ_UNLIMITED;
      case STRIPE_OBJ_DATA_UNLIMITED:
        return MOPSUS_OBJ_DATA_UNLIMITED;
      case STRIPE_AVM_UNLIMITED:
        return MOPSUS_AVM_UNLIMITED;
      case STRIPE_REFERENCE_UNLIMITED:
        return MOPSUS_REFERENCE_UNLIMITED;
      case STRIPE_ECO_UNLIMITED:
        return MOPSUS_ECO_UNLIMITED;
      case STRIPE_ENERGY_UNLIMITED:
        return MOPSUS_ENERGY_UNLIMITED;

      case STRIPE_TRANSACTION_PPU:
        return TRANSACTION_PPU;

      case STRIPE_TRANSACTION_100:
        return TRANSACTION_100;

      default:
        return "";
    }
  } else {
    switch (priceId) {
      case STRIPE_PPU_TEST:
        return MOPSUS_PPU;
      case STRIPE_100_TEST:
        return MOPSUS_100;

      case STRIPE_250_TEST:
        return MOPSUS_250;

      case STRIPE_750_TEST:
        return MOPSUS_750;

      case STRIPE_2000_TEST:
        return MOPSUS_2000;

      case STRIPE_20_DAILY_TEST:
        return MOPSUS_DAILY_20;

      case STRIPE_100_DAILY_TEST:
        return MOPSUS_DAILY_100;

      case STRIPE_250_DAILY_TEST:
        return MOPSUS_DAILY_250;

      case STRIPE_STARTUP_5000_TEST:
        return MOPSUS_STARTUP_5000;
      case STRIPE_STARTUP_200_TEST:
        return MOPSUS_STARTUP_200;

      // Yearly Plan
      case STRIPE_1200_YEARLY_RECURRING_TEST:
        return MOPSUS_1200;

      case STRIPE_3000_YEARLY_RECURRING_TEST:
        return MOPSUS_3000;

      case STRIPE_9000_YEARLY_RECURRING_TEST:
        return MOPSUS_9000;

      case STRIPE_24000_YEARLY_RECURRING_TEST:
        return MOPSUS_24000;

      // UNlimited
      case STRIPE_WOZ_UNLIMITED_TEST:
        return MOPSUS_WOZ_UNLIMITED;
      case STRIPE_OBJ_DATA_UNLIMITED_TEST:
        return MOPSUS_OBJ_DATA_UNLIMITED;
      case STRIPE_AVM_UNLIMITED_TEST:
        return MOPSUS_AVM_UNLIMITED;
      case STRIPE_REFERENCE_UNLIMITED_TEST:
        return MOPSUS_REFERENCE_UNLIMITED;
      case STRIPE_ECO_UNLIMITED_TEST:
        return MOPSUS_ECO_UNLIMITED;
      case STRIPE_ENERGY_UNLIMITED_TEST:
        return MOPSUS_ENERGY_UNLIMITED;

      // Transaction

      case STRIPE_TRANSACTION_PPU_TEST:
        return TRANSACTION_PPU;
      case STRIPE_TRANSACTION_100_TEST:
        return TRANSACTION_100;

      default:
        return "";
    }
  }
};

export const returnSubCost = (name: string) => {
  switch (name) {
    case MOPSUS_100:
      return "plan_100";

    case MOPSUS_250:
      return "plan_250";

    case MOPSUS_750:
      return "plan_500";
    default:
      return null;
  }
};
export async function createStripeUsage(
  subscriptionItem: string,
  count: number,
) {
  await stripe.subscriptionItems.createUsageRecord(subscriptionItem, {
    quantity: count,
  });
}
export async function findOneByStripeId(table: string, customerId: string) {
  return await pool.query(`SELECT * FROM ${table} WHERE customer_id=$1`, [
    customerId,
  ]);
}
export async function findUserByStripeId(customerId: string) {
  return await pool.query(`SELECT * FROM users WHERE stripe_customer_id=$1`, [
    customerId,
  ]);
}
export async function updateCustomerPaymentMethod(
  customerId: string,
  paymentMethodId: string,
) {
  await stripe.customers.update(customerId, {
    invoice_settings: {
      default_payment_method: paymentMethodId,
    },
  });
}
export async function attachPaymentMethodToCustomer(
  paymentMethodId: string,
  customerId: string,
) {
  await stripe.paymentMethods.attach(paymentMethodId, {
    customer: customerId,
  });
}

export const createSetupIntent = async (req: Request, res: Response) => {
  try {
    if (req.user == null) {
      return;
    }
    const user = req.user as User;
    const setupIntent = await stripe.setupIntents.create({
      payment_method_types: ["card", "sepa_debit"],
      customer: user.stripe_customer_id,
    });
    res.status(200).json({ clientSecret: setupIntent.client_secret });
  } catch (error: any) {
    console.log(error);
    res.status(400).json({ error: error.message });
  }
};

export const createPaymentIntent = async (req: Request, res: Response) => {
  try {
    if (req.user == null) {
      return;
    }
    const user = req.user as User;
    const paymentIntent = await stripe.paymentIntents.create({
      payment_method_types: ["card", "sepa_debit"],
      customer: user.stripe_customer_id,
      amount: 0,
      currency: "",
    });
    res.status(200).json({ clientSecret: paymentIntent.client_secret });
  } catch (error: any) {
    res.status(400).json({ error: error.message });
  }
};
export const updateCustomerEmail = async (req: AuthRequest, res: Response) => {
  try {
    const updatedCustomer = await stripe.customers.update(
      req.user.stripe_customer_id,
      { email: req.body.email },
    );
    res.status(200).json({ updatedCustomer });
  } catch (error: any) {
    res.status(400).json({ error: error.message });
  }
};
