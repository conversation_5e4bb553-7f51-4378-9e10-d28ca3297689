// Import packages
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import { validationResult } from "express-validator";
import crypto from "crypto";
import AWS from "aws-sdk";
import { nanoid } from "nanoid";
import { NextFunction, Request, Response } from "express";
import rateLimit from "express-rate-limit";
import { send2FACode } from "../utils/twoFactorAuth";
import pool from "../db";
import { createStripeCustomer } from "./stripeController";
import Email from "../utils/emailHandler";
import { createAnalyticsTag } from "../utils/sendAnalytics";
import logger from "../utils/logger";
import { AnalyticsHooks } from "../services/admin/analyticsHooks";
import { isDisposableDomain } from "../utils/domainChecker";

// Import utils
import AppError from "../utils/appError";
import { createTokenResponse } from "../utils/authUtil";
import { updateContact } from "../utils/sendgridContactHandler";
import { AuthRequest, JWTPayload, User } from "../@types";
import { generateCode } from "../utils/generateOtp";
import { BlockedUserService } from "../services/admin/blockedUserService";
import {
  addOnboardingState,
  getOnboardingStepId,
} from "./onboardingController";
import { addOnboardingStateService } from "../services/onboardingService";

AWS.config.update({ region: "eu-west-1" });

// Constants for security
const GENERIC_AUTH_ERROR = "Ongeldige e-mailadres en/of wachtwoord combinatie";
const GENERIC_RESET_MESSAGE =
  "Als er een account bestaat met dit e-mailadres, ontvangt u een link om uw wachtwoord opnieuw in te stellen";
const TOKEN_EXPIRY_MINUTES = 30;

// List of blocked forwarding email domains
const BLOCKED_EMAIL_DOMAINS = [
  // 10 Minute Mail variants
  "10minutemail.com",
  "10minutemail.net",
  "10minutemail.org",

  // Temp Mail variants
  "temp-mail.org",
  "tempmail.com",
  "tempmail.net",
  "tempmail.org",
  "tempr.email",
  "tempinbox.co.uk",
  "tempinbox.com",
  "temporaryinbox.com",
  "tempemail.biz",
  "tempemail.com",
  "tempemail.net",
  "tempmail.dev",
  "tempmail.ninja",
  "tempmail.plus",
  "tempmailo.com",
  "minuteinbox.com",
  "tempmailin.com",
  "tempmail.altmails.com",
  ...Array.from(
    { length: 40 },
    (_, i) =>
      `tempmail.${
        [
          "co",
          "io",
          "app",
          "wiki",
          "cloud",
          "live",
          "space",
          "tech",
          "digital",
          "online",
          "site",
          "network",
          "center",
          "guru",
          "info",
          "fun",
          "design",
          "today",
          "agency",
          "services",
          "solutions",
          "systems",
          "works",
          "expert",
          "directory",
          "guide",
          "consulting",
          "management",
          "marketing",
          "media",
          "studio",
          "technology",
          "tools",
          "ventures",
          "vision",
          "wtf",
          "xyz",
          "zone",
          "email",
        ][i] || ""
      }`,
  ),

  // Trash Mail variants
  "throwawaymail.com",
  "trashmail.com",
  "trashmail.net",
  "trashmail.at",
  "trashmail.me",
  "trashmail.ws",
  "trashymail.com",
  "trashymail.net",
  "trash2009.com",
  "trashdevil.com",
  "trashdevil.de",
  "mytrashmail.com",

  // Mailinator variants
  "mailinator.com",
  "mailinator.net",

  // Guerrilla Mail variants
  "guerrillamail.com",
  "guerrillamail.net",
  "guerrillamail.org",
  "guerrillamail.biz",
  "sharklasers.com",
  "grr.la",

  // Yopmail variants
  "yopmail.com",
  "yopmail.net",
  "yopmail.fr",

  // Mail Drop variants
  "maildrop.cc",
  "spamgourmet.com",
  "spamgourmet.net",
  "spamgourmet.org",

  // Jetable variants
  "jetable.org",
  "jetable.net",
  "jetable.com",

  // Disposable variants
  "dispostable.com",
  "discard.email",
  "discardmail.com",
  "discardmail.de",

  // Spambog variants
  "spambog.com",
  "spambog.de",
  "spambog.ru",

  // Forward Mail Services
  "forward.cat",
  "forward.email",
  "forwardemail.net",
  "anonaddy.com",
  "anonaddy.me",
  "simplelogin.co",
  "simplelogin.io",
  "relay.firefox.com",
  "duck.com",
  "relay.duck.com",

  // Email Relay/Hide Services
  "hidemy.name",
  "hidemail.pro",
  "burnermail.io",
  "email-relay.com",

  // Additional Disposable Services
  "mailnesia.com",
  "mailnull.com",
  "spam4.me",
  "nospam.ze.tc",
  "nomail.xl.cx",
  "mega.zik.dj",
  "speed.1s.fr",
  "courriel.fr.nf",
  "moncourrier.fr.nf",
  "monemail.fr.nf",
  "monmail.fr.nf",

  // More Disposable Services
  "0815.ru",
  "0wnd.net",
  "0wnd.org",
  "33mail.com",
  "anonymail.dk",
  "anonymizer.com",
  "bloggeremail.com",
  "bugmenot.com",
  "deadaddress.com",
  "dodgeit.com",
  "e4ward.com",
  "emailias.com",
  "fakeinbox.com",
  "fastmail.fm",
  "filzmail.com",
  "fudgerub.com",
  "get2mail.fr",
  "getonemail.com",
  "gishpuppy.com",
  "great-host.in",
  "incognitomail.com",
  "kasmail.com",
  "keemail.me",
  "kleemail.com",
  "koszmail.pl",
  "kurzepost.de",
  "lavabit.com",
  "letthemeatspam.com",
  "lhsdv.com",
  "lifebyfood.com",
  "link2mail.net",
  "lortemail.dk",
  "mintemail.com",
  "myspamless.com",
  "netmails.net",
  "netzidiot.de",
  "no-spam.ws",
  "nobulk.com",
  "noclickemail.com",
  "nowmymail.com",
  "objectmail.com",
  "obobbo.com",
  "oneoffemail.com",
  "onewaymail.com",
  "otherinbox.com",
  "pjjkp.com",
  "politikerclub.de",
  "pookmail.com",
  "privacy.net",
  "proxymail.eu",
  "prtnx.com",
  "putthisinyourspamdatabase.com",
  "quickinbox.com",
  "rcpt.at",
  "recode.me",
  "recursor.net",
  "regbypass.com",
  "safetymail.info",
  "sandelf.de",
  "saynotospams.com",
  "selfdestructingmail.com",
  "sendspamhere.com",
  "shiftmail.com",
  "shitmail.me",
  "shitmail.org",
  "sneakemail.com",
  "sofort-mail.de",
  "sogetthis.com",
  "soodonims.com",
  "spam.la",
  "spam.su",
  "spamavert.com",
  "spambox.info",
  "spambox.us",
  "spamcannon.com",
  "spamcannon.net",
  "spamcero.com",
  "spamcon.org",
  "spamcorptastic.com",
  "spamday.com",
  "spamex.com",
  "spamobox.com",
  "spamslicer.com",
  "spamspot.com",
  "spamthis.co.uk",
  "spamthisplease.com",
  "spamtrail.com",
  "supergreatmail.com",
  "supermailer.jp",
  "suremail.info",
  "teewars.org",
  "teleworm.com",
  "thankyou2010.com",
  "thisisnotmyrealemail.com",
  "throwawayemailaddress.com",
  "tilien.com",
  "tmailinator.com",
  "toiea.com",
  "trashemail.de",
  "turual.com",
  "twinmail.de",
  "tyldd.com",
  "uggsrock.com",
  "upliftnow.com",
  "venompen.com",
  "wegwerfadresse.de",
  "wegwerfemail.com",
  "wegwerfemail.de",
  "wegwerfemail.net",
  "wegwerfemail.org",
  "wetrainbayarea.com",
  "wetrainbayarea.org",
  "wh4f.org",
  "whyspam.me",
  "willhackforfood.biz",
  "willselfdestruct.com",
  "winemaven.info",
  "wronghead.com",
  "wuzup.net",
  "wuzupmail.net",
  "xemaps.com",
  "xents.com",
  "xmaily.com",
  "xoxy.net",
  "yep.it",
  "yogamaven.com",
  "yuurok.com",
  "zehnminutenmail.de",
  "zippymail.info",
  "zoaxe.com",
  "zoemail.net",
  "zoemail.org",
  "passmail.com",
  "passmail.net",
  "passmail.org",
  "passmails.com",
  "passmails.net",
  "passmails.org",
  "passmails.co.uk",
  "passmails.info",
  "passmails.me",
  "passmails.us",
  "passmails.cc",
  "passmails.pro",
  "passmails.biz",
  "passmails.name",
  "passmails.asia",
  "passmails.tel",
  "passmails.mobi",
  "passmails.cc.uk",
];

// Helper function to check if email domain is blocked
const isBlockedEmailDomain = (email: string): boolean => {
  const domain = email.split("@")[1]?.toLowerCase();
  return BLOCKED_EMAIL_DOMAINS.includes(domain);
};

// Rate limiters
export const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per IP
  message: "Te veel inlogpogingen. Probeer het over 15 minuten opnieuw.",
});

export const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 attempts per IP
  message:
    "Te veel pogingen om het wachtwoord opnieuw in te stellen. Probeer het over een uur opnieuw.",
});

// FUNCTION: Create JWT based on user id

// FUNCTION: Hash and encrypt password
const encryptPassword = async (password: string) => {
  const saltRounds = 10;
  const salt = await bcrypt.genSalt(saltRounds);
  return await bcrypt.hash(password, salt);
};

// MIDDLEWARE: Error checking / Validation on inputs
export const errorCheck = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessage = errors
      .array()
      .map((error) => `${error.param}: ${error.msg}`);
    next(new AppError(errorMessage[0], 400, true));
    return;
  }
  next();
};

// MIDDLEWARE: PROTECT, give access to only users with valid token
export const protect = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    if (req.user != null) {
      next();
      return;
    }

    // Check for token in cookie or Authorization header
    let token = req.cookies["x-mopsus-jwt"];

    // Check for Bearer token in Authorization header
    const authHeader = req.headers.authorization;
    if (!token && authHeader && authHeader.startsWith("Bearer ")) {
      token = authHeader.split(" ")[1];
    }

    if (!token) {
      next(new AppError("je bent niet ingelogd", 401, true));
      return;
    }

    const validToken = jwt.verify(
      token,
      process.env.JWT_SECRET_KEY!,
    ) as JWTPayload;
    const user = await pool.query<User>(
      "SELECT * FROM users where user_id=$1",
      [validToken.id],
    );
    if (user.rows.length === 0) {
      next(
        new AppError(
          "De token voor deze gebruiker bestaat niet meer",
          401,
          true,
        ),
      );
      return;
    }

    // Set user in request to current user
    const returnUser = { ...user.rows[0], password: "" };
    req.user = returnUser;

    // Track user activity
    await AnalyticsHooks.onUserActivity(returnUser.user_id, "api_request");

    next();
  } catch (error: any) {
    console.log("error", error);
    // Send error as JSON response
    next(new AppError(error.message, 401, false));
  }
};

export const allowAdminOnly = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    if (req.user == null) {
      return;
    }
    const user = req.user as User;
    if (user.role === "admin") {
      next();
    } else {
      next(new AppError("Unauthorized user", 403, true));
    }
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

// HANDLER: SIGN UP
export const signup = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  // Destructure form details from req.body
  const {
    first_name,
    last_name,
    email,
    password,
    usageId = "812sc7",
  }: User = req.body;
  try {
    // Check if email domain is blocked
    if (isBlockedEmailDomain(email)) {
      next(new AppError(GENERIC_AUTH_ERROR, 400, true));
      return;
    }
    // Extract domain from email
    const domain = email.split("@")[1];
    if (!domain) {
      next(new AppError("Invalid email format", 400, true));
      return;
    }

    // Check if domain is disposable using API
    const isDisposable = await isDisposableDomain(domain);
    if (isDisposable) {
      console.warn(`Warning: Disposable email domain detected for ${email}`);
      next(new AppError(GENERIC_AUTH_ERROR, 400, true));
      return;
    }

    // Check if email is blocked
    const isBlocked = await BlockedUserService.isEmailBlocked(email);
    if (isBlocked) {
      next(new AppError(GENERIC_AUTH_ERROR, 401, true));
      return;
    }

    // Check if user already exists
    const user = await pool.query<User>("SELECT * FROM users WHERE email=$1", [
      email,
    ]);
    if (user.rows.length > 0 && user.rows[0].active) {
      next(new AppError("Gebruiker bestaat al", 409, true));
      return;
    }
    if (user.rows.length > 0 && !user.rows[0].active) {
      const response = await sendVerificationMailService(email);
      return res.status(201).json({
        message: response.message,
        status: "success",
        accountInitialised: true,
        active: false,
        id: user.rows[0].user_id,
      });
    }

    // Encrypt the password
    const encryptedPassword = await encryptPassword(password);
    logger.info("Sign up via Email");
    // Create Stripe customer object
    logger.info("creating stripe customer object");
    const stripeCustomerId = await createStripeCustomer(email);

    const verificationExpiry = (Date.now() + 24 * 60 * 60 * 1000) / 1000.0;
    const apiKeyName = `Mopsus-${email}-${nanoid(10)}`;

    // AUTO-CREATE API KEY FOR USER8
    logger.info("AUTO-CREATE API KEY FOR USER");
    let apiKey = "";
    let apiKeyId = "";
    const apiGateway = new AWS.APIGateway({
      accessKeyId: process.env.AWS_ACCESS_KEY,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      endpoint: "https://apigateway.eu-west-1.amazonaws.com",
    });

    // // Create AWS API key for usage plan
    logger.info("creating AWS API Key for usage plan");
    apiGateway.createApiKey(
      {
        name: apiKeyName,
        enabled: true,
      },
      async (err, data) => {
        if (err) console.log(err, err.stack);
        apiKey = data.value!;
        apiKeyId = data.id!;
        // LINK TO CALCHAS USAGE PLAN
        apiGateway.createUsagePlanKey(
          {
            keyId: data.id!,
            keyType: "API_KEY",
            usagePlanId: usageId,
          },
          async (err, data) => {
            if (err) {
              logger.error(err);
              console.log(err, err.stack);
            }

            // If no errors, create new user
            logger.info("No error found creating usage plan key");
            const newUser = await createNewUser(
              first_name,
              last_name,
              email,
              encryptedPassword,
              "",
              "",
              apiKey,
              apiKeyId,
              stripeCustomerId,
              verificationExpiry,
              usageId,
            );

            // Log signup event
            await AnalyticsHooks.onUserSignup(newUser);

            // enter user into usage_emails table
            await addUserToEmailTable(newUser);
            await addUserToNotificationTable(newUser);
            const response = await sendVerificationMailService(newUser.email);

            await createAnalyticsTag({
              event: "Signup",
              category: "Authentication",
            });
            logger.info("User created successfully");
            return res.status(201).json({
              message: response.message,
              status: "success",
              accountInitialised: true,
              active: false,
              id: newUser.user_id,
            });
          },
        );
      },
    );
  } catch (error: any) {
    // Send error as JSON response
    res.status(500).json({ message: error?.message });
    next(new AppError(error.message, 500, false));
  }
};

export async function addUserToNotificationTable(user: User) {
  await pool.query("INSERT INTO user_notifications(user_id) VALUES($1)", [
    user.user_id,
  ]);
}

// HANDLER: SIGN IN
export const signIn = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { email, password } = req.body;

    // Check if email domain is blocked
    if (isBlockedEmailDomain(email)) {
      next(new AppError(GENERIC_AUTH_ERROR, 400, true));
      return;
    }

    // Check if email is blocked
    const isBlocked = await BlockedUserService.isEmailBlocked(email);
    if (isBlocked) {
      next(new AppError(GENERIC_AUTH_ERROR, 401, true));
      return;
    }

    // check if user exists and validate password
    const user = await pool.query<User>("SELECT * FROM users WHERE email=$1", [
      email,
    ]);
    const validPassword =
      user.rows.length > 0
        ? await bcrypt.compare(password, user.rows[0].password)
        : false;

    if (!user.rows.length || !validPassword) {
      next(new AppError(GENERIC_AUTH_ERROR, 401, true));
      return;
    }

    if (!user.rows[0].active) {
      const res = await sendVerificationMailService(email);
      next(new AppError(res.message, 400, true));
      return;
    }

    // Check if user has 2FA enabled
    if (user.rows[0].two_factor_enabled) {
      if (user.rows[0].two_factor_type === "email") {
        // Send 2FA code via email
        await send2FACode(user.rows[0]);
      }

      return res.status(200).json({
        status: "2fa_required",
        message: "2FA verification required",
        userId: user.rows[0].user_id,
        type: user.rows[0].two_factor_type,
      });
    }

    // Always clear password reset token and reset expiry if it exists
    await pool.query(
      "UPDATE users SET password_reset_token=$1, password_reset_token_expiry=$2 where email=$3",
      [null, null, email],
    );

    if (user.rows[0].role !== "admin") {
      await createAnalyticsTag({ event: "Signin", category: "Authentication" });
    }

    // Log login event
    await AnalyticsHooks.onUserLogin(user.rows[0]);

    createTokenResponse(user.rows[0], res);
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

// HANDLER: LOGOUT USER
export const logout = (req: Request, res: Response, next: NextFunction) => {
  // Set cookie to expire instantly
  // req.session = null;

  res.cookie("x-mopsus-jwt", "", {
    expires: new Date(Date.now()),
    httpOnly: true,
  });
  res.status(200).json({
    status: "success",
  });
};

// HANDLER: LOAD USER
export const loadUser = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    if (req.user == null) {
      console.log("User not found");
      return;
    }
    const user1 = req.user as User;
    const user = await pool.query<User>(
      "SELECT * FROM users WHERE user_id=$1",
      [user1.user_id],
    );
    const returnUser = { ...user.rows[0], password: "" };
    return res.status(200).json({
      status: "success",
      user: returnUser,
    });
  } catch (error: any) {
    console.log(error);
    // return next(new AppError(error.message, 500, false));
  }
};

// HANDLER: FORGOT PASSWORD => SEND RESET TOKEN
export const forgotPassword = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = await pool.query<User>("SELECT * FROM users where email=$1", [
      req.body.email,
    ]);

    // Always return success response regardless of whether user exists
    if (user.rows.length === 0) {
      res.status(200).json({
        status: "success",
        message: GENERIC_RESET_MESSAGE,
      });
      return;
    }

    const resetToken = crypto.randomBytes(32).toString("hex"); // Increased from 16 to 32 bytes
    const encyrptedToken = crypto
      .createHash("sha256")
      .update(resetToken)
      .digest("hex");

    const tokenExpiry =
      (Date.now() + TOKEN_EXPIRY_MINUTES * 60 * 1000) / 1000.0;

    await pool.query(
      "UPDATE users SET password_reset_token=$1, password_reset_token_expiry=(to_timestamp($2)) where email=$3",
      [encyrptedToken, tokenExpiry, req.body.email],
    );

    const url = `${req.protocol}://${req.get(
      "host",
    )}/users/resetPassword/${resetToken}`;

    await new Email(user.rows[0], url).changePassword();

    // Don't expose reset token in response
    res.status(200).json({
      status: "success",
      message: GENERIC_RESET_MESSAGE,
    });
  } catch (error: any) {
    await pool.query(
      "UPDATE users SET password_reset_token=$1, password_reset_token_expiry=$2 where email=$3",
      [null, null, req.body.email],
    );
    next(new AppError(error.message, 500, false));
  }
};

// HANDLER: RESET PASSWORD
export const resetPassword = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { resetToken } = req.params;
    const encyrptedToken = crypto
      .createHash("sha256")
      .update(resetToken)
      .digest("hex");

    // Check if user has valid token and token expiry
    const user = await pool.query<User>(
      `SELECT * FROM users 
       WHERE password_reset_token = $1 
       AND password_reset_token_expiry > NOW() 
       AND password_reset_token_expiry <= NOW() + INTERVAL '${TOKEN_EXPIRY_MINUTES} minutes'`,
      [encyrptedToken],
    );

    if (user.rows.length === 0) {
      next(new AppError("Reset token is ongeldig of verlopen", 400, true));
      return;
    }

    const { password, passwordConfirm } = req.body;
    if (password !== passwordConfirm) {
      next(new AppError("Wachtwoorden komen niet overeen", 400, true));
      return;
    }

    const encryptedPassword = await encryptPassword(password);

    // Invalidate token, update password, track password change, and clear any active 2FA codes
    const updatedUser = await pool.query<User>(
      `UPDATE users 
       SET password=$1, 
           password_reset_token=NULL, 
           password_reset_token_expiry=NULL,
           last_password_change=CURRENT_TIMESTAMP,
           otp=NULL,
           otp_expiration=CURRENT_TIMESTAMP
       WHERE email=$2 
       RETURNING *`,
      [encryptedPassword, user.rows[0].email],
    );

    createTokenResponse(updatedUser.rows[0], res);
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const checkValidResetPasswordToken = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  // check if resetToken belongs to user
  const { resetToken } = req.params;
  // Encrypt token to check if it is the same as in DB
  const encyrptedToken = await crypto
    .createHash("sha256")
    .update(resetToken)
    .digest("hex");

  // Check if user has valid token and token expiry is greater than Date.now
  const user = await pool.query<User>(
    `SELECT * FROM users WHERE password_reset_token=$1 and password_reset_token_expiry>NOW()`,
    [encyrptedToken],
  );
  if (user.rows.length === 0) {
    next(new AppError("Reset token is ongeldig of verlopen", 400, true));
    return;
  }

  res.status(200).json({
    status: "success",
    resetTokenAccess: true,
  });
};

export const verifyAccount = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { email, code } = req.body;
    const user = await pool.query<User>(
      "SELECT * FROM users WHERE email=$1 AND active=$2",
      [email, false],
    );
    if (user.rows.length === 0) {
      next(
        new AppError(
          "Een account met dit e-mailadres bestaat niet of het account is al geverifieerd.",
          400,
          true,
        ),
      );
      return;
    }
    if (user.rows[0].otp_expiration < Date.now()) {
      next(new AppError("De code is mogelijk verlopen", 400, true));
      return;
    }
    if (user.rows[0].otp != code.toString()) {
      next(
        new AppError(`${email} niet geverifieerd. Ongeldige code`, 400, true),
      );
      return;
    }
    const updatedUser = await pool.query<User>(
      "UPDATE users SET active=$1, verification_timer=$2 WHERE email=$3 RETURNING *",
      [true, null, email],
    );
    const activeEmailStep = await getOnboardingStepId(
      "Bevestig het email adres",
    );
    await addOnboardingStateService(
      activeEmailStep.step_id,
      updatedUser.rows[0].user_id,
    );

    // Log email verification
    await AnalyticsHooks.onEmailVerification(updatedUser.rows[0]);

    await updateContact(user.rows[0], process.env.SENDGRID_LIST_NEWUSERS!);

    await createAnalyticsTag({
      event: "Verified account",
      category: "Authentication",
    });

    createTokenResponse(updatedUser.rows[0], res);
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const sendVerificationEmail = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { email } = req.body;
    const response = await sendVerificationMailService(email);
    res.status(200).json({ message: response.message });
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

async function sendVerificationMailService(email: string) {
  try {
    const code = generateCode();
    const query = await pool.query(
      `SELECT active, email FROM users where email=$1`,
      [email],
    );
    await new Email(query.rows[0]).verifyEmail(code);
    await pool.query<User>(
      "UPDATE users SET otp=$1, otp_expiration=(CURRENT_TIMESTAMP + INTERVAL '10 minutes') WHERE email=$2 RETURNING *",
      [code, email],
    );

    return {
      status: "SUCCESS",
      message: `verificatiecode is verzonden naar ${email}`,
    };
  } catch (error: any) {
    throw new AppError(error.message, 400, false);
  }
}

export const allowSignupConfirmation = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { id } = req.params;
    const query = await pool.query(
      `SELECT active, email FROM users where user_id=$1`,
      [id],
    );

    if (query.rows.length === 0) {
      next(new AppError("Geen gebruiker gevonden", 404, true));
      return;
    }

    const { active } = query.rows[0];
    if (!active) {
      res.status(200).json({
        status: "success",
      });
    } else {
      next(new AppError("Gebruiker is al geverifieerd.", 400, true));
    }
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};

export const addUserToEmailTable = async (newUser: User) => {
  logger.info("Add User to Email Table");
  await pool.query<User>(
    "INSERT INTO usage_emails (user_id, api_extension_updated_at, email_80_sent_at, email_100_sent_at, hrs24_api_not_used, days7_api_not_used) values ($1, to_timestamp($2), to_timestamp($3), to_timestamp($4), to_timestamp($5), to_timestamp($6)) RETURNING *",
    [
      newUser.user_id,
      Date.now() / 1000,
      Date.now() / 1000 - 100,
      Date.now() / 1000 - 100,
      (Date.now() + 24 * 3600 * 1000) / 1000,
      (Date.now() + 7 * 24 * 3600 * 1000) / 1000,
    ],
  );
};

export const createNewUser = async (
  first_name: string,
  last_name: string,
  email: string,
  encryptedPassword: string,
  company: string,
  kvk: string,
  apiKey: string,
  apiKeyId: string,
  stripeCustomerId: string,
  verificationExpiry: number,
  usagePlanId = "812sc7",
  active = false, // Default to free plan
): Promise<User> => {
  logger.info("Creating new user");

  const newUser = await pool.query<User>(
    "INSERT INTO users (first_name, last_name, email, password, company, kvk, api_key, api_key_id, current_usage_plan, stripe_customer_id, verification_timer, active) VALUES($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, to_timestamp($11), $12) RETURNING *",
    [
      first_name,
      last_name,
      email,
      encryptedPassword,
      company,
      kvk,
      apiKey,
      apiKeyId,
      usagePlanId,
      stripeCustomerId,
      verificationExpiry,
      active,
    ],
  );
  return newUser.rows[0];
};

// 2FA verification handler for both social and regular logins
export const verify2FA = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { userId, code } = req.body;

    // Get user by ID
    const user = await pool.query<User>(
      "SELECT * FROM users WHERE user_id = $1",
      [userId],
    );

    if (user.rowCount === 0) {
      next(new AppError("User not found", 404, true));
      return;
    }

    // Verify 2FA code
    const isValid = await pool.query(
      "SELECT * FROM two_factor_codes WHERE user_id = $1 AND code = $2 AND expires_at > NOW()",
      [userId, code],
    );

    if (isValid.rowCount === 0) {
      next(new AppError("Invalid or expired 2FA code", 400, true));
      return;
    }

    // Clear used 2FA code
    await pool.query("DELETE FROM two_factor_codes WHERE user_id = $1", [
      userId,
    ]);
    createTokenResponse(user.rows[0], res);
  } catch (error: any) {
    next(new AppError(error.message, 500, false));
  }
};
