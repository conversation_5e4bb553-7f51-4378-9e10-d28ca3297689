import { GraphQLClient } from "graphql-request";
import AppError from "./appError";

export default async (status: string | number) => {
  try {
    let query = `
    {
      actor {
        account(id: $id) {
          nrql(
            query: "SELECT count(*) FROM Log WHERE status = ${status} SINCE 24 hours ago FACET apiKey"
          ) {
            results
          }
        }
      }
    }
  `;

    query = query.replace("$id", process.env.GRAPHQL_ID!);

    const client = new GraphQLClient("https://api.eu.newrelic.com/graphql", {
      headers: { "api-key": `${process.env.GRAPHQL_APIKEY}` },
    });

    return await client
      .request(query)
      .then((data) => data.actor.account.nrql.results)
      .catch((error) => {
        console.log(error);
        new AppError("unable to fetch apiKey from new relic", 400);
      });
  } catch (err) {
    console.log(err);
    return new AppError("unable to fetch api<PERSON><PERSON> from new relic", 400);
  }
};
