import React from "react";
import { View, StyleSheet, Text, Svg, Path } from "@react-pdf/renderer";

interface ScaleValueMarkerProps {
  value: number;
  position: {
    left?: string;
    top?: number;
  };
}

const DEFAULT_TOP_POSITION = 35;

const styles = StyleSheet.create({
  markerContainer: {
    position: "absolute",
    alignItems: "center",
    transform: "translateX(-15px)",
    zIndex: 10,
  },
  valueText: {
    fontSize: 8,
    textAlign: "center",
    marginTop: 2,
  },
});

export const ScaleValueMarker: React.FC<ScaleValueMarkerProps> = ({
  value,
  position,
}) => {
  return (
    <View
      style={[
        styles.markerContainer,
        {
          ...(position.left ? { left: position.left } : {}),
          top: position.top ?? DEFAULT_TOP_POSITION,
        },
      ]}
    >
      <Svg width={20} height={20} viewBox="0 0 20 20">
        <Path d="M10 0 V15" stroke="black" strokeWidth="1" />
      </Svg>
      <Text style={styles.valueText}>
        {typeof value === "number" ? value : "N/A"}
      </Text>
    </View>
  );
};
