import React from "react";
import { View, StyleSheet, Text, Svg, Path } from "@react-pdf/renderer";

interface ScaleValueMarkerProps {
  value: number;
  position: {
    left?: string;
    top?: number;
  };
}

const DEFAULT_TOP_POSITION = 30;

const styles = StyleSheet.create({
  markerContainer: {
    position: "absolute",
    alignItems: "center",
    transform: "translateX(-15px)",
  },
  valueText: {
    fontSize: 8,
  },
});

export const ScaleValueMarker: React.FC<ScaleValueMarkerProps> = ({
  value,
  position,
}) => {
  return (
    <View
      style={[
        styles.markerContainer,
        {
          ...(position.left ? { left: position.left } : {}),
          top: position.top ?? DEFAULT_TOP_POSITION,
        },
      ]}
    >
      <Svg width={30} height={30} viewBox="0 0 30 30">
        <Path d="M10 0 V20 H20" stroke="black" strokeWidth="1" />
      </Svg>
      <Text style={styles.valueText}>
        {typeof value === "number" ? value : "N/A"}
      </Text>
    </View>
  );
};
