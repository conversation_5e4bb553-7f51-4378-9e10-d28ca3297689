import { FilterAlt } from "react-basicons";
import styled from "styled-components";

const IconWrapper = styled.div`
	border: none;
	margin: 1px;
	padding: 1rem;
`;

function Filter({
	children,
	onClick,
}: {
	children: React.ReactNode;
	onClick: () => void;
}) {
	return (
		<>
			<IconWrapper onClick={onClick}>
				<FilterAlt size={20} />
			</IconWrapper>
			{children}
		</>
	);
}

export default Filter;
