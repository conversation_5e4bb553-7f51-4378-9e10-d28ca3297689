import speakeasy from "speakeasy";
import QRCode from "qrcode";
import { User } from "../@types";
import pool from "../db";
import Email from "./emailHandler";
import { generateCode } from "./generateOtp";
import AppError from "./appError";

interface TOTPSecretConfig {
  ascii: string;
  hex: string;
  base32: string;
  otpauth_url: string;
}

export async function generateTOTPSecret(
  email: string,
): Promise<TOTPSecretConfig> {
  try {
    console.log("Generating TOTP secret for:", email); // Debug log
    const secret = speakeasy.generateSecret({
      name: `Mopsus:${email}`,
      issuer: "Mopsus",
    });

    if (!secret.ascii || !secret.hex || !secret.base32 || !secret.otpauth_url) {
      throw new AppError("Failed to generate TOTP secret", 500, false);
    }

    console.log("TOTP secret generated successfully"); // Debug log
    return {
      ascii: secret.ascii,
      hex: secret.hex,
      base32: secret.base32,
      otpauth_url: secret.otpauth_url,
    };
  } catch (error) {
    console.error("Error generating TOTP secret:", error); // Error log
    throw new AppError("Error generating TOTP secret", 500, false);
  }
}

export async function generateQRCode(otpauthUrl: string): Promise<string> {
  try {
    console.log("Generating QR code for URL"); // Debug log
    const qrCode = await QRCode.toDataURL(otpauthUrl);
    console.log("QR code data URL preview:", qrCode.substring(0, 50) + "..."); // Debug log
    console.log("QR code generated successfully"); // Debug log
    return qrCode;
  } catch (error) {
    console.error("Error generating QR code:", error); // Error log
    throw new AppError("Error generating QR code", 500, false);
  }
}

export async function verifyTOTP(
  token: string,
  secret: string,
): Promise<boolean> {
  try {
    console.log("Verifying TOTP"); // Debug log
    const isValid = speakeasy.totp.verify({
      secret,
      encoding: "base32",
      token,
      window: 1, // Allow 30 seconds window
    });
    console.log("TOTP verification result:", isValid); // Debug log
    return isValid;
  } catch (error) {
    console.error("Error verifying TOTP:", error); // Error log
    throw new AppError("Error verifying TOTP", 500, false);
  }
}

export async function enable2FA(
  userId: string,
  type: "email" | "authenticator",
  totpSecret?: string,
): Promise<void> {
  try {
    console.log(`Enabling 2FA for user ${userId} with type ${type}`); // Debug log
    const result = await pool.query(
      `UPDATE users 
       SET two_factor_enabled = TRUE,
           two_factor_type = $1,
           totp_secret = $2
       WHERE user_id = $3
       RETURNING *`,
      [type, totpSecret || null, userId],
    );

    if (result.rowCount === 0) {
      throw new AppError("User not found", 404, true);
    }

    console.log("2FA enabled successfully"); // Debug log
  } catch (error: any) {
    console.error("Error enabling 2FA:", error); // Error log
    if (error instanceof AppError) throw error;
    throw new AppError(
      error.message || "Error enabling 2FA in database",
      500,
      false,
    );
  }
}

export async function disable2FA(userId: string): Promise<void> {
  try {
    const result = await pool.query(
      `UPDATE users 
       SET two_factor_enabled = FALSE,
           two_factor_type = NULL,
           totp_secret = NULL,
           otp = NULL,
           otp_expiration = CURRENT_TIMESTAMP
       WHERE user_id = $1
       RETURNING *`,
      [userId],
    );

    if (result.rowCount === 0) {
      throw new AppError("User not found", 404, true);
    }

    console.log("2FA disabled successfully"); // Debug log
  } catch (error: any) {
    console.error("Error disabling 2FA:", error); // Error log
    if (error instanceof AppError) throw error;
    throw new AppError(
      error.message || "Error disabling 2FA in database",
      500,
      false,
    );
  }
}

export async function send2FACode(user: User): Promise<void> {
  try {
    console.log(`Sending 2FA code to user ${user.user_id}`); // Debug log
    if (user.two_factor_type !== "email") {
      throw new AppError("Invalid 2FA type", 400, true);
    }

    const code = generateCode().toString(); // Ensure code is string
    console.log("Generated OTP code:", code); // Debug log

    const expiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
    const result = await pool.query(
      "UPDATE users SET otp = $1::text, otp_expiration = $2 WHERE user_id = $3 RETURNING *",
      [code, expiry, user.user_id],
    );

    if (result.rowCount === 0) {
      throw new AppError("User not found", 404, true);
    }

    // Verify the OTP was saved correctly
    const savedUser = result.rows[0];
    console.log("Saved OTP in database:", savedUser.otp); // Debug log

    if (savedUser.otp !== code) {
      throw new AppError("Failed to save OTP correctly", 500, false);
    }

    await new Email(user).send2FACode(code);
    console.log("2FA code sent successfully"); // Debug log
  } catch (error: any) {
    console.error("Error sending 2FA code:", error); // Error log
    if (error instanceof AppError) throw error;
    throw new AppError(error.message || "Error sending 2FA code", 500, false);
  }
}

export async function verify2FACode(
  userId: string,
  code: string,
  type: "email" | "authenticator",
): Promise<boolean> {
  try {
    console.log(`Verifying 2FA code for user ${userId} with type ${type}`); // Debug log
    const user = await pool.query<User>(
      "SELECT * FROM users WHERE user_id = $1",
      [userId],
    );

    if (user.rows.length === 0) {
      throw new AppError("User not found", 404, true);
    }
    if (type === "email") {
      if (user.rows[0].otp !== code) {
        console.log("Invalid email OTP code");
        throw new AppError("Invalid 2FA code", 401, true);
      }

      const now = new Date();
      const expiry = new Date(user.rows[0].otp_expiration!);
      if (now > expiry) {
        throw new AppError("Code expired", 400, true);
      }

      // Clear the OTP after successful verification
      await pool.query(
        "UPDATE users SET otp = NULL, otp_expiration = CURRENT_TIMESTAMP WHERE user_id = $1",
        [userId],
      );

      console.log("Email OTP verified successfully"); // Debug log
      return true;
    } else if (type === "authenticator") {
      if (!user.rows[0].totp_secret) {
        throw new AppError("TOTP secret not found", 400, true);
      }

      const isValid = await verifyTOTP(code, user.rows[0].totp_secret);
      if (!isValid) {
        console.log("Invalid TOTP code"); // Debug log
        throw new AppError("Invalid 2FA code", 401, true);
      }
      return true;
    }

    return false;
  } catch (error: any) {
    console.error("Error verifying 2FA code:", error); // Error log
    if (error instanceof AppError) throw error;
    throw new AppError(error.message || "Error verifying 2FA code", 500, false);
  }
}
