import React, { useEffect } from "react";
import Result, { ResultSummary } from "../components/Result";
import ResultSkeleton from "../components/ResultSkeleton";
import { useAppDispatch, useAppSelector } from "../../../../../redux/hooks";
import { useFormContext } from "../../components/FormContext";
import { Redirect, useHistory } from "react-router-dom";
import RedoButton from "../../components/RedoButtons";
import {
  clearSustainabilityResults,
  modifySustainabilityQueries,
} from "../../../../../redux/actions/sustainabilityActions";

type Props = {};

const SustainabilityResult = (props: Props) => {
  const { result, loading } = useAppSelector((state) => state.sustainability);
  const { buildingPhoto, map, setPostalAddress } = useFormContext();
  const history = useHistory();
  const dispatch = useAppDispatch();

  const clearResults = () => {
    dispatch(clearSustainabilityResults());
    history.push("/sustainability");
  };

  const modifyResults = () => {
    dispatch(modifySustainabilityQueries());
    history.push("/sustainability");
  };

  useEffect(() => {
    if (Object.keys(result).length > 0) {
      const propeties = result as any;
      setPostalAddress(
        `${propeties.address.post_code}-${propeties.address.house_number}-${
          propeties.address.addition ? propeties.address.addition : ""
        }`,
      );
    }
  }, [result, setPostalAddress]);

  if (Object.keys(result).length === 0 && !loading) {
    return (
      <Redirect
        to={{
          pathname: "/sustainability",
        }}
      />
    );
  }
  return (
    <>
      {loading ? (
        <ResultSkeleton />
      ) : (
        <>
          <ResultSummary
            property={result}
            buildingPhoto={buildingPhoto}
            map={map}
          />
          <Result property={result} buildingPhoto={buildingPhoto} />
          <RedoButton modify={modifyResults} clear={clearResults} />
        </>
      )}
    </>
  );
};

export default SustainabilityResult;
