image: atlassian/default-image:3
options:
  docker: true
  size: 2x

pipelines:
  cache:
    npm: client/node_modules
    server-npm: server/node_modules
  branches:
    master:
      - step:
          name: "Build and Push"
          caches:
            - docker
            - npm
            - server-npm
          services:
            - docker
          size: 2x
          script:
            # Publish auth container
            - echo "Deploying to Production"
            - sed -i "s|<IMAGE_NAME>|613421106348.dkr.ecr.eu-west-3.amazonaws.com/mopsus-plateform|g" Dockerrun.aws.json
            - docker build -t mopsus-plateform --build-arg EC2_PROD=$EC2_PROD --build-arg STAGE_PROD=$STAGE_PROD --build-arg X_API_KEY=$X_API_KEY --build-arg PK_LIVE=$PK_LIVE --build-arg GOOGLE_STREETVIEW_APIKEY=$GOOGLE_STREETVIEW_APIKEY --build-arg GOOGLE_TRACKING_ID=$GOOGLE_TRACKING_ID -f Dockerfile .

            #Push to AWS Container Registry
            - pipe: atlassian/aws-ecr-push-image:1.6.2
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                IMAGE_NAME: mopsus-plateform
            - zip mopsus.zip Dockerrun.aws.json
          artifacts:
            - mopsus.zip
      - step:
          name: "Deploy to production"
          deployment: production
          trigger: manual
          script:
            - pipe: atlassian/aws-elasticbeanstalk-deploy:1.0.2
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: "eu-west-3"
                APPLICATION_NAME: "mopsus"
                ENVIRONMENT_NAME: "Mopsus-prod"
                S3_BUCKET: "elasticbeanstalk-eu-west-3-613421106348"
                ZIP_FILE: "mopsus.zip"

    staging:
      - step:
          caches:
            - docker
          services:
            - docker
          size: 2x
          script:
            # Publish auth container
            - echo "Deploying to Staging"
            - sed -i "s|<IMAGE_NAME>|613421106348.dkr.ecr.eu-west-3.amazonaws.com/mopsus-staging|g" Dockerrun.aws.json
            - docker build -t mopsus-staging --build-arg EC2_STAGE=$EC2_STAGE --build-arg TEST=$TEST --build-arg X_API_KEY=$X_API_KEY --build-arg PK_TEST=$PK_TEST --build-arg GOOGLE_STREETVIEW_APIKEY=$GOOGLE_STREETVIEW_APIKEY --build-arg GOOGLE_TRACKING_ID=$GOOGLE_TRACKING_ID -f Dockerfile.stage .

            #Push to AWS Container Registry
            - pipe: atlassian/aws-ecr-push-image:1.6.2
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                IMAGE_NAME: mopsus-staging
            - zip mopsus.zip Dockerrun.aws.json
          artifacts:
            - mopsus.zip
      - step:
          name: "Deploy to stage"
          deployment: staging
          script:
            - pipe: atlassian/aws-elasticbeanstalk-deploy:1.0.2
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: "eu-west-3"
                APPLICATION_NAME: "mopsus-test"
                ENVIRONMENT_NAME: "Mopsus-test-env"
                S3_BUCKET: "elasticbeanstalk-eu-west-3-613421106348"
                ZIP_FILE: "mopsus.zip"
definitions:
  services:
    docker:
      memory: 7128
  caches:
    npm: client/node_modules
    server-npm: server/node_modules
