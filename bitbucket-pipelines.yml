image: atlassian/default-image:3
options:
  docker: true
  size: 2x

pipelines:
  branches:
    master:
      - step:
          name: "Security Scanning - SAST"
          caches:
            - npm
            - server-npm
          size: 2x
          script:
            # Install dependencies for both client and server
            - echo "Installing dependencies for security scanning..."
            - cd client && npm install --legacy-peer-deps
            - cd ../server && npm install
            - cd ..

            # Install security scanning tools
            - npm install -g @microsoft/eslint-plugin-sdl
            - npm install -g retire
            - npm install -g audit-ci

            # Run dependency vulnerability scanning
            - echo "Running dependency vulnerability scans..."
            - cd client && npm audit --audit-level=moderate || true
            - npx audit-ci --moderate || true
            - npx retire --outputformat json --outputpath client-retire-report.json . || true
            - cd ../server && npm audit --audit-level=moderate || true
            - npx audit-ci --moderate || true
            - npx retire --outputformat json --outputpath server-retire-report.json . || true
            - cd ..

            # Run Semgrep SAST scanning using Docker
            - echo "Running Semgrep SAST scanning..."
            - docker run --rm -v $(pwd):/src returntocorp/semgrep:latest --config=auto --json --output=/src/semgrep-auto-report.json /src || true
            - docker run --rm -v $(pwd):/src returntocorp/semgrep:latest --config=p/owasp-top-ten --json --output=/src/semgrep-owasp-report.json /src || true
            - docker run --rm -v $(pwd):/src returntocorp/semgrep:latest --config=/src/.semgrep.yml --json --output=/src/semgrep-custom-report.json /src || true

            # Run ESLint with security rules
            - echo "Running ESLint security scanning..."
            - cd client && npx eslint . --ext .ts,.tsx --format json --output-file ../eslint-client-report.json || true
            - cd ../server && npx eslint . --ext .ts --format json --output-file ../eslint-server-report.json || true
            - cd ..

          artifacts:
            - "*-report.json"

      - step:
          name: "Security Scanning - Container"
          caches:
            - docker
          services:
            - docker
          size: 2x
          script:
            # Build Docker image for scanning
            - echo "Building Docker image for security scanning..."
            - docker build -t mopsus-security-scan --build-arg EC2_PROD=$EC2_PROD --build-arg STAGE_PROD=$STAGE_PROD --build-arg X_API_KEY=$X_API_KEY --build-arg PK_LIVE=$PK_LIVE --build-arg GOOGLE_STREETVIEW_APIKEY=$GOOGLE_STREETVIEW_APIKEY --build-arg GOOGLE_TRACKING_ID=$GOOGLE_TRACKING_ID -f Dockerfile .

            # Install and run Trivy for container scanning
            - echo "Installing Trivy for container vulnerability scanning..."
            - wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | apt-key add -
            - echo "deb https://aquasecurity.github.io/trivy-repo/deb $(lsb_release -sc) main" | tee -a /etc/apt/sources.list.d/trivy.list
            - apt-get update && apt-get install -y trivy

            # Run Trivy container scan
            - echo "Running Trivy container security scan..."
            - trivy image --format json --output trivy-report.json mopsus-security-scan || true

            # Run Docker Bench Security (if applicable)
            - echo "Running Docker security best practices check..."
            - docker run --rm --net host --pid host --userns host --cap-add audit_control \
              -e DOCKER_CONTENT_TRUST=$DOCKER_CONTENT_TRUST \
              -v /etc:/etc:ro \
              -v /usr/bin/containerd:/usr/bin/containerd:ro \
              -v /usr/bin/runc:/usr/bin/runc:ro \
              -v /usr/lib/systemd:/usr/lib/systemd:ro \
              -v /var/lib:/var/lib:ro \
              -v /var/run/docker.sock:/var/run/docker.sock:ro \
              --label docker_bench_security \
              docker/docker-bench-security:latest > docker-bench-report.txt || true

          artifacts:
            - "trivy-report.json"
            - "docker-bench-report.txt"

      - step:
          name: "Build and Push"
          caches:
            - docker
            - npm
            - server-npm
          services:
            - docker
          size: 2x
          script:
            # Publish auth container
            - echo "Deploying to Production"
            - sed -i "s|<IMAGE_NAME>|613421106348.dkr.ecr.eu-west-3.amazonaws.com/mopsus-plateform|g" Dockerrun.aws.json
            - docker build -t mopsus-plateform --build-arg EC2_PROD=$EC2_PROD --build-arg STAGE_PROD=$STAGE_PROD --build-arg X_API_KEY=$X_API_KEY --build-arg PK_LIVE=$PK_LIVE --build-arg GOOGLE_STREETVIEW_APIKEY=$GOOGLE_STREETVIEW_APIKEY --build-arg GOOGLE_TRACKING_ID=$GOOGLE_TRACKING_ID -f Dockerfile .

            #Push to AWS Container Registry
            - pipe: atlassian/aws-ecr-push-image:1.6.2
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                IMAGE_NAME: mopsus-plateform
            - zip mopsus.zip Dockerrun.aws.json
          artifacts:
            - mopsus.zip
      - step:
          name: "Deploy to production"
          deployment: production
          trigger: manual
          script:
            - pipe: atlassian/aws-elasticbeanstalk-deploy:1.0.2
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: "eu-west-3"
                APPLICATION_NAME: "mopsus"
                ENVIRONMENT_NAME: "Mopsus-prod"
                S3_BUCKET: "elasticbeanstalk-eu-west-3-613421106348"
                ZIP_FILE: "mopsus.zip"

    staging:
      - step:
          name: "Security Scanning - SAST (Staging)"
          caches:
            - npm
            - server-npm
          size: 2x
          script:
            # Install dependencies for both client and server
            - echo "Installing dependencies for security scanning..."
            - cd client && npm install --legacy-peer-deps
            - cd ../server && npm install
            - cd ..

            # Install security scanning tools
            - npm install -g @microsoft/eslint-plugin-sdl
            - npm install -g retire
            - npm install -g audit-ci

            # Run dependency vulnerability scanning
            - echo "Running dependency vulnerability scans..."
            - cd client && npm audit --audit-level=moderate || true
            - npx audit-ci --moderate || true
            - npx retire --outputformat json --outputpath client-retire-report.json . || true
            - cd ../server && npm audit --audit-level=moderate || true
            - npx audit-ci --moderate || true
            - npx retire --outputformat json --outputpath server-retire-report.json . || true
            - cd ..

            # Run Semgrep SAST scanning using Docker
            - echo "Running Semgrep SAST scanning..."
            - docker run --rm -v $(pwd):/src returntocorp/semgrep:latest --config=auto --json --output=/src/semgrep-auto-report.json /src || true
            - docker run --rm -v $(pwd):/src returntocorp/semgrep:latest --config=p/owasp-top-ten --json --output=/src/semgrep-owasp-report.json /src || true

            # Run ESLint with security rules
            - echo "Running ESLint security scanning..."
            - cd client && npx eslint . --ext .ts,.tsx --format json --output-file ../eslint-client-report.json || true
            - cd ../server && npx eslint . --ext .ts --format json --output-file ../eslint-server-report.json || true
            - cd ..

          artifacts:
            - "*-report.json"

      - step:
          name: "Build and Push (Staging)"
          caches:
            - docker
          services:
            - docker
          size: 2x
          script:
            # Publish auth container
            - echo "Deploying to Staging"
            - sed -i "s|<IMAGE_NAME>|613421106348.dkr.ecr.eu-west-3.amazonaws.com/mopsus-staging|g" Dockerrun.aws.json
            - docker build -t mopsus-staging --build-arg EC2_STAGE=$EC2_STAGE --build-arg TEST=$TEST --build-arg X_API_KEY=$X_API_KEY --build-arg PK_TEST=$PK_TEST --build-arg GOOGLE_STREETVIEW_APIKEY=$GOOGLE_STREETVIEW_APIKEY --build-arg GOOGLE_TRACKING_ID=$GOOGLE_TRACKING_ID -f Dockerfile.stage .

            #Push to AWS Container Registry
            - pipe: atlassian/aws-ecr-push-image:1.6.2
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                IMAGE_NAME: mopsus-staging
            - zip mopsus.zip Dockerrun.aws.json
          artifacts:
            - mopsus.zip
      - step:
          name: "Deploy to stage"
          deployment: staging
          script:
            - pipe: atlassian/aws-elasticbeanstalk-deploy:1.0.2
              variables:
                AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                AWS_DEFAULT_REGION: "eu-west-3"
                APPLICATION_NAME: "mopsus-test"
                ENVIRONMENT_NAME: "Mopsus-test-env"
                S3_BUCKET: "elasticbeanstalk-eu-west-3-613421106348"
                ZIP_FILE: "mopsus.zip"

  pull-requests:
    "**":
      - step:
          name: "Security Scanning - Pull Request"
          caches:
            - npm
            - server-npm
          size: 2x
          script:
            # Install dependencies for both client and server
            - echo "Installing dependencies for security scanning..."
            - cd client && npm install --legacy-peer-deps
            - cd ../server && npm install
            - cd ..

            # Install security scanning tools
            - npm install -g @microsoft/eslint-plugin-sdl
            - npm install -g retire
            - npm install -g audit-ci

            # Run dependency vulnerability scanning
            - echo "Running dependency vulnerability scans..."
            - cd client && npm audit --audit-level=moderate || true
            - npx audit-ci --moderate || true
            - npx retire --outputformat json --outputpath client-retire-report.json . || true
            - cd ../server && npm audit --audit-level=moderate || true
            - npx audit-ci --moderate || true
            - npx retire --outputformat json --outputpath server-retire-report.json . || true
            - cd ..

            # Run Semgrep SAST scanning using Docker
            - echo "Running Semgrep SAST scanning..."
            - docker run --rm -v $(pwd):/src returntocorp/semgrep:latest --config=auto --json --output=/src/semgrep-auto-report.json /src || true
            - docker run --rm -v $(pwd):/src returntocorp/semgrep:latest --config=p/owasp-top-ten --json --output=/src/semgrep-owasp-report.json /src || true

            # Run ESLint with security rules
            - echo "Running ESLint security scanning..."
            - cd client && npx eslint . --ext .ts,.tsx --format json --output-file ../eslint-client-report.json || true
            - cd ../server && npx eslint . --ext .ts --format json --output-file ../eslint-server-report.json || true
            - cd ..

            # Generate security summary
            - echo "Generating security scan summary..."
            - echo "Security scan completed. Check artifacts for detailed reports." > security-summary.txt
            - echo "Reports generated:" >> security-summary.txt
            - echo "- Dependency vulnerabilities (npm audit + retire.js)" >> security-summary.txt
            - echo "- SAST findings (Semgrep)" >> security-summary.txt
            - echo "- Code quality and security (ESLint)" >> security-summary.txt

          artifacts:
            - "*-report.json"
            - "security-summary.txt"

      # Post-deployment security validation
      - step:
          name: "Security Headers Validation"
          trigger: manual
          script:
            - echo "Validating security headers on deployed application..."

            # Test staging environment security headers
            - chmod +x scripts/security-headers-test.sh
            - ./scripts/security-headers-test.sh https://mopsus-test.altum.ai

            # Run DAST scan on staging
            - chmod +x scripts/dast-scan.sh
            - ./scripts/dast-scan.sh https://mopsus-test.altum.ai

            # Generate combined security validation report
            - echo "Post-deployment security validation completed" > security-validation-summary.txt
            - echo "Tests performed:" >> security-validation-summary.txt
            - echo "- Security headers validation" >> security-validation-summary.txt
            - echo "- DAST scanning with OWASP ZAP" >> security-validation-summary.txt
            - echo "- SSL/TLS configuration check" >> security-validation-summary.txt

          artifacts:
            - "security-reports/*"
            - "security-validation-summary.txt"

definitions:
  services:
    docker:
      memory: 7128
  caches:
    npm: client/node_modules
    server-npm: server/node_modules
