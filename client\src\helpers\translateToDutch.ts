const translateToDutch = (name: string): string => {
  const translations: Record<string, string> = {
    indoor: 'Binnen',
    outdoor: 'Buiten',
    living_room: 'Huiskamer',
    kitchen: '<PERSON>uke<PERSON>',
    bathroom: '<PERSON><PERSON><PERSON>',
    bedroom: '<PERSON><PERSON><PERSON>ka<PERSON>',
    other_room: '<PERSON><PERSON> kamers',
    other: '<PERSON><PERSON>',
    hall_corridor: 'Hal gang',
    room: '<PERSON><PERSON>',
    scullery: 'B<PERSON>jk<PERSON><PERSON>',
    facade: 'Facade',
    view: 'Weergave',
    street: 'Straat',
    building_part: 'Gebouw deel',
    no_building: 'Geen gebouw',
    'balcony-garden': 'Balkon Tuin',
  };

  return translations[name] ?? 'Ander';
};
export default translateToDutch;
