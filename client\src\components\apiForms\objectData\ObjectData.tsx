import React from "react";
import { Redirect } from "react-router";
import { useAppSelector } from "../../../redux/hooks";
import Loading from "../../Loading";
import ObjectDataForm from "./ObjectDataForm";
import { FormProvider } from "../../../pages/Api/usage-pages/components/FormContext";
const ObjectData = () => {
  const { loading, result } = useAppSelector((state) => state.objectData);

  if (loading) {
    return <Loading />;
  }
  return (
    <FormProvider>
      {Object.keys(result).length > 0 ? (
        <Redirect
          to={{
            pathname: "/objectdata-result",
          }}
        />
      ) : (
        <ObjectDataForm />
      )}
    </FormProvider>
  );
};

export default ObjectData;
