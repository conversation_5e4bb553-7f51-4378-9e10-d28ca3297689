import axios from "axios";

export const sendToSlack = async (
  email: string,
  responses: {
    question: string | undefined;
    answers: string;
  }[],
) => {
  const webhookUrl = process.env.REACT_APP_SLACK_WEBHOOK_URL;

  if (!webhookUrl) {
    console.warn(
      "REACT_APP_SLACK_WEBHOOK_URL is not set. Skipping Slack notification.",
    );
    return;
  }

  try {
    const slackMessage = {
      text: `New submission from ${email}`,
      attachments: [
        {
          color: "#36a64f", // You can use a good color for the attachment
          author_name: email,
          title: "New Onboarding Responses",
          text: `Email: ${email}`,
          fields: responses.map((response) => ({
            title: response.question,
            value: response.answers,
            short: false,
          })),
        },
      ],
    };
    await axios.post(webhookUrl, JSON.stringify(slackMessage));
  } catch (error) {
    console.error("error", error);
  }
};
