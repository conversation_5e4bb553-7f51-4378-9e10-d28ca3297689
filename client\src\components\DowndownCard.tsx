import React from "react";
import {
  ArrowDropdownCaretSortSelectArrow,
  ArrowUpPushupCaretSortSelectArrow,
} from "react-basicons";
import styled from "styled-components";
import { Title } from "../styles/styled/Common.styled";
import { Colors } from "../styles/Theme";
import { useAppDispatch } from "../redux/hooks";
import {
  getOnboardingState,
  setAllOnboardingState,
} from "../redux/actions/onboardingActions";
import ProgressBarComponent from "./Progress";

const CardContainer = styled.div<{ isVisible: boolean }>`
  display: flex;
  flex-direction: column;
  padding-top: 16px;
  padding-bottom: 10px;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 4px 8px rgba(185, 188, 201, 0.16);
  cursor: pointer;
  transition: all 0.5s ease-in;
  padding: 16px;

  overflow: hidden;
  margin: 0 0 1rem 0;
`;

const CardSection = styled.div`
  display: flex;
  justify-content: space-between;
`;

const TextBase = styled.h4`
  font-style: italic;
  color: ${Colors.main.green};
  margin: 0 1rem 0 0;
`;

const UlContainer = styled.ul<{ isVisible: boolean }>`
  list-style: none;
  border-color: grey;
  flex-direction: column;
  display: "flex";
  max-height: ${(props) => (props.isVisible ? "100vh" : "0vh")};
  transition: all 0.5s ease-in;
  margin-top: 10px;
  overflow: hidden;
  text-align: right;
`;

interface DropdownCardProps {
  title: string;
  children: React.ReactNode;
  isOpen: boolean;
  setIsOpen: (value: boolean) => void;
  percentage: number;
}

const DropdownCard: React.FC<DropdownCardProps> = ({
  title,
  children,
  setIsOpen,
  isOpen,
  percentage,
}) => {
  const toggleOpen = () => setIsOpen(!isOpen);
  const dispatch = useAppDispatch();
  return (
    <CardContainer isVisible={isOpen}>
      <CardSection onClick={toggleOpen}>
        <Title>{title}</Title>
        {isOpen ? (
          <ArrowUpPushupCaretSortSelectArrow />
        ) : (
          <ArrowDropdownCaretSortSelectArrow />
        )}
      </CardSection>
      <ProgressBarComponent label={title} percentage={percentage} />

      <UlContainer isVisible={isOpen}>
        {children}

        <TextBase
          onClick={() =>
            dispatch(setAllOnboardingState()).then(() =>
              dispatch(getOnboardingState()),
            )
          }
        >
          'Aan de slag' niet meer weergeven
        </TextBase>
      </UlContainer>
    </CardContainer>
  );
};

export default DropdownCard;
