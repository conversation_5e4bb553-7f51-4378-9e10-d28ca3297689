import Text from "../../../../../components/Text";

type Props = {
  legendKey: any[];
};
const COLORS = ["#EC9E47", "#D2DE80", "#E0E0E0", "#FF8042", "#27AE60"];
const ErrorLegend = (props: Props) => {
  return (
    <div className="flex max-w-[100%] flex-wrap justify-center gap-2 self-end lg:max-w-[95%] ">
      {props.legendKey.map((key, index) => (
        <div className="flex items-center gap-2">
          <div
            className={`w-4 h-2 rounded-2xl`}
            style={{
              backgroundColor: COLORS[index % COLORS.length],
            }}
          ></div>
          <Text>{key.name}</Text>
        </div>
      ))}
    </div>
  );
};

export default ErrorLegend;
